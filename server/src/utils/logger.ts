import { createLogger, format, transports } from 'winston';
import { consoleFormat/*, ConsoleFormat */ } from 'winston-console-format';
// const util = require('util');

// const colors = require('colors/safe');
// const { MESSAGE } = require('triple-beam');
//
// class MyConsoleFormat extends ConsoleFormat {
//   constructor(opts) {
//     super(opts);
//   }
//
//   message(info, chr, color) {
//     const message = info.message.replace(
//       ConsoleFormat.reSpacesOrEmpty,
//       `$1${color}${colors.dim(chr)}${colors.reset(" ")}`
//     )
//
//     let date = new Date(info.timestamp).toLocaleString()
//     return `${info.level}[${color}${colors.dim(date)}${colors.reset(']')}:${message}`
//   }
//
//   write(info, messages, color) {
//     const pad = this.pad(info.message)
//
//     messages.forEach((line, index, arr) => {
//       const lineNumber = colors.dim(`[${(index + 1).toString().padStart(arr.length.toString().length, " ")}]`)
//       let chr = ConsoleFormat.chars.line
//       if (index === arr.length - 1) {
//         chr = ConsoleFormat.chars.endLine
//       }
//
//       let date = new Date(info.timestamp).toLocaleString()
//       info[MESSAGE] += `\n${colors.dim(info.level)}[${color}${colors.dim(date)}${colors.reset(']')}:${pad}${color}${colors.dim(chr)}${colors.reset(" ")}`
//       info[MESSAGE] += `${lineNumber} ${line}`
//     })
//   }
// }
//
// const consoleFormat = (opts) => {
//   return new MyConsoleFormat(opts)
// }

// Configure the Winston logger. For the complete documentation see https://github.com/winstonjs/winston
export const Logger = createLogger({
  level: 'silly',
  format: format.combine(
    // format.label({ label: 'CUSTOM', message: true }),
    format.timestamp(),
    format.ms(),
    format.errors({ stack: true }),
    format.splat(),
    // formaton()
  ),
  defaultMeta: { service: 'Test' },
  transports: [
    new transports.Console({
      format: format.combine(
        format.colorize({ all: true }),
        format.padLevels(),
        consoleFormat({
          showMeta: true,
          metaStrip: ['service'],
          inspectOptions: {
            depth: 5,
            colors: true,
            maxArrayLength: Infinity,
            breakLength: 120,
            compact: Infinity
          }
        })
      )
    })
  ]
});

export default Logger;



