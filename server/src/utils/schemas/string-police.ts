const trimItOnce = (val:string):string => {
    return val?.trim() || '';
}

export const trimIt = (val:string|Array<string>):string|Array<string> => {
    return Array.isArray(val) ? val.map(a => trimItOnce(a)) : trimItOnce(val);
}

const lowercaseItOnce = (val:string):string => {
    return val?.toLowerCase() || '';
}

export const lowercaseIt = (val:string|Array<string>):string|Array<string> => {
    return Array.isArray(val) ? val.map(a => lowercaseItOnce(a)) : lowercaseItOnce(val);
}

const enumItOnce = (val:string, list:Array<string>, throwErr?:boolean):string => {
    const v = list.includes(val);
    if(!v && throwErr) throw new Error(`${val} not included in allowable value list ${list.join(', ')}`);
    else return v ? val : '';
};

export const enumIt = (val:string|Array<string>, list:Array<string>, throwErr?:boolean):string|Array<string>|undefined => {
    return Array.isArray(val) ? val.filter(a => !!enumItOnce(a, list, throwErr)) : enumItOnce(val, list, throwErr) || undefined;
}
