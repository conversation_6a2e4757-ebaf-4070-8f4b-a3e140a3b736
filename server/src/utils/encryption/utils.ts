import {generateKeyPairSync} from 'crypto';
import * as tweetnacl from 'tweetnacl';
export const BASE58_DID_PREFIX = "did:key:p"
import * as uint8arrays from 'uint8arrays';


// source path oddjs/components/crypto/implementation
export type VerifyArgs = {
    message: Uint8Array
    publicKey: Uint8Array
    signature: Uint8Array
}

export const equal = (aBuf: ArrayBuffer, bBuf: ArrayBuffer): boolean => {
    const a = new Uint8Array(aBuf)
    const b = new Uint8Array(bBuf)
    if (a.length !== b.length) return false
    for (let i = 0; i < a.length; i++) {
        if (a[i] !== b[i]) return false
    }
    return true
}

export const genEd255519KeyPair = () => generateKeyPairSync('ed25519',
    {
        privateKeyEncoding: {format: 'pem', type: 'pkcs8'},
        publicKeyEncoding: {format: 'pem', type: 'spki'}
    });

export async function ed25519Verify({message, publicKey, signature}: VerifyArgs): Promise<boolean> {
    return tweetnacl.sign.detached.verify(message, signature, publicKey)
}


export const cryptoMod = {
    did: {
        keyTypes: {
            "bls12-381": {
                magicBytes: new Uint8Array([0xea, 0x01]),
                verify: () => {
                    throw new Error("Not implemented")
                },
            },
            "ed25519": {
                magicBytes: new Uint8Array([0xed, 0x01]),
                verify: ed25519Verify,
            }
        }
    }
}

type Crypto = {
    did: {
        keyTypes: {
            [key: string]: {
                magicBytes: Uint8Array
                verify: (args: VerifyArgs) => Promise<boolean>
            }
        }
    }
}
/**
 * Convert a base64 public key to a DID (did:key).
 */
export function publicKeyToDid(
    crypto: Crypto = cryptoMod,
    publicKey: Uint8Array,
    keyType: string
): string {
    // Prefix public-write key
    const prefix = crypto.did.keyTypes[keyType]?.magicBytes
    if (prefix === null) {
        throw new Error(`Key type '${keyType}' not supported, available types: ${Object.keys(crypto.did.keyTypes).join(", ")}`)
    }

    const prefixedBuf = uint8arrays.concat([prefix, publicKey])

    // Encode prefixed
    return BASE58_DID_PREFIX + uint8arrays.toString(prefixedBuf, "base58btc")
}

// export const hasPrefix = (prefixedKey: ArrayBuffer, prefix: ArrayBuffer): boolean => {
//     return equal(prefix, prefixedKey.slice(0, prefix.byteLength))
// }

// /**
//  * Convert a DID (did:key) to a base64 public key.
//  */
// export function didToPublicKey(crypto: Crypto, did: string): {
//     publicKey: Uint8Array
//     type: string
// } {
//     if (!did.startsWith(BASE58_DID_PREFIX)) {
//         throw new Error("Please use a base58-encoded DID formatted `did:key:p...`")
//     }
//
//     const didWithoutPrefix = did.substr(BASE58_DID_PREFIX.length)
//     const magicalBuf = uint8arrays.fromString(didWithoutPrefix, "base58btc")
//     const result = Object.entries(crypto.did.keyTypes).find(
//         ([_key, attr]) => hasPrefix(magicalBuf as any, attr.magicBytes as any)
//     )
//
//     if (!result) {
//         throw new Error("Unsupported key algorithm.")
//     }
//
//     return {
//         publicKey: magicalBuf.slice(result[1].magicBytes.length),
//         type: result[0]
//     }
// }

/**
 * Check if a buffer has the given prefix.
 */
export const hasPrefix = (buffer: Uint8Array, prefix: Uint8Array): boolean => {
    if (buffer.length < prefix.length) {
        return false;
    }
    return prefix.every((byte, index) => buffer[index] === byte);
}
/**
 * Convert a DID (did:key) to a base64 public key.
 */
export function didToPublicKey(crypto: Crypto, did: string): {
    publicKey: Uint8Array;
    type: string;
} {
    if (!did.startsWith(BASE58_DID_PREFIX)) {
        throw new Error("Please use a base58-encoded DID formatted as `did:key:p...`");
    }

    // Remove the DID prefix
    const didWithoutPrefix = did.substring(BASE58_DID_PREFIX.length);

    // Convert base58-encoded string to Uint8Array
    const magicalBuf = uint8arrays.fromString(didWithoutPrefix, 'base58btc');

    // Find the key type by matching the magic bytes prefix
    const result = Object.entries(crypto.did.keyTypes).find(([_, attr]) =>
        hasPrefix(magicalBuf, attr.magicBytes)
    );

    if (!result) {
        throw new Error("Unsupported key algorithm.");
    }

    // Slice the magic bytes off to get the public key
    const [type, { magicBytes }] = result;

    return {
        publicKey: magicalBuf.slice(magicBytes.length),
        type,
    };
}

