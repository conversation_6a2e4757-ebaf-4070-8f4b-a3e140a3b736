import { encrypt, decrypt, PrivateKey } from 'eciesjs'
import {fromString} from 'uint8arrays';

export const getPublicKey = (privateKey:string) => {
    const sk = PrivateKey.fromHex(privateKey);
    return sk.publicKey.toHex();
}

export const decryptFromString = (privateKey:string, message:string):Buffer => {
    const sk = PrivateKey.fromHex(privateKey);
    return decrypt(sk.secret, fromString(message))
}


