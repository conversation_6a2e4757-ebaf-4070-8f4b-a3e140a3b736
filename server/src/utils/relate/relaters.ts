/* eslint-disable no-unused-vars */
import {HookContext} from "../../declarations.js";
import {Hook, NullableId} from "@feathersjs/feathers";

import beginTransaction from './beginTransaction.js';
import {FeathersError/*, GeneralError*/} from '@feathersjs/errors';
import HookSystem from './HookSystem.js';
import {_get, _set, _isnil, _isequal, _unset, stringifyPath} from 'symbol-ucan';
import {AnyObj, CoreCall, setExists} from 'feathers-ucan';

type ID = NullableId;

const TransactionManager = {
    commitTransaction: async (context: HookContext) => {
        return await context.params.mongoParams?.session?.commitTransaction()
    },
    abortTransaction: async (context: HookContext) => {
        return await context.params.mongoParams?.session?.abortTransaction()
    }
}

import checkContext from './checkContext.js';
import {loadExists} from 'feathers-ucan';

function isEmpty(obj: any): number | boolean {
    return Array.isArray(obj) ? obj.length : typeof obj === 'object' ?
        (!!obj // 👈 null and undefined check
            && Object.keys(obj).length === 0
            && Object.getPrototypeOf(obj) === Object.prototype) : !!obj;
}

class relateOtoError extends FeathersError {
    constructor(message: string, data: object | string) {
        super(message, 'relateOto', 520, 'relateOtoError', data);
    }
}

class removeOtoError extends FeathersError {
    constructor(message: string, data: object | string) {
        super(message, 'removeOto', 521, 'removeOtoError', data);
    }
}

class relateMtmError extends FeathersError {
    constructor(message: string, data: object | string) {
        super(message, 'relateMtm', 522, 'relateMtmError', data);
    }
}

class removeMtmError extends FeathersError {
    constructor(message: string, data: object | string) {
        super(message, 'removeMtm', 523, 'removeMtmError', data);
    }
}

class relateOtmError extends FeathersError {
    constructor(message: string, data: object | string) {
        super(message, 'relateOtm', 524, 'relateOtmError', data);
    }
}

class removeOtmError extends FeathersError {
    constructor(message: string, data: object | string) {
        super(message, 'removeOtm', 525, 'removeOtmError', data);
    }
}

const coreOptions = {
    skipJoins: true,
    admin_pass: true
}

export type RelateParams = {
    idPath?: string,
    dataPath?: string,
    thereService: string,
    herePath: string | Array<string>,
    therePath: string | Array<string>,
    paramsName?: string,
    addParams?: object,
    passParams?: boolean,
    beforeHooks?: Hook[],
    afterHooks?: Hook[],
    errorHooks?: Hook[],
    fkAction?: 'protect' | 'replace' | 'null' | 'unset' | 'cascade'
};


export const relateOto = (
    {
        idPath = '_id',
        dataPath = 'result',
        thereService,
        herePath,
        therePath,
        paramsName = `${thereService}_${stringifyPath(therePath)}_${stringifyPath(herePath)}`,
        addParams = {},
        passParams = false,
        beforeHooks = [],
        afterHooks = [],
        errorHooks = [],
    }: RelateParams): (context: HookContext) => Promise<HookContext> => {
    let hookManagerOto = new HookSystem({
        hook_store: {
            before: beforeHooks,
            after: afterHooks,
            error: errorHooks,
        },
    });
    return async (context: HookContext): Promise<HookContext> => {
        checkContext(context, ['before', 'after'], ['create', 'update', 'patch'], 'relateOto');

        try {
            if (context.type === 'before') {
                let skipPath = _get(context, 'params.skipPath', []) as string | string[] | undefined;
                let startTransactionOptions = _get(context, 'params.startTransactionOptions', {}) as object | undefined;
                await beginTransaction(context, {skipPath, startTransactionOptions});
            }

            if (context.type === 'before' && context.method !== 'create') {
                let id = _get(context, 'id');
                // if (_isnil(id)) id = _get(context, `params.query.${idPath}`);
                if (!_isnil(id)) {
                    const relateOto_res_before = await loadExists(context);
                    context = setExists(context, relateOto_res_before)
                    context.params = _set(context.params, ['relateOto', paramsName, 'relateOto_res_before'], relateOto_res_before);
                }
            } else if (context.type === 'after') {
                const tp = Array.isArray(therePath) ? therePath : [therePath];
                const hereData = _get(context, dataPath);
                const fkId = _get(hereData, herePath);
                let oldFkId;
                const id = _get(hereData, idPath, context.id);

                const notEmpty = !Array.isArray(fkId) ? !!fkId : !!fkId.length;
                // let runRelate = !_isnil(fkId) && !['{}', '[]'].includes(stringifyPath(fkId));
                let runRelate = !_isnil(fkId) && notEmpty;

                if (runRelate && context.method !== 'create') {
                    oldFkId = _get(context, `params.relateOto.${paramsName}.relateOto_res_before.${stringifyPath(herePath)}`);
                    if (_isnil(_get(context, `params.relateOto.${paramsName}.relateOto_res_before`))) {
                        runRelate = false;
                    } else {
                        runRelate = !_isnil(oldFkId) && !_isequal(oldFkId, fkId);
                    }
                }

                if (runRelate) {
                    let patchObj = {};
                    patchObj = _set(patchObj, tp, id);

                    const relateParams = {
                        paramsName,
                        idPath,
                        dataPath,
                        thereService,
                        herePath,
                        therePath: tp,
                        passParams,
                        beforeHooks,
                        afterHooks,
                        errorHooks,
                    };

                    context.params = _set(context.params, ['relateOto', paramsName, 'fkId'], fkId);
                    context.params = _set(context.params, ['relateOto', paramsName, 'oldFkId'], oldFkId);
                    context.params = _set(context.params, ['relateOto', paramsName, 'patchObj'], patchObj);
                    context.params = _set(context.params, ['relateOto', paramsName, 'relateParams'], relateParams);

                    let params = {core: {...context.params.core, skipJoins: true}, ...addParams};
                    if (passParams) {
                        params = {
                            ..._unset(context.params, ['provider', 'query']),
                            ...{core: {...context.params.core, skipJoins: true}, ...addParams},
                        };
                    }

                    context.params = _set(context.params, ['relateMtm', paramsName, 'params'], params);

                    await hookManagerOto.call({path: 'before'}, context, {paramsName});

                    const customParams = _get(context, ['params', 'relateMtm', paramsName, 'params'], params);


                    const relateOto_res = await new CoreCall(thereService, context, coreOptions).patch(
                        _get(context, ['params', 'relateOto', paramsName, 'fkId'], fkId) as NullableId,
                        _get(context, ['params', 'relateOto', paramsName, 'patchObj'], patchObj) as AnyObj,
                        {...customParams, relate_hook: 'relateOto', disableSoftDelete: true}
                    )
                    context.params = _set(context.params, ['relateOto', paramsName, 'relateOto_res'], relateOto_res);

                    await hookManagerOto.call({path: 'after'}, context, {paramsName});

                    if (context.method !== 'create') {
                        let oldFkPayload = {};
                        let newOldFk = _get(context, 'params.replaceId');
                        if (newOldFk) {
                            oldFkPayload = _set({}, tp, newOldFk);
                        } else {
                            oldFkPayload = _set({}, ['$unset', ...tp], 1);
                        }
                        await new CoreCall(thereService, context, coreOptions).patch(oldFkId as any, oldFkPayload, {
                            ...customParams,
                            ...{
                                relate_hook: 'relateOto',
                                disableSoftDelete: true
                            }
                        })
                    }
                }
            }

            if (context.type === 'after') {
                await TransactionManager.commitTransaction(context);
            }
        } catch (err) {
            context.params = _set(context.params, ['relateOto', paramsName, 'Error'], err);
            await hookManagerOto.call({path: 'error'}, context, {err, paramsName});

            if (!_get(context, ['params', 'relateOto', paramsName, 'skipError'], false)) {
                await TransactionManager.abortTransaction(context);
                throw new relateOtoError(_get(err, 'message', '') as string, err as (string | object));
            }
        }
        return context;
    };
};

/* 4 things removeOto hook must handle:
  1. Cascade delete.
  2. If Allow null set to null or unset
  3. Stop and don't allow action to happen.
  4. switch relationship to some other record.
*/
export const removeOto = (
    {
        idPath = '_id',
        dataPath = 'result',
        thereService,
        herePath,
        therePath,
        paramsName = `${thereService}_${stringifyPath(therePath)}_${stringifyPath(herePath)}`,
        addParams = {},
        passParams = false,
        beforeHooks = [],
        afterHooks = [],
        errorHooks = [],
        fkAction = 'null',
    }: RelateParams): (context: HookContext) => Promise<HookContext> => {
    let hookManagerRemoveOto = new HookSystem({
        hook_store: {
            before: beforeHooks,
            after: afterHooks,
            error: errorHooks,
        },
    });
    let fkActionOptions = ['protect', 'replace', 'null', 'unset', 'cascade'];
    if (!fkActionOptions.includes(fkAction)) {
        throw Error(`fkAction must be on of these options: "${fkActionOptions.join(', ')}". fkAction: "${fkAction}",`);
    }
    return async (context: HookContext): Promise<HookContext> => {
        checkContext(context, ['before', 'after'], ['remove'], 'removeOto');

        try {
            if (context.type === 'before') {
                let skipPath = _get(context, 'params.skipPath', []) as string | string[] | undefined;
                let startTransactionOptions = _get(context, 'params.startTransactionOptions', {}) as object | undefined;
                await beginTransaction(context, {skipPath, startTransactionOptions});
            }

            if (context.type === 'after') {
                const tp = Array.isArray(therePath) ? therePath : [therePath];
                const hereData = _get(context, dataPath);
                const fkId = _get(hereData, herePath);
                const id = _get(hereData, idPath, context.id);

                const notEmpty = !Array.isArray(fkId) ? !isEmpty(fkId) : !!fkId.length;
                // let runRelate = !_isnil(fkId) && !['{}', '[]'].includes(stringifyPath(fkId));
                let runRelate = !_isnil(fkId) && notEmpty;

                if (runRelate) {
                    if (fkAction.toLowerCase() === 'protect') {
                        throw new removeOtoError(`Dependent relationship: "${fkId}". Can't remove record: "${id}".`, context);
                    }

                    let patchObj = {};
                    let newFkId = _get(context, 'params.replaceId');

                    if (fkAction.toLowerCase() === 'replace') {
                        patchObj = _set({}, tp, newFkId);
                    } else if (fkAction.toLowerCase() === 'null') {
                        patchObj = _set({}, tp, null);
                    } else if (fkAction.toLowerCase() === 'unset') {
                        patchObj = _set({}, ['$unset', ...tp], 1);
                    }

                    let relateParams = {
                        paramsName,
                        idPath,
                        dataPath,
                        thereService,
                        herePath,
                        therePath,
                        passParams,
                        beforeHooks,
                        afterHooks,
                        errorHooks,
                        fkAction,
                    };

                    context.params = _set(context.params, ['removeOto', paramsName, 'fkId'], fkId);
                    context.params = _set(context.params, ['removeOto', paramsName, 'newFkId'], newFkId);
                    context.params = _set(context.params, ['removeOto', paramsName, 'patchObj'], patchObj);
                    context.params = _set(context.params, ['removeOto', paramsName, 'relateParams'], relateParams);

                    let params = {core: {...context.params.core, skipJoins: true}, ...addParams};
                    if (passParams) {
                        params = {
                            ..._unset(context.params, ['provider', 'query']),
                            ...{core: {...context.params.core, skipJoins: true}, ...addParams},
                        };
                    }

                    context.params = _set(context.params, ['relateMtm', paramsName, 'params'], params);

                    await hookManagerRemoveOto.call({path: 'before'}, context, {paramsName});

                    const customParams = _get(context, ['params', 'relateMtm', paramsName, 'params'], params);

                    if (fkAction.toLowerCase() === 'cascade') {
                        let removeOto_res = await new CoreCall(thereService, context, coreOptions).remove( _get(context, ['params', 'removeOto', paramsName, 'fkId'], fkId) as NullableId,
                            {...customParams, relate_hook: 'removeOto', disableSoftDelete: true})
                        context.params = _set(context.params, ['removeOto', paramsName, 'removeOto_res'], removeOto_res);
                    } else {
                        let removeOto_res = await new CoreCall(thereService, context, coreOptions).patch(
                            _get(context, ['params', 'removeOto', paramsName, 'fkId'], fkId) as NullableId,
                            _get(context, ['params', 'removeOto', paramsName, 'patchObj'], patchObj) as AnyObj,
                            {...customParams, relate_hook: 'removeOto', disableSoftDelete: true}
                        )

                        context.params = _set(context.params, ['removeOto', paramsName, 'removeOto_res'], removeOto_res);
                    }

                    await hookManagerRemoveOto.call({path: 'after'}, context, {paramsName});
                }
            }

            if (context.type === 'after') {
                await TransactionManager.commitTransaction(context);
            }
        } catch (err) {
            context.params = _set(context.params, ['removeOto', paramsName, 'Error'], err);
            await hookManagerRemoveOto.call({path: 'error'}, context, {err, paramsName});

            if (!_get(context, ['params', 'removeOto', paramsName, 'skipError'], false)) {
                await TransactionManager.abortTransaction(context);
                throw new removeOtoError(_get(err, 'message', '') as string, err as object | string);
            }
        }
        return context;
    };
};

export const relateMtm = (
    {
        idPath = '_id',
        dataPath = 'result',
        thereService,
        herePath,
        therePath,
        paramsName = `${thereService}_${stringifyPath(therePath)}_${stringifyPath(herePath)}`,
        addParams = {},
        passParams = false,
        beforeHooks = [],
        afterHooks = [],
        errorHooks = [],
    }: RelateParams): (context: HookContext) => Promise<HookContext> => {
    let hookManagerMtm = new HookSystem({
        hook_store: {
            before: beforeHooks,
            after: afterHooks,
            error: errorHooks,
        },
    });
    return async (context: HookContext): Promise<HookContext> => {
        checkContext(context, ['before', 'after'], ['create', 'update', 'patch'], 'relateMtm');

        try {
            if (context.type === 'before') {
                let skipPath = _get(context, 'params.skipPath', []) as string | string[] | undefined;
                let startTransactionOptions = _get(context, 'params.startTransactionOptions', {}) as object | undefined;
                await beginTransaction(context, {skipPath, startTransactionOptions});
            }

            if (context.type === 'before' && context.method !== 'create') {
                let id = _get(context, 'id');
                // if (_isnil(id)) id = _get(context, `params.query.${idPath}`);
                if (!_isnil(id)) {
                    const relateMtm_res_before = await loadExists(context);

                    context = setExists(context, relateMtm_res_before)
                    context.params = _set(context.params, ['relateMtm', paramsName, 'relateMtm_res_before'], relateMtm_res_before);
                }
            } else if (context.type === 'after') {
                const tp = Array.isArray(therePath) ? therePath : [therePath];
                const hereData = _get(context, dataPath);
                const fkIds = _get(hereData, herePath, []);
                const id = _get(hereData, idPath, context.id);
                let oldFkIds = _get(context, `params.relateMtm.${paramsName}.relateMtm_res_before.${stringifyPath(herePath)}`, []);
                let newFkIds;
                let removedFkIds;

                let runRelate = !_isnil(fkIds) && fkIds?.length !== oldFkIds?.length;

                if (runRelate && context.method !== 'create') {
                    if (_isnil(_get(context, `params.relateMtm.${paramsName}.relateMtm_res_before`))) {
                        runRelate = false;
                    } else {
                        let fkIdsMap = fkIds?.map((fkId: ID) => fkId);
                        removedFkIds = oldFkIds?.reduce((acc: (ID)[], oldFkId: ID) => {
                            if (fkIdsMap?.every((fkId: ID) => !_isequal(oldFkId, fkId))) {
                                acc.push(oldFkId);
                            }
                            return acc;
                        }, []);

                        let oldFkIdsMap = oldFkIds?.map((oldFkId: ID) => oldFkId);
                        newFkIds = fkIds?.reduce((acc: ID[], fkId: ID) => {
                            if (oldFkIdsMap?.every((oldFkId: ID) => !_isequal(fkId, oldFkId))) {
                                acc.push(fkId);
                            }
                            return acc;
                        }, []);
                        // @ts-ignore TODO: remove ts-ignore from relaters
                        runRelate = !!newFkIds.length || !!removedFkIds.length;
                    }
                } else if (runRelate && context.method === 'create') {
                    // @ts-ignore TODO: remove ts-ignore from relaters
                    newFkIds = Array.from(fkIds);
                    runRelate = !!newFkIds.length;
                }

                if (runRelate) {
                    let patchObj = {};
                    patchObj = _set(patchObj, ['$addToSet', ...tp], id);

                    let removedPatchObj = {};
                    removedPatchObj = _set(removedPatchObj, ['$pull', ...tp], id);

                    let relateParams = {
                        paramsName,
                        idPath,
                        dataPath,
                        thereService,
                        herePath,
                        therePath,
                        passParams,
                        beforeHooks,
                        afterHooks,
                        errorHooks,
                    };

                    context.params = _set(context.params, ['relateMtm', paramsName, 'fkIds'], fkIds);
                    context.params = _set(context.params, ['relateMtm', paramsName, 'oldFkIds'], oldFkIds);
                    context.params = _set(context.params, ['relateMtm', paramsName, 'removedFkIds'], removedFkIds);
                    context.params = _set(context.params, ['relateMtm', paramsName, 'newFkIds'], newFkIds);
                    context.params = _set(context.params, ['relateMtm', paramsName, 'patchObj'], patchObj);
                    context.params = _set(context.params, ['relateMtm', paramsName, 'removedPatchObj'], removedPatchObj);
                    context.params = _set(context.params, ['relateMtm', paramsName, 'relateParams'], relateParams);

                    let params = {core: {...context.params.core, skipJoins: true}, ...addParams};
                    if (passParams) {
                        params = {
                            ..._unset(context.params, ['provider', 'query']),
                            ...{core: {...context.params.core, skipJoins: true}, ...addParams},
                        };
                    }

                    _set(context, ['params', 'relateMtm', paramsName, 'params'], params);

                    await hookManagerMtm.call({path: 'before'}, context, {paramsName});

                    const customParams = _get(context, ['params', 'relateMtm', paramsName, 'params'], params);

                    if (!_isnil(_get(context, ['params', 'relateMtm', paramsName, 'newFkIds'], newFkIds)) && (_get(context, ['params', 'relateMtm', paramsName, 'newFkIds'], newFkIds) || []).length) {
                        let relateMtm_res = await new CoreCall(thereService, context, coreOptions).patch(null,
                            _get(context, ['params', 'relateMtm', paramsName, 'patchObj'], patchObj) as AnyObj,
                            {
                                ...customParams,
                                query: {
                                    _id: _get(context, ['params', 'relateMtm', paramsName, 'newFkIds'], newFkIds),
                                },
                                relate_hook: 'relateMtm',
                                disableSoftDelete: true,
                            })
                        context.params = _set(context.params, ['relateMtm', paramsName, 'relateMtm_res'], relateMtm_res);
                    }

                    // @ts-ignore TODO: remove ts-ignore from relaters
                    if (!_isnil(_get(context, ['params', 'relateMtm', paramsName, 'removedFkIds'], removedFkIds)) && _get(context, ['params', 'relateMtm', paramsName, 'removedFkIds'], removedFkIds).length) {
                        let relateMtm_removed_res = await new CoreCall(thereService, context, coreOptions).patch(
                            null,
                            _get(context, ['params', 'relateMtm', paramsName, 'removedPatchObj'], removedPatchObj) as AnyObj,
                            {
                                ...customParams,
                                query: {
                                    _id: _get(context, ['params', 'relateMtm', paramsName, 'removedFkIds'], removedFkIds),
                                },
                                relate_hook: 'relateMtm',
                                disableSoftDelete: true,
                            }
                        )
                        context.params = _set(context.params, ['relateMtm', paramsName, 'relateMtm_removed_res'], relateMtm_removed_res);
                    }

                    await hookManagerMtm.call({path: 'after'}, context, {paramsName});
                }
            }

            if (context.type === 'after') {
                await TransactionManager.commitTransaction(context);
            }
        } catch (err) {
            context.params = _set(context.params, ['relateMtm', paramsName, 'Error'], err);
            await hookManagerMtm.call({path: 'error'}, context, {err, paramsName});

            if (!_get(context, ['params', 'relateMtm', paramsName, 'skipError'], false)) {
                await TransactionManager.abortTransaction(context);
                throw new relateMtmError(_get(err, 'message', '') as string, err as string | object);
            }
        }
        return context;
    };
};

/*
* pull id from parents list of fkIds.
* */
export const removeMtm = (
    {
        idPath = '_id',
        dataPath = 'result',
        thereService,
        herePath,
        therePath,
        paramsName = `${thereService}_${stringifyPath(therePath)}_${stringifyPath(herePath)}`,
        addParams = {},
        passParams = false,
        beforeHooks = [],
        afterHooks = [],
        errorHooks = [],
    }: RelateParams): (context: HookContext) => Promise<HookContext> => {
    let hookManagerRemoveMtm = new HookSystem({
        hook_store: {
            before: beforeHooks,
            after: afterHooks,
            error: errorHooks,
        },
    });
    return async (context: HookContext): Promise<HookContext> => {
        checkContext(context, ['before', 'after'], ['remove'], 'removeMtm');

        try {
            if (context.type === 'before') {
                let skipPath = _get(context, 'params.skipPath', []) as string | string[] | undefined;
                let startTransactionOptions = _get(context, 'params.startTransactionOptions', {}) as object | undefined;
                await beginTransaction(context, {skipPath, startTransactionOptions});
            }

            if (context.type === 'after') {
                const tp = Array.isArray(therePath) ? therePath : [therePath];
                const hereData = _get(context, dataPath);
                const fkIds = _get(hereData, herePath, []);
                const id = _get(hereData, idPath, context.id);

                let runRelate = !_isnil(fkIds) && fkIds?.length;

                if (runRelate) {
                    let patchObj = {};
                    patchObj = _set(patchObj, ['$pull', ...tp], id);

                    let relateParams = {
                        paramsName,
                        idPath,
                        dataPath,
                        thereService,
                        herePath,
                        therePath,
                        passParams,
                        beforeHooks,
                        afterHooks,
                        errorHooks,
                    };

                    context.params = _set(context.params, ['removeMtm', paramsName, 'fkIds'], fkIds);
                    context.params = _set(context.params, ['removeMtm', paramsName, 'patchObj'], patchObj);
                    context.params = _set(context.params, ['removeMtm', paramsName, 'relateParams'], relateParams);

                    let params = {core: {...context.params.core, skipJoins: true}, ...addParams};
                    if (passParams) {
                        params = {
                            ..._unset(context.params, ['provider', 'query']),
                            ...{core: {...context.params.core, skipJoins: true}, ...addParams},
                        };
                    }

                    _set(context, ['params', 'relateMtm', paramsName, 'params'], params);

                    await hookManagerRemoveMtm.call({path: 'before'}, context, {paramsName});

                    const customParams = _get(context, ['params', 'relateMtm', paramsName, 'params'], params);

                    if (!_isnil(_get(context, ['params', 'removeMtm', paramsName, 'fkIds'], fkIds)) && (_get(context, ['params', 'removeMtm', paramsName, 'fkIds'], fkIds) as Array<any>).length) {
                        let relateMtm_res = await new CoreCall(thereService, context, coreOptions).patch(
                            null,
                            _get(context, ['params', 'removeMtm', paramsName, 'patchObj'], patchObj) as AnyObj,
                            {
                                ...customParams,
                                query: {
                                    _id: _get(context, ['params', 'removeMtm', paramsName, 'fkIds'], fkIds),
                                },
                                relate_hook: 'removeMtm',
                                disableSoftDelete: true,
                            }
                        )
                        context.params = _set(context.params, ['removeMtm', paramsName, 'relateMtm_res'], relateMtm_res);
                    }

                    await hookManagerRemoveMtm.call({path: 'after'}, context, {paramsName});
                }
            }

            if (context.type === 'after') {
                await TransactionManager.commitTransaction(context);
            }
        } catch (err) {
            context.params = _set(context.params, ['removeMtm', paramsName, 'Error'], err);
            await hookManagerRemoveMtm.call({path: 'error'}, context, {err, paramsName});

            if (!_get(context, ['params', 'removeMtm', paramsName, 'skipError'], false)) {
                await TransactionManager.abortTransaction(context);
                throw new removeMtmError(_get(err, 'message', '') as string, err as string | object);
            }
        }
        return context;
    };
};

// TO put on the service 'here' with the one and be related to the service 'there' with the many array
export const relateOtm = (
    {
        idPath = '_id',
        dataPath = 'result',
        thereService,
        herePath,
        therePath,
        paramsName = `${thereService}_${stringifyPath(therePath)}_${stringifyPath(herePath)}`,
        addParams = {},
        passParams = false,
        beforeHooks = [],
        afterHooks = [],
        errorHooks = [],
    }: RelateParams): (context: HookContext) => Promise<HookContext> => {
    let hookManagerOtm = new HookSystem({
        hook_store: {
            before: beforeHooks,
            after: afterHooks,
            error: errorHooks,
        },
    });
    return async (context: HookContext): Promise<HookContext> => {
        checkContext(context, ['before', 'after'], ['create', 'update', 'patch'], 'relateOtm');

        try {
            if (context.type === 'before') {
                let skipPath = _get(context, 'params.skipPath', []) as string | string[] | undefined;
                let startTransactionOptions = _get(context, 'params.startTransactionOptions', {}) as object | undefined;
                await beginTransaction(context, {skipPath, startTransactionOptions});
            }

            if (context.type === 'before' && context.method !== 'create') {
                let id = _get(context, 'id');
                // if (_isnil(id)) id = _get(context, `params.query.${idPath}`);
                if (!_isnil(id)) {
                    const relateOtm_res_before = await loadExists(context);
                    context = setExists(context, relateOtm_res_before)

                    context.params = _set(context.params, ['relateOtm', paramsName, 'relateOtm_res_before'], relateOtm_res_before);
                }
            } else if (context.type === 'after') {
                const tp = Array.isArray(therePath) ? therePath : [therePath];
                const hereData = _get(context, dataPath);
                const fkId = _get(hereData, herePath);
                let oldFkId;
                const id = _get(hereData, idPath, context.id);

                const notEmpty = !Array.isArray(fkId) ? !isEmpty(fkId) : !!fkId.length;
                // let runRelate = !_isnil(fkId) && !['{}', '[]'].includes(stringifyPath(fkId));
                let runRelate = !_isnil(fkId) && notEmpty;

                if (runRelate && context.method !== 'create') {
                    oldFkId = _get(context, `params.relateOtm.${paramsName}.relateOtm_res_before.${stringifyPath(herePath)}`);
                    if (_isnil(_get(context, `params.relateOtm.${paramsName}.relateOtm_res_before`))) {
                        runRelate = false;
                    } else {
                        runRelate = !_isnil(oldFkId) && !_isequal(oldFkId, fkId);
                    }
                }

                if (runRelate) {
                    let patchObj = {};
                    patchObj = _set(patchObj, ['$addToSet', ...tp], id);

                    let relateParams = {
                        paramsName,
                        idPath,
                        dataPath,
                        thereService,
                        herePath,
                        therePath,
                        passParams,
                        beforeHooks,
                        afterHooks,
                        errorHooks,
                    };

                    context.params = _set(context.params, ['relateOtm', paramsName, 'fkId'], fkId);
                    context.params = _set(context.params, ['relateOtm', paramsName, 'oldFkId'], oldFkId);
                    context.params = _set(context.params, ['relateOtm', paramsName, 'patchObj'], patchObj);
                    context.params = _set(context.params, ['relateOtm', paramsName, 'relateParams'], relateParams);

                    let params = {core: {...context.params.core, skipJoins: true}, ...addParams};
                    if (passParams) {
                        params = {
                            ..._unset(context.params, ['provider', 'query']),
                            ...{core: {...context.params.core, skipJoins: true}, ...addParams},
                        };
                    }

                    context.params = _set(context.params, ['relateOtm', paramsName, 'params'], params);

                    await hookManagerOtm.call({path: 'before'}, context, {paramsName});

                    const customParams = _get(context, ['params', 'relateMtm', paramsName, 'params'], params);

                    let relateOtm_res = await new CoreCall(thereService, context, coreOptions).patch(
                        _get(context, ['params', 'relateOtm', paramsName, 'fkId'], fkId) as NullableId,
                        _get(context, ['params', 'relateOtm', paramsName, 'patchObj'], patchObj) as AnyObj,
                        {...customParams, relate_hook: 'relateOtm', disableSoftDelete: true}
                    )

                    context.params = _set(context.params, ['relateOtm', paramsName, 'relateOtm_res'], relateOtm_res);

                    await hookManagerOtm.call({path: 'after'}, context, {paramsName});

                    if (context.method !== 'create') {
                        let oldFkPayload = {};
                        oldFkPayload = _set(oldFkPayload, ['$pull', ...tp], id);

                        await new CoreCall(thereService, context, coreOptions).patch(oldFkId as any, oldFkPayload, {
                            ...customParams,
                            ...{
                                relate_hook: 'relateOtm',
                                disableSoftDelete: true
                            }
                        })
                    }
                }
            }

            if (context.type === 'after') {
                await TransactionManager.commitTransaction(context);
            }
        } catch (err) {
            context.params = _set(context.params, ['relateOtm', paramsName, 'Error'], err);
            await hookManagerOtm.call({path: 'error'}, context, {err, paramsName});

            if (!_get(context, ['params', 'relateOtm', paramsName, 'skipError'], false)) {
                await TransactionManager.abortTransaction(context);
                throw new relateOtmError(_get(err, 'message', '') as string, err as string | object);
            }
        }
        return context;
    };
};

/*
* pull id from parent list of fkIds.
* */
export const removeOtm = (
    {
        idPath = '_id',
        dataPath = 'result',
        thereService,
        herePath,
        therePath,
        paramsName = `${thereService}_${stringifyPath(therePath)}_${stringifyPath(herePath)}`,
        addParams = {},
        passParams = false,
        beforeHooks = [],
        afterHooks = [],
        errorHooks = [],
    }: RelateParams): (context: HookContext) => Promise<HookContext> => {
    let hookManagerRemoveOtm = new HookSystem({
        hook_store: {
            before: beforeHooks,
            after: afterHooks,
            error: errorHooks,
        },
    });
    return async (context: HookContext): Promise<HookContext> => {


        try {
            if (context.type === 'before') {
                let skipPath = _get(context, 'params.skipPath', []) as string | string[] | undefined;
                let startTransactionOptions = _get(context, 'params.startTransactionOptions', {}) as object | undefined;
                await beginTransaction(context, {skipPath, startTransactionOptions});
            }

            if (context.type === 'after') {
                const tp = Array.isArray(therePath) ? therePath : [therePath];
                const hereData = _get(context, dataPath);
                const fkId = _get(hereData, herePath);
                const id = _get(hereData, idPath, context.id);

                const notEmpty = !Array.isArray(fkId) ? !isEmpty(fkId) : !!fkId.length;
                // let runRelate = !_isnil(fkId) && !['{}', '[]'].includes(stringifyPath(fkId));
                let runRelate = !_isnil(fkId) && notEmpty;

                if (runRelate) {
                    let patchObj = {};
                    patchObj = _set(patchObj, ['$pull', ...tp], id);

                    let relateParams = {
                        paramsName,
                        idPath,
                        dataPath,
                        thereService,
                        herePath,
                        therePath,
                        passParams,
                        beforeHooks,
                        afterHooks,
                        errorHooks,
                    };

                    context.params = _set(context.params, ['removeOtm', paramsName, 'fkId'], fkId);
                    context.params = _set(context.params, ['removeOtm', paramsName, 'patchObj'], patchObj);
                    context.params = _set(context.params, ['removeOtm', paramsName, 'relateParams'], relateParams);

                    let params = {core: {...context.params.core, skipJoins: true}, ...addParams};
                    if (passParams) {
                        params = {
                            ..._unset(context.params, ['provider', 'query']),
                            ...{core: {...context.params.core, skipJoins: true}, ...addParams},
                        };
                    }

                    _set(context, ['params', 'relateMtm', paramsName, 'params'], params);

                    await hookManagerRemoveOtm.call({path: 'before'}, context, {paramsName});

                    const customParams = _get(context, ['params', 'relateMtm', paramsName, 'params'], params);

                    let removeOtm_res = await new CoreCall(thereService, context, coreOptions).patch(
                        _get(context, ['params', 'removeOtm', paramsName, 'fkId'], fkId) as NullableId,
                        _get(context, ['params', 'removeOtm', paramsName, 'patchObj'], patchObj) as AnyObj,
                        {...customParams, relate_hook: 'removeOtm', disableSoftDelete: true}
                    )

                    context.params = _set(context.params, ['removeOtm', paramsName, 'removeOtm_res'], removeOtm_res);

                    await hookManagerRemoveOtm.call({path: 'after'}, context, {paramsName});
                }
            }

            if (context.type === 'after') {
                await TransactionManager.commitTransaction(context);
            }
        } catch (err) {
            context.params = _set(context.params, ['removeOtm', paramsName, 'Error'], err);
            await hookManagerRemoveOtm.call({path: 'error'}, context, {err, paramsName});

            if (!_get(context, ['params', 'removeOtm', paramsName, 'skipError'], false)) {
                await TransactionManager.abortTransaction(context);
                throw new removeOtmError(_get(err, 'message', '') as string, err as string | object);
            }
        }
        return context;
    };
};
