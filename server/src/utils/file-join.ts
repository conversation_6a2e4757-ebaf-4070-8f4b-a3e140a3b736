
import {_get, _set, stringifyPath} from './dash-utils.js';
import {HookContext} from "../declarations.js";
import {AnyObj} from "./types.js";
import {ObjectId} from "mongodb";
import {getJoin, findJoin} from './fast-join.js';
import {CoreCall, NullableId, getExists, loadExists, setExists} from 'feathers-ucan';

const lowerPaths = (record: AnyObj, paths: Array<any>): AnyObj => {
    paths.forEach(path => {
        const url = _get(record, `_fastjoin.files.${path}.url`)
        if (url) {
            record = _set(record, `${path}.url`, url);
        }
    })
    return record
}
export const fileJoinHook = (paths: Array<string> = []): (c: HookContext) => Promise<HookContext> => {
    return async (context: HookContext): Promise<HookContext> => {
        const isFind = context.method === 'find';

        if (!context.params.core?.skipJoins) {
            const fns = {
                getJoin,
                findJoin
            };
            const joinPath = async (fn: string, path: string) => {
                const config = {
                    service: 'uploads',
                    name: 'inner-file-join',
                    herePath: `${path}.uploadId`,
                    joinPath: `files.${path}`
                }
                const cfgs = (fn:any) => {
                    const opts = {
                        'getJoin': () => {
                            return {
                                ...config
                            }
                        },
                        'findJoin': () => {
                            return {
                                ...config,
                                query: {
                                    _id: {$in: isFind ? [].concat(...(context.result.data || []).map((a:any) => (a[path] || []).map((b:any) => b.uploadId))) : (context.result[path] || []).map((a:any) => a.uploadId)}
                                }
                            }
                        }
                    }
                    return (opts as any)[fn as any]()
                }
                // if(fn === 'getJoin') context = await getJoin(config)(context);
                context = await (fns as any)[fn as any](cfgs(fn))(context)
                    .catch(() => context)
                return context;
            }

            const getFn = (path: string | Array<string>) => {
                const p = Array.isArray(path) ? path : [path];
                const isArr = isFind ? Array.isArray(_get(context.result, ['data', 0, ...p])) : Array.isArray(_get(context.result, path));
                if (isArr) return 'findJoin';
                else return 'getJoin';
            }


            const stringPaths = paths.map(a => stringifyPath(a));
            const nestedPaths = getNestedPaths(stringPaths, context);
            await Promise.all(nestedPaths.map(a => joinPath(getFn(a), a as string)))
            if(isFind){
                if(!context.result.data) return context;
                context.result.data = context.result.data.map((a:any) => lowerPaths(a, nestedPaths));
            } else context.result = lowerPaths(context.result, nestedPaths)
        }
        return context;
    };
}

export const removeFiles = (
    {
        paths = [],
        ids = [],
        throwErr = false
    }: { ids?: Array<any>, paths?: Array<string>, throwErr?: boolean }
) => {
    return async (context: HookContext): Promise<HookContext> => {
        const doc = context.result ?? await new CoreCall(context.path, context, { skipJoins: true }).get(context.id as NullableId);
        const files: Array<string | ObjectId> = [...(ids ?? [])];

        // Only scan paths if ids are not already supplied
        if (!ids) {
            const addId = (rawPath: string) => {
                const segments = rawPath.split('.');
                const recurse = (data: any, segIndex: number, currPath = '') => {
                    if (segIndex >= segments.length) return;

                    const seg = segments[segIndex];

                    if (seg === '*') {
                        const val = _get(data, currPath);
                        if (val && typeof val === 'object' && !Array.isArray(val)) {
                            for (const key in val) {
                                const nextPath = `${currPath}${currPath ? '.' : ''}${key}`;
                                recurse(data, segIndex + 1, nextPath);
                            }
                        }
                    } else {
                        const nextPath = `${currPath}${currPath ? '.' : ''}${seg}`;

                        if (segIndex === segments.length - 1) {
                            const val = _get(data, nextPath);
                            if (val) {
                                if (Array.isArray(val)) {
                                    val.forEach(f => {
                                        const id = _get(f, 'uploadId') as string | ObjectId;
                                        if (id) files.push(id);
                                    });
                                } else {
                                    const id = _get(val, 'uploadId') as string | ObjectId;
                                    if (id) files.push(id);
                                }
                            }
                        } else {
                            recurse(data, segIndex + 1, nextPath);
                        }
                    }
                };

                recurse(doc, 0);
            };

            for (const path of paths) {
                addId(path);
            }
        }

        // Usage check + conditional delete
        const checkAndRemove = async (ul: AnyObj) => {
            ul.usage = (ul.usage || []).filter((a: any) => !files.includes(a.subject));
            if (!ul.usage?.length) {
                await new CoreCall('uploads', context, {}).remove(ul._id);
            } else {
                await new CoreCall('uploads', context, { skipJoins: true }).patch(ul._id, ul);
            }
        };

        const matches = files.length
            ? await new CoreCall('uploads', context, { skipJoins: true }).find({ query: { _id: { $in: files } } })
            : { total: 0, data: [] };

        if (matches.total) {
            await Promise.all(matches.data.map(checkAndRemove));
        }

        return context;
    };
};

const relatePath = (path: string | Array<string>) => {
    return async (context: HookContext): Promise<HookContext> => {
        const data = _get<any, { uploadId: string | ObjectId } & AnyObj>(context.result, path);
        if (data) {
            const subjectArray = Array.isArray(data);
            const subject = context.result._id;
            const subjectModel = context.path;

            const patchObj = {
                $addToSet: {
                    usage: {
                        subjectModel,
                        subject,
                        subjectArray,
                        subjectPath: stringifyPath(path)
                    }
                }
            };

            if (subjectArray) {

                await new CoreCall('uploads', context, {skipJoins: true}).patch(null, patchObj, {query: {_id: {$in: data.map(a => a.uploadId)}}, admin_pass: true })

            } else if (data.uploadId) {

                await new CoreCall('uploads', context, {skipJoins: true}).patch(data.uploadId as NullableId, patchObj, { admin_pass: true })

            }
        }
        return context;
    }
};

export const getNestedPaths = (paths: Array<string | string[]>, context: HookContext): Array<string | string[]> => {
    const resultItems = context.method === 'find' ? (context.result?.data || []) : [context.result];

    const expandPath = (pathSegments: string[], obj: any, currPath = '', segIndex = 0, out: string[] = []) => {
        if (!obj || typeof obj !== 'object') return;

        if (segIndex >= pathSegments.length) {
            out.push(currPath);
            return;
        }

        const seg = pathSegments[segIndex];

        if (seg === '*') {
            const subObj = _get(obj, currPath);
            if (subObj && typeof subObj === 'object') {
                for (const key in subObj) {
                    const nextPath = `${currPath}${currPath ? '.' : ''}${key}`;
                    expandPath(pathSegments, obj, nextPath, segIndex + 1, out);
                }
            }
        } else {
            const nextPath = `${currPath}${currPath ? '.' : ''}${seg}`;
            expandPath(pathSegments, obj, nextPath, segIndex + 1, out);
        }
    };

    const allPaths: string[] = [];

    for (const record of resultItems) {
        for (const path of paths) {
            if (Array.isArray(path)) {
                allPaths.push(path.join('.')); // retain as-is
            } else {
                const segments = path.split('.');
                expandPath(segments, record, '', 0, allPaths);
            }
        }
    }

    return allPaths;
};
export const relateFiles = (paths: Array<string | string[]> = []) => {
    return async (context: HookContext): Promise<HookContext> => {
        const nestedPaths = getNestedPaths(paths, context);
        await Promise.all(nestedPaths.map(a => relatePath(a)(context)));
        return context;
    };
};

type HandlerArgs<E> = { oldVal?: E, paths: Array<string>, throwErr?: boolean, joinByDefault?:Array<string> }

export const handleFileUpdate = <Existing>({oldVal, paths = [], throwErr}: HandlerArgs<Existing>) => {
    return async (context: HookContext): Promise<HookContext> => {
        if (context.type === 'before') {
            let doc = oldVal || await loadExists(context);
            context = setExists(context, doc);
            //REMOVE URL FROM PATHS
        } else {
            const doc = getExists(context);
            if (doc) {
                const rPaths: Array<string | Array<any>> = [];
                let removeIds: any[] = [];

                const addAndRemoveUploads = (rawPath: string) => {
                    const segments = rawPath.split('.');
                    const recurse = (dataOld: any, dataNew: any, segIndex: number, currPath = '') => {
                        if (segIndex >= segments.length) return;

                        const seg = segments[segIndex];

                        if (seg === '*') {
                            const oldObj = _get(dataOld, currPath);
                            const newObj = _get(dataNew, currPath);
                            const keys = new Set([
                                ...(oldObj && typeof oldObj === 'object' ? Object.keys(oldObj) : []),
                                ...(newObj && typeof newObj === 'object' ? Object.keys(newObj) : []),
                            ]);

                            for (const key of keys) {
                                const nextPath = `${currPath}${currPath ? '.' : ''}${key}`;
                                recurse(dataOld, dataNew, segIndex + 1, nextPath);
                            }
                        } else {
                            const nextPath = `${currPath}${currPath ? '.' : ''}${seg}`;

                            if (segIndex === segments.length - 1) {
                                const ov:any = _get(dataOld, nextPath) as AnyObj | Array<AnyObj>;
                                const nv:any = _get(dataNew, nextPath) as AnyObj | Array<AnyObj>;

                                const oldArr = Array.isArray(ov);

                                if (ov && !nv) {
                                    removeIds = [...removeIds, ...(oldArr ? [...ov] : [ov]).map(a => a.uploadId)];
                                } else if (ov) {
                                    if (oldArr) {
                                        const oIds = ov.map(a => String(a.uploadId));
                                        const nIds = nv?.map?.((a: any) => String(a.uploadId)) || [];
                                        removeIds = [...removeIds, ...([...oIds].filter(a => !nIds.includes(a)))];
                                        if (nIds.some((a: any) => !oIds.includes(a))) rPaths.push(nextPath);
                                    } else {
                                        const diff = String(ov.uploadId) !== String(nv?.uploadId);
                                        if (diff) {
                                            removeIds.push(ov.uploadId);
                                            rPaths.push(nextPath);
                                        }
                                    }
                                } else if (nv) {
                                    rPaths.push(nextPath);
                                }
                            } else {
                                recurse(dataOld, dataNew, segIndex + 1, nextPath);
                            }
                        }
                    };

                    recurse(doc, context.result, 0);
                };
                for (const path of paths) {
                    addAndRemoveUploads(path);
                }
                await removeFiles({ids: removeIds})(context);
                await relateFiles(rPaths)(context)
            }
            context.result = lowerPaths(context.result, paths);
        }
        return context;
    };
};


//for nested objects of files, you use '*' at the end of the path. So 'files.* - or files.*.foo for nested wildcard recursion'
export const scrubUploads = <Existing>({oldVal, paths = [], throwErr = false, joinByDefault}: HandlerArgs<Existing>) => {
    return async (context: HookContext): Promise<HookContext> => {
        if(!context.params.core?.skipJoins) {
            if (context.method === 'remove' && context.type === 'after') {
                return await removeFiles({paths, throwErr})(context);

            } else if (['patch', 'update'].includes(context.method)) {

                return await handleFileUpdate({oldVal, paths, throwErr})(context);

            } else if (context.type === 'after' && context.method === 'create') {
                return await relateFiles(paths)(context);
            } else if (context.type === 'after') {
                const add_files = context.params.runJoin?.add_files
                if(joinByDefault) return await fileJoinHook(joinByDefault)(context);
                if(add_files) {
                    return await fileJoinHook(Array.isArray(add_files) ? add_files : paths)(context);
                }
            }
        }
        return context;
    };
};




