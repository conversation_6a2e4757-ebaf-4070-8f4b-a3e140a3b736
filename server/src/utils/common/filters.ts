import leven from 'leven';
type LevenSortOptions = { key?:string }
export const levenSort = <T = any>(text:string, list:Array<T>, options?:LevenSortOptions):Array<T> => {
    if(!text || !list?.length) return [];

    const getVal = (v:any) => {
        if(!options?.key) return v;
        else return v[options.key];
    }
    const matches: Array<any> = [{ text: list[0], distance: leven(text, getVal(list[0]))}];
    for(let i = 1; i < list.length; i++){
        const distance = leven(text, getVal(list[i]));
        const item = { text: list[i], distance };

        // Find the correct insertion point using findIndex
        const index = matches.findIndex((match) => distance < match.distance);
        if (index === -1) {
            // If no smaller distance is found, add to the end
            matches.push(item);
        } else {
            // Insert at the found index
            matches.splice(index, 0, item);
        }
    }
    return matches;
}
