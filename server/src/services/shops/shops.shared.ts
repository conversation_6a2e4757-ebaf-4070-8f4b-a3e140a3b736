// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Shops, ShopsData, ShopsPatch, ShopsQuery, ShopsService } from './shops.class.js'

export type { Shops, ShopsData, ShopsPatch, ShopsQuery }

export type ShopsClientService = Pick<ShopsService<Params<ShopsQuery>>, (typeof shopsMethods)[number]>

export const shopsPath = 'shops'

export const shopsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const shopsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(shopsPath, connection.service(shopsPath), {
    methods: shopsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [shopsPath]: ShopsClientService
  }
}
