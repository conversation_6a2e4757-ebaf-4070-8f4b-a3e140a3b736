// Download the helper library from https://www.twilio.com/docs/node/install
// Find your Account SID and Auth Token at twilio.com/console
// and set the environment variables. See http://twil.io/secure
import tClient  from 'twilio';


export const sendVerifyText = async (PIN:string, login, { key, id, from}) => {
  const client = tClient(id, key);
  return await client.messages
    .create({from: from || '+***********', body: `Your Commoncare account verification PIN is ${PIN}. Go to https://commoncare.org/login/verify?id=${login._id} to verify`, to: login.number })
    .then(message => console.log(message.sid))
    .catch(err => console.log(err));
};

export const sendPasswordResetText = async (PIN, login, { key, id, from }) => {
  const client = tClient(id, key);
  return await client.messages
    .create({from: from || '+***********', body: `Your Commoncare password reset PIN is ${PIN}. Go to https://commoncare.org/login/reset?id=${login._id} to complete the reset`, to: login.number })
    .then(message => console.log(message.sid))
    .catch(err => console.log(err));
};

