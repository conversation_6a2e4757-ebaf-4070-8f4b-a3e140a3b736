import sgMail from '@sendgrid/mail';

import {verifyLogin, passwordReset} from './templates/index.js';

export const sendVerifyEmail = async (PIN, login, {key}) => {
    sgMail.setApiKey(key);
    const em = verifyLogin(PIN, login._id);
    const msg = await sgMail.send({
        from: 'Commoncare Admin <<EMAIL>>',
        to: [login.email],
        subject: 'Account Verification PIN',
        text: 'Here is your account verification pin',
        html: em
    })
        .catch(err => {
            console.log('error sending message', err);
        });

    return msg;
};

export const sendPasswordReset = async (PIN, login, {key}) => {
    sgMail.setApiKey(key);

    const em = passwordReset(PIN, login._id);
    const msg = await sgMail.send({
        from: 'CommonCare Admin <<EMAIL>>',
        to: [login.email],
        subject: 'Password Reset PIN',
        text: 'Here is your password reset pin',
        html: em
    })
        .catch(err => {
            console.log('error sending message', err);
        });

    return msg;
};

