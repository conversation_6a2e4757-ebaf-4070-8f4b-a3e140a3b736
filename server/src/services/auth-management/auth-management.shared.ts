import {ClientApplication} from '../../client.js';

export const authManagementPath = 'auth-management';
export const authManagementMethods = ['create'] as const;

import { AuthManagementService } from "./auth-management.js";
export type AuthManagementClientService = Pick<AuthManagementService, (typeof authManagementMethods)[number]>

export const authManagementClient = (client: ClientApplication) => {
    const connection = client.get('connection');

    client.use(authManagementPath, connection.service(authManagementPath), { methods: authManagementMethods })
}

// Add this service to the client service type index
declare module '../../client.js' {
    interface ServiceTypes {
        [authManagementPath]: AuthManagementClientService
    }
}
