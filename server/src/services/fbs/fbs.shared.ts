// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Fbs, FbsData, FbsPatch, FbsQuery, FbsService } from './fbs.class.js'

export type { Fbs, FbsData, FbsPatch, FbsQuery }

export type FbsClientService = Pick<FbsService<Params<FbsQuery>>, (typeof fbsMethods)[number]>

export const fbsPath = 'fbs'

export const fbsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const fbsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(fbsPath, connection.service(fbsPath), {
    methods: fbsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [fbsPath]: FbsClientService
  }
}
