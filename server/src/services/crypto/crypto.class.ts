// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#custom-services
import type { Id, NullableId, Params, ServiceInterface } from '@feathersjs/feathers'

import type { Application } from '../../declarations.js'

type Crypto = any
type CryptoData = any
type CryptoPatch = any
type CryptoQuery = any

export type { Crypto, CryptoData, CryptoPatch, CryptoQuery }

export interface CryptoServiceOptions {
  app: Application
}

export interface CryptoParams extends Params<CryptoQuery> {}

// This is a skeleton for a custom service class. Remove or add the methods you need here
export class CryptoService<ServiceParams extends CryptoParams = CryptoParams>
  implements ServiceInterface<Crypto, CryptoData, ServiceParams, CryptoPatch>
{
  constructor(public options: CryptoServiceOptions) {}

  async find(_params?: ServiceParams): Promise<Crypto[]> {
    return []
  }

  async get(id: Id, _params?: ServiceParams): Promise<Crypto> {
    return {
      id: 0,
      text: `A new message with ID: ${id}!`
    }
  }

  async create(data: CryptoData, params?: ServiceParams): Promise<Crypto>
  async create(data: CryptoData[], params?: ServiceParams): Promise<Crypto[]>
  async create(data: CryptoData | CryptoData[], params?: ServiceParams): Promise<Crypto | Crypto[]> {
    if (Array.isArray(data)) {
      return Promise.all(data.map((current) => this.create(current, params)))
    }

    return {
      id: 0,
      ...data
    }
  }

  // This method has to be added to the 'methods' option to make it available to clients
  async update(id: NullableId, data: CryptoData, _params?: ServiceParams): Promise<Crypto> {
    return {
      id: 0,
      ...data
    }
  }

  async patch(id: NullableId, data: CryptoPatch, _params?: ServiceParams): Promise<Crypto> {
    return {
      id: 0,
      text: `Fallback for ${id}`,
      ...data
    }
  }

  async remove(id: NullableId, _params?: ServiceParams): Promise<Crypto> {
    return {
      id: 0,
      text: 'removed'
    }
  }
}

export const getOptions = (app: Application) => {
  return { app }
}
