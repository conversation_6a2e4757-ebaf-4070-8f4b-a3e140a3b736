// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  StaticPlans,
  StaticPlansData,
  StaticPlansPatch,
  StaticPlansQuery,
  StaticPlansService
} from './static-plans.class.js'

export type { StaticPlans, StaticPlansData, StaticPlansPatch, StaticPlansQuery }

export type StaticPlansClientService = Pick<
  StaticPlansService<Params<StaticPlansQuery>>,
  (typeof staticPlansMethods)[number]
>

export const staticPlansPath = 'static-plans'

export const staticPlansMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const staticPlansClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(staticPlansPath, connection.service(staticPlansPath), {
    methods: staticPlansMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [staticPlansPath]: StaticPlansClientService
  }
}
