// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    staticPlansDataValidator,
    staticPlansPatchValidator,
    staticPlansQueryValidator,
    staticPlansResolver,
    staticPlansExternalResolver,
    staticPlansDataResolver,
    staticPlansPatchResolver,
    staticPlansQueryResolver,
    staticSchema
} from './static-plans.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {StaticPlansService, getOptions} from './static-plans.class.js'
import {staticPlansPath, staticPlansMethods} from './static-plans.shared.js'
import {allUcanAuth, CapabilityParts, CoreCall, noThrow} from 'feathers-ucan';
import {logChange} from '../../utils/index.js';

export * from './static-plans.class.js'
export * from './static-plans.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['static-plans', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['static-plans', '*']] as Array<CapabilityParts>;
    const ucanArgs: any = {
        get: noThrow,
        find: noThrow,
        create: noThrow,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };

    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        or: '*'
    })(context)
}

const handleBeforeCreate = async (context: HookContext) => {
    if (Array.isArray(context.data)) {
        let query:any = {$or: []}
        const coverageIds:any = [];
        const plan_ids:any = [];
        const compare_ids:any = [];
        for(let i = 0; i < context.data.length; i++){
            const d = context.data[i];
            if(d.coverage) {
                coverageIds.push(d.coverage);
                continue
            }
            if(d.plan_id) {
                plan_ids.push(d.plan_id);
                continue;
            }
            if(d.compare_id){
                compare_ids.push(d.compare_id);
            }
        }
        if(coverageIds.length) query.$or.push({coverage: {$in: coverageIds}});
        if(plan_ids.length) query.$or.push({plan_id: {$in: plan_ids}});
        if(compare_ids.length) query.$or.push({compare_id: {$in: compare_ids}});
        if(query.$or.length === 1) query = query.$or[0];
        const ex = await new CoreCall('static-plans', context).find({query, paginate: false})
        const byCoverage:any = {};
        const byPlanId:any = {};
        const byCompareId:any = {};
        const newByCoverage:any = {};
        const newByPlanId:any = {};
        const newByCompareId:any = {};
        // context.params.ex_static_plans = ex.data;
        for(const sp of ex){
            if(sp.coverage) byCoverage[sp.coverage] = sp;
            else if(sp.plan_id) byPlanId[sp.plan_id] = sp;
            else if(sp.compare_id) byCompareId[sp.compare_id] = sp;
        }
        const patchSp:any = {};
        const noPatch:any = [];
        for(const sp of context.data){
            let current;
            if(sp.coverage) current = byCoverage[sp.coverage];
            else if(sp.plan_id) current = byPlanId[sp.plan_id];
            else if(sp.compare_id) current = byCompareId[sp.compare_id];
            if(current){
                if(new Date().getTime() - new Date(current.updatedAt).getTime() < (1000 * 60 * 60)){
                    noPatch.push(current);
                    continue;
                }
                patchSp[current._id] = sp;
            } else {
                const { _id, ...rest } = sp
                if(sp.coverage) newByCoverage[sp.coverage] = rest;
                else if(sp.plan_id) newByPlanId[sp.plan_id] = rest;
                else if(sp.compare_id) newByCompareId[sp.compare_id] = rest;
            }
        }

        const updates = Object.keys(patchSp).map(a => {
            return {
                ...patchSp[a],
                _id: a
            }
        })
        if(updates.length) {

            const ops = updates.map(u => ({
                updateOne: {
                    filter: {_id: u._id},
                    update: {$set: u}
                }
            }));

            const svc: any = context.app.service('static-plans');

            const model = await svc.getModel()

            await model.bulkWrite(ops);
        }
        // const patchOne = async (id:any, data:any) => {
        //     return await new CoreCall('static-plans', context).patch(id, data, { admin_pass: true })
        // }
        // const patched = await Promise.all(Object.keys(patchSp).map(a => patchOne(a, patchSp[a])))
        // context.params.ex_static_plans = [...context.params.ex_static_plans, ...patched];
        const newSp:any = [];
        for(const k in newByCoverage) newSp.push(newByCoverage[k]);
        for(const k in newByPlanId) newSp.push(newByPlanId[k]);
        for(const k in newByCompareId) newSp.push(newByCompareId[k]);
        context.params.patched_static_plans = [...noPatch, ...updates];
        if(!newSp.length) context.result = context.params.patched_static_plans;
        context.data = newSp;
    } else {
        const query: any = {$limit: 1}
        if (context.data.coverage) query.coverage = context.data.coverage;
        else if (context.data.plan_id) query.plan_id = context.data.plan_id;
        else if (context.data.compare_id) query.compare_id = context.data.compare_id;
        const ex = await new CoreCall('static-plans', context).find({query})
        if (ex.total) {
            context.result = await new CoreCall('static-plans', context).patch(ex.data[0]._id, context.data)
            return context;
        }
    }
    return context;
}

const handleAfterCreate = async (context: HookContext) => {
    if (Array.isArray(context.data)) {
        context.result = [...context.result, ...context.params.patched_static_plans || []];
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const staticPlans = (app: Application) => {
    // Register our service on the Feathers application
    app.use(staticPlansPath, new StaticPlansService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: staticPlansMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(staticPlansPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(staticPlansExternalResolver),
                schemaHooks.resolveResult(staticPlansResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(staticPlansQueryValidator),
                schemaHooks.resolveQuery(staticPlansQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(staticPlansDataValidator),
                schemaHooks.resolveData(staticPlansDataResolver),
                handleBeforeCreate
            ],
            patch: [
                schemaHooks.validateData(staticPlansPatchValidator),
                schemaHooks.resolveData(staticPlansPatchResolver)
            ],
            remove: []
        },
        after: {
            all: [],
            create: [handleAfterCreate]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [staticPlansPath]: StaticPlansService
    }
}
