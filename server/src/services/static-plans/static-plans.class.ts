// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'
import type { Collection } from 'mongodb'
import type { Application } from '../../declarations.js'
import type { StaticPlans, StaticPlansData, StaticPlansPatch, StaticPlansQuery } from './static-plans.schema.js'

export type { StaticPlans, StaticPlansData, StaticPlansPatch, StaticPlansQuery }

export interface StaticPlansParams extends MongoDBAdapterParams<StaticPlansQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class StaticPlansService<ServiceParams extends Params = StaticPlansParams> extends MongoDBService<
  StaticPlans,
  StaticPlansData,
  StaticPlansParams,
  StaticPlansPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('static-plans')).then((collection) => {
        collection.createIndex({plan_id: 1}, {unique: true, sparse:true})
        collection.createIndex({coverage: 1}, {unique: true, sparse:true})
        collection.createIndex({compare_id: 1}, {unique: true, sparse:true})
        return collection;
      }),
    multi: true,
    operators: ['$regex', '$options']
  }
}
