// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'
import type { Application } from '../../declarations.js'
import {_get} from "../../utils/dash-utils.js";
import geoip from 'geoip-lite';
import logger from '../../utils/logger.js';
import {AnyObj} from "../../utils/types";

type MyIp = any
type MyIpData = any
type MyIpPatch = any
type MyIpQuery = any

export type { MyIp, MyIpData, MyIpPatch, MyIpQuery }

export interface MyIpParams extends MongoDBAdapterParams<MyIpQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class MyIpService<ServiceParams extends Params = MyIpParams> extends MongoDBService<
    MyIp,
    MyIpData,
    MyIpParams,
    MyIpPatch
> {
  async get(id, params:ServiceParams) {
    // let ip = id || _get(params,'ip');
    let ip = id !== '1' ? id : params['ip'];
    // IPV6 addresses can include IPV4 addresses
    // So req.ip can be '::ffff:***********'
    // However geoip-lite returns null for these
    if (ip && ip.includes('::ffff:')) {
      ip = ip.split(':').reverse()[0];
    }
    let lookedUpIP;
    try {
      lookedUpIP = geoip.lookup(ip);
      // console.log('looked up ip', ip, lookedUpIP?.ll);
    } catch (e) {
      logger.error('geoiplookup fail', e);
    } finally {
      logger.info('done', lookedUpIP);
    }
    if (!lookedUpIP && (ip === '127.0.0.1' || ip === '::1')) {
      lookedUpIP = {
        ip: '************',
        // _id: 'a',
        range: [],
        country: 'US',
        region: 'North Carolina',
        eu: undefined,
        timezone: 'America/Eastern',
        city: 'Apex',
        ll: [-78.90828,35.72123],
        metro: 0,
        area: 0,
        error: 'localhost ip'
      };
    }
    const ll = [lookedUpIP.ll[1], lookedUpIP.ll[0]];
    return {
      ip,
      ...lookedUpIP,
      ll,
      lngLat: ll
    };
  }

  async create(data:AnyObj, params:ServiceParams){
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    let ip = data.ip || params['ip'];

    // IPV6 addresses can include IPV4 addresses
    // So req.ip can be '::ffff:***********'
    // However geoip-lite returns null for these
    if (ip && ip.includes('::ffff:')) {
      ip = ip.split(':').reverse()[0];
    }
    let lookedUpIP;
    try {
      lookedUpIP = geoip.lookup(ip);
      // console.log('looked up ip', lookedUpIP);
    } catch (e) {
      logger.error('geoiplookup fail', e);
    } finally {
      logger.info('done', lookedUpIP);
    }
    if (!lookedUpIP && (ip === '127.0.0.1' || ip === '::1')) {
      lookedUpIP = {
        ip: '::1',
        // _id: 'a',
        range: [],
        country: 'US',
        region: 'North Carolina',
        eu: undefined,
        timezone: 'America/Eastern',
        city: 'Apex',
        ll: [-78.90828,35.72123],
        metro: 0,
        area: 0,
        error: 'localhost ip'
      };
    }
    return {
      ip,
      ...lookedUpIP,
      lngLat: _get(lookedUpIP,'ll')
    };
  }
}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    multi: true,
    Model: app.get('mongodbClient').then((db) => db.collection('my-ip'))
  }
}
