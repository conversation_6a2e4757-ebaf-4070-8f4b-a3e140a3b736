// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Caps, CapsData, CapsPatch, CapsQuery, CapsService } from './caps.class.js'

export type { Caps, CapsData, CapsPatch, CapsQuery }

export type CapsClientService = Pick<CapsService<Params<CapsQuery>>, (typeof capsMethods)[number]>

export const capsPath = 'caps'

export const capsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const capsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(capsPath, connection.service(capsPath), {
    methods: capsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [capsPath]: CapsClientService
  }
}
