// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    capsDataValidator,
    capsPatchValidator,
    capsQueryValidator,
    capsResolver,
    capsExternalResolver,
    capsDataResolver,
    capsPatchResolver,
    capsQueryResolver
} from './caps.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {CapsService, getOptions} from './caps.class.js'
import {capsPath, capsMethods} from './caps.shared.js'
import {allUcanAuth, anyAuth, CoreCall, loadExists, setExists} from 'feathers-ucan';
import {_get, AnyObj, logChange} from '../../utils/index.js';
import {buildUcan, encodeKeyPair, parseUcan, reduceAbilities, stackAbilities, ucanToken} from 'symbol-ucan';
import {ObjectId} from 'mongodb';

export * from './caps.class.js'
export * from './caps.schema.js'

const authenticate = async (context: HookContext): Promise<HookContext> => {

    const cap_subjects:any = []
    let ex: any = {}
    if (!['create', 'get', 'find'].includes(context.method) && !context.params.admin_pass) {
        ex = await loadExists(context);
        context = setExists(context, ex);
        if(ex) cap_subjects.push(ex.subject);
    }
    if(context.data?.subject) cap_subjects.push(context.data.subject);

    const {defaultScheme, defaultHierPart} = context.app.get('authentication') as any;

    const parentPermission = (can: string) => {
        if (!ex) return [['caps', 'WRITE'] as [string, string]];

        return [
            ['caps', 'WRITE'] as [string, string],
            ['orgs', 'WRITE'] as [string, string],
            {
                with: {
                    scheme: defaultScheme,
                    hierPart: defaultHierPart,
                },
                can: {namespace: `orgs:${ex.subject}`, segments: [`${can}`]}

            },
            {
                with: {
                    scheme: defaultScheme,
                    hierPart: defaultHierPart,
                },
                can: {namespace: `orgs:${ex.subject}`, segments: ['orgAdmin']}

            }
        ]
    }
    const config = {
        create: anyAuth,
        patch: parentPermission('WRITE'),
        update: parentPermission('WRITE'),
        remove: parentPermission('DELETE')
    }
    const allMethods = ['get', 'find', 'create', 'patch', 'remove']
    return await allUcanAuth<HookContext>(config, {
        adminPass: allMethods,
        or: '*',
        cap_subjects,
    })(context);
}

export const combineUcan = (ucan: string, applyUcan: string, reduce?: boolean) => {
    return async (context) => {
        const {secret} = context.app.get('authentication');

        const _ucan = parseUcan(ucan);
        const _applyUcan = parseUcan(applyUcan);
        const combinedUcan = await buildUcan({
            audience: _ucan.payload.aud,
            issuer: encodeKeyPair({secretKey: secret}),
            proofs: _ucan.payload.prf || [],
            capabilities: reduce ? reduceAbilities(_applyUcan.payload.att, _ucan.payload.att) : stackAbilities([..._ucan.payload.att, ..._applyUcan.payload.att])
        })
        return ucanToken(combinedUcan);
    }
};

type AdjustOptions = {
    logins: Array<string>,
    applyUcan: string,
    reduce?: boolean,
    check?: boolean
}
const adjustMemberPermissions = ({logins, applyUcan, reduce}: AdjustOptions) => {
    return async (context: HookContext): Promise<HookContext> => {

        if (logins?.length && (applyUcan)) {

            const $limit = 25;
            let total = 0;
            let $skip = 0

            const updateAll = async () => {
                const mbrs = await new CoreCall('logins', context,).find({
                    query: {_id: {$in: logins}, $limit, $skip},
                    admin_pass: true
                })

                total = mbrs.total;

                const patchLogin = async (login) => {
                    const {ucan, _id} = login;
                    const newUcan = await combineUcan(ucan, applyUcan, reduce)(context)
                        .catch(err => {
                            console.log(err);
                        });
                    return await new CoreCall('logins', context, {skipJoins: true}).patch(_id, {ucan: newUcan}, {admin_pass: true})
                }

                if (applyUcan) await Promise.all(mbrs.data.filter(a => !!a.login).map(a => patchLogin(a)));

                if (total > $skip + $limit) {
                    $skip += $limit
                    await updateAll();
                }
                return;
            };
            await updateAll();
        }
        return context;
    }
}


const createUcan = async (ucan: string, context: HookContext) => {
    if (ucan && ['{', '['].includes(ucan.charAt(0))) {
        const {secret} = context.app.get('authentication') as AnyObj;
        return await buildUcan({
            issuer: encodeKeyPair({secretKey: secret}),
            audience: context.params.login.did,
            capabilities: JSON.parse(ucan)
        })
    }
    return ucan;
}

//run after update logins
const convertUcans = async (context: HookContext): Promise<HookContext> => {
    const apply: any = {};
    const ex = await loadExists(context);
    context = setExists(context, ex);
    const {$set = {}, caps} = context.data;
    if (caps) {
        const list = Object.keys(caps)
        for (let i = 0; i < list.length; i++) {
            const {ucan} = caps[list[i]];
            if (ucan) {
                const newUcan = await createUcan(ucan, context);
                context.data.caps[list[i]].ucan = newUcan;
                if (newUcan !== (ex.caps || {})[list[i]]?.ucan) apply[list[i]] = newUcan;
            }
        }
    }
    const keys = Object.keys($set || {});
    if (keys.length) {
        for (let i = 0; i < keys.length; i++) {
            if (/^caps\..+\.ucan$/.test(keys[i])) {
                const newUcan = await createUcan($set[keys[i]], context);
                context.data.$set[keys[i]] = newUcan;
                if (newUcan !== _get(ex, keys[i])) apply[keys[i].split('.')[1]] = newUcan
            }
        }
    }
    const applyKeys = Object.keys(apply);
    if (applyKeys.length) context.params._applyKeys = apply;
    return context;
}

const convertLogins = async (context: HookContext): Promise<HookContext> => {
    const apply = context.params._applyKeys || {}
    const adding: any = {}
    const pulling: any = {}

    //handle adding ucan to logins that were added and convert the list to objectIds since we can't resolve this using the schema resolver
    const adds = Object.keys(context.data.$addToSet || {});
    if (adds.length) {
        for (let i = 0; i < adds.length; i++) {
            const k = adds[i];
            if (/^caps\..+\.logins$/.test(k)) {
                if (!apply[k.split('.')[1]]) {
                    const subK = k.split('.').slice(0, 2).join('.');
                    const logins = context.data.$addToSet[k]
                    if (logins.$each) {
                        context.data.$addToSet[k].$each = logins.$each.map(a => ObjectId.createFromHexString(a))
                        adding[subK] = logins.$each
                    } else {
                        adding[subK] = [logins];
                        context.data.$addToSet[k] = ObjectId.createFromHexString(logins);
                    }
                }
            }
        }
    }
    //handle removing ucan from logins that were pulled and convert the list to objectIds since we can't resolve this using the schema resolver
    const pulls = Object.keys(context.data.$pull || {});
    if (pulls.length) {
        for (let i = 0; i < pulls.length; i++) {
            const k = pulls[i];
            if (/^caps\..+\.logins$/.test(k)) {
                const logins = context.data.$pull[k]
                const subK = k.split('.').slice(0, 2).join('.')
                if (Array.isArray(logins)) {
                    pulling[subK] = logins
                    context.data.$pull[k] = logins.map(a => ObjectId.createFromHexString(a))
                } else {
                    pulling[subK] = [logins];
                    context.data.$pull[k] = ObjectId.createFromHexString(logins);
                }
            }
        }
    }
    context.params._cap = {...context.params._cap, add_logins: adding, pull_logins: pulling}
    return context;
}
const updateLogins = async (context: HookContext): Promise<HookContext> => {
    let adding: any = context.params._cap?.add_logins || {};
    let pulling: any = context.params._cap?.pull_logins || {};
    const apply = context.params._applyKeys || {}

    //handle changing ucan for all logins where the ucan was modified
    const applyKeys = Object.keys(apply);
    for (let i = 0; i < applyKeys.length; i++) {
        const logins = _get(context.result, ['caps', applyKeys[i], 'logins']) as any;
        if (logins?.length) adding[`caps.${applyKeys[i]}`] = logins;
    }
    const keys = Object.keys(adding);
    for (let i = 0; i < keys.length; i++) {
        await adjustMemberPermissions({
            logins: adding[keys[i]],
            applyUcan: _get(context.result, [keys[i], 'ucan']) as any
        })(context)
    }
    const pKeys = Object.keys(pulling);
    for (let i = 0; i < pKeys.length; i++) {
        await adjustMemberPermissions({
            logins: pulling[keys[i]],
            applyUcan: _get(context.result, [pKeys[i], 'ucan']) as any,
            reduce: true
        })(context)
    }
    return context;
}

const syncUcans = async (context: HookContext): Promise<HookContext> => {
    const {sync_ucans} = context.params.caps || {}
    //pass an array of caps keys to update
    if (sync_ucans) {
        for (let i = 0; i < sync_ucans.length; i++) {
            const {logins, ucan}: any = _get(context.result, `caps.${sync_ucans[i]}`) || {};
            if (logins && ucan) await adjustMemberPermissions({logins, ucan} as any)(context)
        }
    }
    return context;
}

const checkObjectId = (paths:Array<string>) => {
    return (context:HookContext) => {
        for (let i = 0; i < paths.length; i++) {
            const val = _get(context.params.query, paths[i]);
            if (val) {
                if (typeof val === 'string') {
                    context.params.query[paths[i]] = ObjectId.createFromHexString(val)
                } else if(Array.isArray(val)) {
                    context.params.query[paths[i]] = val.map(a => typeof a === 'string' ? ObjectId.createFromHexString(a) : a)
                } else {
                    for(const op of ['$in', '$eq', '$ne', '$nin']){
                        if(val[op]) context.params.query[paths[i]] =  { [`${op}`]: Array.isArray(val[op]) ? val[op].map(a => typeof a === 'string' ? ObjectId.createFromHexString(a) : a) : typeof val[op] === 'string' ? ObjectId.createFromHexString(val[op]) : val[op] }
                    }
                }
            }
        }
        return context;
    }
}

// A configure function that registers the service and its hooks via `app.configure`
export const caps = (app: Application) => {
    // Register our service on the Feathers application
    app.use(capsPath, new CapsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: capsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(capsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(capsExternalResolver),
                schemaHooks.resolveResult(capsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(capsQueryValidator),
                schemaHooks.resolveQuery(capsQueryResolver),
                checkObjectId(['subject'])
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(capsDataValidator),
                schemaHooks.resolveData(capsDataResolver)
            ],
            patch: [
                schemaHooks.validateData(capsPatchValidator),
                schemaHooks.resolveData(capsPatchResolver),
                convertUcans,
                convertLogins
            ],
            remove: []
        },
        after: {
            all: [],
            patch: [updateLogins],
            get: [syncUcans]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [capsPath]: CapsService
    }
}
