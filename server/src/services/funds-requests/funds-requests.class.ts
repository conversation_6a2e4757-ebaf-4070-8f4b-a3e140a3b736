// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { MongoDBService } from '@feathersjs/mongodb'
import type { MongoDBAdapterParams, MongoDBAdapterOptions } from '@feathersjs/mongodb'

import type { Application } from '../../declarations.js'
import type {
  FundsRequests,
  FundsRequestsData,
  FundsRequestsPatch,
  FundsRequestsQuery
} from './funds-requests.schema'

export type { FundsRequests, FundsRequestsData, FundsRequestsPatch, FundsRequestsQuery }

export interface FundsRequestsParams extends MongoDBAdapterParams<FundsRequestsQuery> {}

// By default calls the standard MongoDB adapter service methods but can be customized with your own functionality.
export class FundsRequestsService<ServiceParams extends Params = FundsRequestsParams> extends MongoDBService<
  FundsRequests,
  FundsRequestsData,
  FundsRequestsParams,
  FundsRequestsPatch
> {}

export const getOptions = (app: Application): MongoDBAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('mongodbClient').then((db) => db.collection('funds-requests'))
  }
}
