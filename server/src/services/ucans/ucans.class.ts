// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#custom-services
import type { Id, NullableId, Params, ServiceInterface } from '@feathersjs/feathers'

import type { Application } from '../../declarations.js'

type Ucans = any
type UcansData = any
type UcansPatch = any
type UcansQuery = any

export type { Ucans, UcansData, UcansPatch, UcansQuery }

export interface UcansServiceOptions {
  app: Application
}

export interface UcansParams extends Params<UcansQuery> {}

// This is a skeleton for a custom service class. Remove or add the methods you need here
export class UcansService<ServiceParams extends UcansParams = UcansParams>
  implements ServiceInterface<Ucans, UcansData, ServiceParams, UcansPatch>
{
  constructor(public options: UcansServiceOptions) {}

  async find(_params?: ServiceParams): Promise<Ucans[]> {
    return []
  }

  async get(id: Id, _params?: ServiceParams): Promise<Ucans> {
    return {
      id: 0,
      text: `A new message with ID: ${id}!`
    }
  }

  async create(data: UcansData, params?: ServiceParams): Promise<Ucans>
  async create(data: UcansData[], params?: ServiceParams): Promise<Ucans[]>
  async create(data: UcansData | UcansData[], params?: ServiceParams): Promise<Ucans | Ucans[]> {
    if (Array.isArray(data)) {
      return Promise.all(data.map((current) => this.create(current, params)))
    }

    return {
      id: 0,
      ...data
    }
  }

  // This method has to be added to the 'methods' option to make it available to clients
  async update(id: NullableId, data: UcansData, _params?: ServiceParams): Promise<Ucans> {
    return {
      id: 0,
      ...data
    }
  }

  async patch(id: NullableId, data: UcansPatch, _params?: ServiceParams): Promise<Ucans> {
    return {
      id: 0,
      text: `Fallback for ${id}`,
      ...data
    }
  }

  async remove(id: NullableId, _params?: ServiceParams): Promise<Ucans> {
    return {
      id: 0,
      text: 'removed'
    }
  }
}

export const getOptions = (app: Application) => {
  return { app }
}
