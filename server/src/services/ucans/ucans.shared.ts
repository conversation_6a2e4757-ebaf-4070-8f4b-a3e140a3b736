// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Ucans, UcansData, UcansPatch, UcansQuery, UcansService } from './ucans.class.js'

export type { Ucans, UcansData, UcansPatch, UcansQuery }

export type UcansClientService = Pick<UcansService<Params<UcansQuery>>, (typeof ucansMethods)[number]>

export const ucansPath = 'ucans'

export const ucansMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const ucansClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(ucansPath, connection.service(ucansPath), {
    methods: ucansMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [ucansPath]: UcansClientService
  }
}
