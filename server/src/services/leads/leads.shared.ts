// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Leads, LeadsData, LeadsPatch, LeadsQuery, LeadsService } from './leads.class.js'

export type { Leads, LeadsData, LeadsPatch, LeadsQuery }

export type LeadsClientService = Pick<LeadsService<Params<LeadsQuery>>, (typeof leadsMethods)[number]>

export const leadsPath = 'leads'

export const leadsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const leadsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(leadsPath, connection.service(leadsPath), {
    methods: leadsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [leadsPath]: LeadsClientService
  }
}
