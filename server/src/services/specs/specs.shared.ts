// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Specs, SpecsData, SpecsPatch, SpecsQuery, SpecsService } from './specs.class.js'

export type { Specs, SpecsData, SpecsPatch, SpecsQuery }

export type SpecsClientService = Pick<SpecsService<Params<SpecsQuery>>, (typeof specsMethods)[number]>

export const specsPath = 'specs'

export const specsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const specsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(specsPath, connection.service(specsPath), {
    methods: specsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [specsPath]: SpecsClientService
  }
}
