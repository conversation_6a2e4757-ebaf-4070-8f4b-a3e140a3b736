// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Wallets, WalletsData, WalletsPatch, WalletsQuery, WalletsService } from './wallets.class.js'

export type { Wallets, WalletsData, WalletsPatch, WalletsQuery }

export type WalletsClientService = Pick<WalletsService<Params<WalletsQuery>>, (typeof walletsMethods)[number]>

export const walletsPath = 'wallets'

export const walletsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const walletsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(walletsPath, connection.service(walletsPath), {
    methods: walletsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [walletsPath]: WalletsClientService
  }
}
