// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Mbrs, MbrsData, MbrsPatch, MbrsQuery, MbrsService } from './mbrs.class.js'

export type { Mbrs, MbrsData, MbrsPatch, MbrsQuery }

export type MbrsClientService = Pick<MbrsService<Params<MbrsQuery>>, (typeof mbrsMethods)[number]>

export const mbrsPath = 'mbrs'

export const mbrsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const mbrsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(mbrsPath, connection.service(mbrsPath), {
    methods: mbrsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [mbrsPath]: MbrsClientService
  }
}
