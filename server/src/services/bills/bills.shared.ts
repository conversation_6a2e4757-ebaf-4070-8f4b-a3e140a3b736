// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Bills, BillsData, BillsPatch, BillsQuery, BillsService } from './bills.class.js'

export type { Bills, BillsData, BillsPatch, BillsQuery }

export type BillsClientService = Pick<BillsService<Params<BillsQuery>>, (typeof billsMethods)[number]>

export const billsPath = 'bills'

export const billsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const billsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(billsPath, connection.service(billsPath), {
    methods: billsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [billsPath]: BillsClientService
  }
}
