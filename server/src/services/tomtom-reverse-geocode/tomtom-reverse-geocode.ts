// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import type { Application } from '../../declarations.js'
import { TomtomReverseGeocodeService, getOptions } from './tomtom-reverse-geocode.class.js'
import { tomtomReverseGeocodePath, tomtomReverseGeocodeMethods } from './tomtom-reverse-geocode.shared.js'

export * from './tomtom-reverse-geocode.class.js'
const successHook = context => {
  context.result.data = context.result.addresses;
  context.result.total = 0;
  context.result.limit = 400;
  context.result.skip = 0;
};
// A configure function that registers the service and its hooks via `app.configure`
export const tomtomReverseGeocode = (app: Application) => {
  // Register our service on the Feathers application
  app.use(tomtomReverseGeocodePath, new TomtomReverseGeocodeService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: tomtomReverseGeocodeMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(tomtomReverseGeocodePath).hooks({
    around: {
      all: []
    },
    before: {
      all: [],
      find: [],
      get: [],
      create: [],
      patch: [],
      remove: []
    },
    after: {
      all: [],
      find: [successHook]
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [tomtomReverseGeocodePath]: TomtomReverseGeocodeService
  }
}
