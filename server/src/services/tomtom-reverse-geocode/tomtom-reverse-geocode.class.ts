// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#custom-services
import type { Id, NullableId, Params, ServiceInterface } from '@feathersjs/feathers'

import type { Application } from '../../declarations.js'
import { tom<PERSON><PERSON><PERSON> as key, apiClient } from '../tomtom-geocode/tomtom-geocode.class.js';
type TomtomReverseGeocode = any
type TomtomReverseGeocodeData = any
type TomtomReverseGeocodePatch = any
type TomtomReverseGeocodeQuery = any

export type {
  TomtomReverseGeocode,
  TomtomReverseGeocodeData,
  TomtomReverseGeocodePatch,
  TomtomReverseGeocodeQuery
}

export interface TomtomReverseGeocodeServiceOptions {
  app: Application
}

export interface TomtomReverseGeocodeParams extends Params<TomtomReverseGeocodeQuery> {}

// This is a skeleton for a custom service class. Remove or add the methods you need here
export class TomtomReverseGeocodeService<
  ServiceParams extends TomtomReverseGeocodeParams = TomtomReverseGeocodeParams
> implements
    ServiceInterface<
      TomtomReverseGeocode,
      TomtomReverseGeocodeData,
      ServiceParams,
      TomtomReverseGeocodePatch
    >
{
  constructor(public options: TomtomReverseGeocodeServiceOptions) {}

  async find(_params?: ServiceParams): Promise<TomtomReverseGeocode[]> {
    let queryParams = {
      key: key,
      language: 'en-US',
      // limit: 10,
      // countrySet: 'US',
      // typeahead: true,
    };
    return await apiClient.get('/search/2/reverseGeocode/' + encodeURI(_params?.query.position) + '.json',{'params': queryParams})
        .then(res => {
          return res.data;
        })
        .catch(err => {
          // eslint-disable-next-line no-console
          console.error('geocode error', err.errno);
          return err;
        });
  }

  async get(id: Id, _params?: ServiceParams): Promise<TomtomReverseGeocode> {
    return {
      id: 0,
      text: `A new message with ID: ${id}!`
    }
  }

  async create(data: TomtomReverseGeocodeData, params?: ServiceParams): Promise<TomtomReverseGeocode>
  async create(data: TomtomReverseGeocodeData[], params?: ServiceParams): Promise<TomtomReverseGeocode[]>
  async create(
    data: TomtomReverseGeocodeData | TomtomReverseGeocodeData[],
    params?: ServiceParams
  ): Promise<TomtomReverseGeocode | TomtomReverseGeocode[]> {
    if (Array.isArray(data)) {
      return Promise.all(data.map((current) => this.create(current, params)))
    }

    return {
      id: 0,
      ...data
    }
  }

  // This method has to be added to the 'methods' option to make it available to clients
  async update(
    id: NullableId,
    data: TomtomReverseGeocodeData,
    _params?: ServiceParams
  ): Promise<TomtomReverseGeocode> {
    return {
      id: 0,
      ...data
    }
  }

  async patch(
    id: NullableId,
    data: TomtomReverseGeocodePatch,
    _params?: ServiceParams
  ): Promise<TomtomReverseGeocode> {
    return {
      id: 0,
      text: `Fallback for ${id}`,
      ...data
    }
  }

  async remove(id: NullableId, _params?: ServiceParams): Promise<TomtomReverseGeocode> {
    return {
      id: 0,
      text: 'removed'
    }
  }
}

export const getOptions = (app: Application) => {
  return { app }
}
