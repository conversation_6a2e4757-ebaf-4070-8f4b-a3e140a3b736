// // Initializes the `uploads` service on path `/uploads`
// import hooks from './uploads.hooks';
// import BlobService from 'feathers-blob';
// import fs from 'fs-blob-store';
//
// export default function (app) {
//   // uploads to public folder
//   // -------------------------------
//   const blobStorage = fs(app.get('uploads').privateFolder + '/uploads');
//   app.use('/upload-local-private',
//     function (req, res, next) {
//       req.feathers.file = req.file;
//       next();
//     },
//     BlobService({ Model: blobStorage })
//   );
//
//   // Get our initialized service so that we can register hooks
//   const staticService = app.service('upload-local-private');
//   staticService.hooks(hooks);
// };
