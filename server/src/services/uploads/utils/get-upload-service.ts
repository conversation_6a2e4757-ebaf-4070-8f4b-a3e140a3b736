import {FeathersApplication} from "@feathersjs/feathers";

export const getUploadService = ({ storage, app }: { storage: string, app: FeathersApplication}) => {
  const uploadsConfig = app.get('uploads');
  const { enums } = uploadsConfig;
  const storageService = storage || enums.STORAGE_TYPES[uploadsConfig.defaultFileService];
  const uploadServiceName = enums.UPLOAD_SERVICES[storageService];
  let uploadService = app.service(uploadServiceName);

  return { uploadService, uploadServiceName, storageService, enums };
};
