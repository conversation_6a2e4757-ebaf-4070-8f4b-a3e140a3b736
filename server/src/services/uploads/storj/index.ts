// Initializes the `uploads` service on path `/uploads`
import {HookContext} from '../../../declarations.js';

import {S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand} from '@aws-sdk/client-s3';
import {getSignedUrl} from '@aws-sdk/s3-request-presigner';

declare type S3Config =
    { accessKeyId: 'string', secretAccessKey: 'string', region: 'string', endpoint: 'string' }
    & { [key: string]: any }
const s3Config = (app): S3Config => {
    return (app.get('s3') || {}) as S3Config
}
const getS3 = (config: S3Config) => {
    const {accessKeyId, secretAccessKey, region, endpoint} = config;
    return new S3Client({
        credentials: {
            accessKeyId,
            secretAccessKey
        },
        endpoint,
        region
    });
}

export const uploadStorj = async (context: HookContext) => {

    const config = s3Config(context.app);
    const s3 = getS3(config);
    const file = context.params.file || context.data.file;

    const filename = new Date().getTime().toString() + ':' + (context.params.query?.name || context.data.name || file.originalname);

    if(!file) throw new Error('No file found for upload');
    const command = new PutObjectCommand({
        Bucket: config.bucket,
        Key: filename,
        Body: file.buffer || file.readable
    })

    const s3Res = await s3.send(command)
        .catch(err => {
            console.error(err);
        });

    context.params.upload = s3Res;
    context.data.fileId = filename;
    context.data.bucket = config.bucket;
    context.data.storage = 'storj';
    return context;
    // Get our initialized service so that we can register hooks
};

export const removeStorj = async (context: HookContext): Promise<HookContext> => {
    const config = s3Config(context.app);
    const s3 = getS3(config);

    const command = new DeleteObjectCommand({
        Bucket: config.bucket,
        Key: context.params.fileId,
    })
    await s3.send(command);
    return context;
    // Get our initialized service so that we can register hooks
}

type StorjOptions = {
    expires?:number
}
export const getStorjUrl = (file:any, options?:StorjOptions): (c: HookContext) => Promise<string> => {
    return async (context: HookContext): Promise<string> => {
        const config = s3Config(context.app);
        const s3 = getS3(config);

        const command = new GetObjectCommand({
            Bucket: file.bucket || config.bucket,
            Key: file.fileId,
            ResponseContentDisposition:'inline',
            ResponseContentType: file.info?.type || file.type
        })

        return await getSignedUrl(s3, command, {expiresIn: options?.expires || 60 * 60 * 5})
        // Get our initialized service so that we can register hooks
    }
}
