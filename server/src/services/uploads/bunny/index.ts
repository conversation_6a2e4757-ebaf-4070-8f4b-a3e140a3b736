import {HookContext} from '../../../declarations.js';
import axios from 'axios';
import {AnyObj, loadExists, setExists} from 'feathers-ucan';
import { Readable } from 'stream';

const bunnyUrl = 'https://video.bunnycdn.com';

export const createVideo = async (context:HookContext):Promise<HookContext> => {
    const libraryId = (context.app.get('video') as AnyObj).bucket
    const key = (context.app.get('video') as AnyObj).key;
    const thumbnailTime = 2000;

    const url = `${bunnyUrl}/library/${libraryId}/videos`;

    const title = context.params.query?.info?.name || (new Date().getTime().toString() + ':' + context.params.file.originalname || 'Untitled')
    const headers = {
        accept: 'application/json',
        'content-type': 'application/*+json',
        'AccessKey': key
    }

   const res = await axios.post(url, {
       title,
       thumbnailTime
   }, {
        headers
    })

    if(res.status === 200){
        context.data.name = title;
        context.data.originalname = context.params.file?.originalname
        context.data.bucket = res.data.videoLibraryId;
        context.data.fileId = res.data.guid;
        context.data.status = String(res.data.status);
        context.data.video = true;
        context.data.storage = 'bunny';
    } else throw new Error(`Upload error code ${res.status}: failed creating video upload`)
    return context;
}

export const uploadVideo = async (context:HookContext):Promise<HookContext> => {
    const key = (context.app.get('video') as AnyObj).key;

    context = await createVideo(context);
    const url = `${bunnyUrl}/library/${context.data.bucket}/videos/${context.data.fileId}`;
    const res = await axios.put(url, Readable.from(context.params.file.buffer), {
        headers: {
            'Content-Type': context.params.file.mimetype,
            accept: 'application/json',
            'AccessKey': key
        }
    })
        .catch(err => {
            console.error(err);
            throw new Error(err);
        })

    context.data.status = String(res.data.statusCode)
    return context;
}

export const deleteVideo = async (context:HookContext) => {
    const key = (context.app.get('video') as AnyObj).key;
    const exists = await loadExists(context) as { fileId: string, bucket: string } & AnyObj;
    context = setExists(context, exists);
    const url = `${bunnyUrl}/library/${exists.bucket}/videos/${exists.fileId}`;
    const res = await axios.delete(url, {
        headers: {
            accept: 'application/json',
            'AccessKey': key
        }
    })
    return context;
}

export const getVideoUrl = (file:any):string => {
    return `https://iframe.mediadelivery.net/embed/${file.bucket}/${file.fileId}`;
}
