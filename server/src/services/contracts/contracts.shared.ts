// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  Contracts,
  ContractsData,
  ContractsPatch,
  ContractsQuery,
  ContractsService
} from './contracts.class.js'

export type { Contracts, ContractsData, ContractsPatch, ContractsQuery }

export type ContractsClientService = Pick<
  ContractsService<Params<ContractsQuery>>,
  (typeof contractsMethods)[number]
>

export const contractsPath = 'contracts'

export const contractsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const contractsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(contractsPath, connection.service(contractsPath), {
    methods: contractsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [contractsPath]: ContractsClientService
  }
}
