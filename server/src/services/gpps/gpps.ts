// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    gppsDataValidator,
    gppsPatchValidator,
    gppsQueryValidator,
    gppsResolver,
    gppsExternalResolver,
    gppsDataResolver,
    gppsPatchResolver,
    gppsQueryResolver
} from './gpps.schema.js'

import type {Application, HookContext} from '../../declarations.js'
import {GppsService, getOptions} from './gpps.class.js'
import {gppsPath, gppsMethods} from './gpps.shared.js'

export * from './gpps.class.js'
export * from './gpps.schema.js'

import {allUcanAuth, CapabilityParts, loadExists, noThrow, setExists} from 'feathers-ucan';
import {logChange, scrub} from '../../utils/index.js';



const authenticate = async (context: HookContext) => {
    const writer = [['ims', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['ims', '*']] as Array<CapabilityParts>;
    const ucanArgs: any = {
        create: noThrow,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };
    if (context.params.special_change) ucanArgs.patch = noThrow
    else if (context.method === 'patch') {
        const existing = await loadExists(context, {params: {runJoin: {ims_person: true}}});
        context = setExists(context, existing);
        if (!existing?.person) {
            ucanArgs.patch = noThrow
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        or: '*',
        loginPass: [[['person/owner'], '*']],
        creatorPass: '*',
    })(context)
}

const addPrints = (context: HookContext) => {
    if(context.data.updatedBy?.fingerprint){
        if(context.data.$addToSet) context.data.$addToSet.fingerprints = context.data.updatedBy.fingerprint;
        else context.data.fingerprints = [context.data.updatedBy.fingerprint];
    }
    return context;
}

const scrubFields = ['comments'];

// A configure function that registers the service and its hooks via `app.configure`
export const gpps = (app: Application) => {
    // Register our service on the Feathers application
    app.use(gppsPath, new GppsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: gppsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(gppsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(gppsExternalResolver),
                schemaHooks.resolveResult(gppsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(gppsQueryValidator),
                schemaHooks.resolveQuery(gppsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(gppsDataValidator),
                schemaHooks.resolveData(gppsDataResolver),
                addPrints,
                scrub(scrubFields)
            ],
            patch: [
                schemaHooks.validateData(gppsPatchValidator),
                schemaHooks.resolveData(gppsPatchResolver),
                addPrints,
                scrub(scrubFields)

            ],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [gppsPath]: GppsService
    }
}
