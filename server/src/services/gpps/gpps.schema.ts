// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, commonPatch} from '../../utils/common/schemas.js';
import { placeSchema, peopleSchema } from '../shops/shops.schema.js';
// Main data model schema
export const gppsSchema = {
    $id: 'Gpps',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'gps'],
    properties: {
        _id: ObjectIdSchema(),
        person: ObjectIdSchema(),
        fingerprint: ObjectIdSchema(),
        gps: ObjectIdSchema(),
        jobTitle: {type: 'string'},
        name: {type: 'string'},
        email: {type: 'string'},
        anon: {type: 'boolean'},
        form: ObjectIdSchema(),
        shop: ObjectIdSchema(),
        fid: { type: 'string' },
        comments: {type: 'string'},
        income: { type: 'number' }, /** wages from employer */
        lastPremium: { type: 'number' },
        deduction: { type: 'number' }, //always converted to monthly
        interval: { type: 'string' }, //just to show it back the way they entered it
        household: {
            type: 'object',
            properties: {
                place: placeSchema,
                people: {type: 'array', items: peopleSchema },
                income: { type: 'number' }
            }
        },

        majorMedical: { type: 'string'},
        closest: { type: 'string' },
        coverage: ObjectIdSchema(),
        policy: { type: 'string' },
        keepOld: { type: 'boolean' },
        cashInLieu: { type: 'boolean' },
        choices: {
            type: 'object', patternProperties: {
                "^.*$": ObjectIdSchema()
            }
        },
        ...commonFields.properties
    }
} as const
export type Gpps = FromSchema<typeof gppsSchema>
export const gppsValidator = getValidator(gppsSchema, dataValidator)
export const gppsResolver = resolve<Gpps, HookContext>({})

export const gppsExternalResolver = resolve<Gpps, HookContext>({})

// Schema for creating new data
export const gppsDataSchema = {
    $id: 'GppsData',
    type: 'object',
    additionalProperties: false,
    required: ['gps'],
    properties: {
        ...gppsSchema.properties
    }
} as const
export type GppsData = FromSchema<typeof gppsDataSchema>
export const gppsDataValidator = getValidator(gppsDataSchema, dataValidator)
export const gppsDataResolver = resolve<GppsData, HookContext>({})

// Schema for updating existing data
export const gppsPatchSchema = {
    $id: 'GppsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...gppsSchema.properties,
        ...commonPatch(gppsSchema.properties).properties
    }
} as const
export type GppsPatch = FromSchema<typeof gppsPatchSchema>
export const gppsPatchValidator = getValidator(gppsPatchSchema, dataValidator)
export const gppsPatchResolver = resolve<GppsPatch, HookContext>({})

// Schema for allowed query properties
export const gppsQuerySchema = {
    $id: 'GppsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax(gppsSchema.properties)
    }
} as const
export type GppsQuery = FromSchema<typeof gppsQuerySchema>
export const gppsQueryValidator = getValidator(gppsQuerySchema, queryValidator)
export const gppsQueryResolver = resolve<GppsQuery, HookContext>({})
