// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { ClaimPayments, ClaimPaymentsData, ClaimPaymentsPatch, ClaimPaymentsQuery, ClaimPaymentsService } from './claim-payments.class.js'

export type { ClaimPayments, ClaimPaymentsData, ClaimPaymentsPatch, ClaimPaymentsQuery }

export type ClaimPaymentsClientService = Pick<
  ClaimPaymentsService<Params<ClaimPaymentsQuery>>,
  (typeof claimPaymentsMethods)[number]
>

export const claimPaymentsPath = 'claim-payments'

export const claimPaymentsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const claimPaymentsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(claimPaymentsPath, connection.service(claimPaymentsPath), {
    methods: claimPaymentsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [claimPaymentsPath]: ClaimPaymentsClientService
  }
}
