import {HookContext} from '../../../declarations.js';
import {CoreCall} from 'feathers-ucan';

export const methods = (context:HookContext) => {
    return {
        'ca': {
            allowed: ['plan'],
            run: async (connect_id:string, data:any) => {
                return await new CoreCall('banking', context).get(connect_id, {
                    banking: {
                        stripe: {
                            method: 'outbound_payment',
                            args: [data]
                        }
                    }
                })
                    .catch(err => {
                        console.error(`<PERSON>rror creating outbound transfer while paying claims - ${err.message}`)
                        throw new Error(`Error creating payment: ${err.message}`)
                    })
            },
            patch: (transaction, data:any) => {
                return {
                    careAccount: data.financial_account,
                    providerCareAccount: data.destination_payment_method_data.financial_account,
                    transactionId:transaction.transactionID,
                    method: 'ca',
                    amount: data.amount,
                    status: transaction.status
                }
            }

        }
    }
}
