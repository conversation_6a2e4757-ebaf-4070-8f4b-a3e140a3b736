// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  Practitioners,
  PractitionersData,
  PractitionersPatch,
  PractitionersQuery,
  PractitionersService
} from './practitioners.class.js'

export type { Practitioners, PractitionersData, PractitionersPatch, PractitionersQuery }

export type PractitionersClientService = Pick<
  PractitionersService<Params<PractitionersQuery>>,
  (typeof practitionersMethods)[number]
>

export const practitionersPath = 'practitioners'

export const practitionersMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const practitionersClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(practitionersPath, connection.service(practitionersPath), {
    methods: practitionersMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [practitionersPath]: PractitionersClientService
  }
}
