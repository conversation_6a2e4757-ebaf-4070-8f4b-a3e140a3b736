// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    claimsDataValidator,
    claimsPatchValidator,
    claimsQueryValidator,
    claimsResolver,
    claimsExternalResolver,
    claimsDataResolver,
    claimsPatchResolver,
    claimsQueryResolver, claimsSchema
} from './claims.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {ClaimsService, getOptions} from './claims.class.js'
import {claimsPath, claimsMethods} from './claims.shared.js'
import {
    allUcanAuth,
    anyAuth,
    CapabilityParts,
    CoreCall,
    getExists,
    loadExists,
    setExists,
    checkUcan
} from 'feathers-ucan';
import {_get, getJoin, limitData, logChange, relate, sanitize, scrub, scrubUploads} from '../../utils/index.js';
import {loadOrCreate} from '../cross-sections/utils/index.js';

export * from './claims.class.js'
export * from './claims.schema.js'

const runJoins = async (context: HookContext) => {
    const {claim_provider} = context.params.runJoin || {};
    if (claim_provider) context = await getJoin({
        herePath: 'provider',
        service: 'providers'
    })(context)
    context = await getJoin({service: 'meds', herePath: 'med', joinPath: 'med'})(context)
    return getJoin({service: 'procedures', herePath: 'procedure', joinPath: 'procedure'})(context);
}

const authenticate = async (context: HookContext) => {
    const writer = [['claims', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['claims', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects:any = []

    if (!['get', 'find'].includes(context.method)) {
        if (context.method !== 'create') {

            const existing = await loadExists(context, {params: {runJoin: {claim_provider: true}}});
            context = setExists(context, existing);

            const orgNamespace = `orgs:${existing._fastjoin?.provider?.org}`;
            cap_subjects.push(existing._fastjoin?.provider?.org)
            const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [`plans:${existing.plan}`, 'planAdmin'], [orgNamespace, 'providerAdmin']]
            for (const w of orgWrite) {
                ucanArgs.patch.unshift(w);
                ucanArgs.update.unshift(w);
                ucanArgs.remove.unshift(w);
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        cap_subjects
    })(context) as any;
}

const relateVisit = async (context: HookContext): Promise<HookContext> => {
    return relate('otm', {
        herePath: 'visit',
        therePath: 'claims',
        thereService: 'visits',
        paramsName: 'claimToVisit'
    })(context);
}


const canEditStatuses = ['unopened', 'open'];
const noEditStatuses = ['paid', 'settled', 'pending', 'approved'];

const preventChanges = async (context: HookContext) => {
    if(context.params.skip_hooks) return context;
    const existing = await loadExists(context);
    context = setExists(context, existing);
    const {status} = existing || {status: 'unopened'}
    if (!context.params._allow_claims_change && noEditStatuses.includes(status)) {
        const moreFields = status === 'pending' ? ['status', 'payments', 'deleted', 'deletedAt'] : ['deleted', 'deletedAt']
        let err = true;
        const $set = {}
        for (const k in context.data?.$set || {}) {
            if (/files\./.test(k)) {
                err = false;
                $set[k] = context.data.$set[k];
            }
        }
        if (!existing.deleted && (context.data.deleted || context.data.$set?.deleted)) err = false;
        if (err) throw new Error(`You cannot change a claim with status ${status} except to add record files`)
        else context.data = limitData(context.data, {$set}, {moreFields})
    }
    return context;
}

const addTotal = (context: HookContext) => {
    const existing = getExists(context);
    const amount = context.data.amount || context.data.$set?.amount || existing?.amount || 0;
    const qty = context.data.qty || context.data.$set?.qty || existing?.qty || 0;
    let taxes = context.data.taxes || context.data.$set?.taxes || existing?.taxes || {};
    const $set = context.data.$set || {}
    let fees = context.data.fees || $set.fees || existing?.fees || {};
    let paid = context.data.paid?.amount || $set.paid?.amount || $set['paid.amount'] || existing?.paid?.amount || 0;
    for (const k in context.data.$set || {}) {
        if (/^taxes\..+\.amount$/.test(k)) {
            taxes[k.split('.')[1]] = {amount: context.data.$set[k]}
        }
        if (/^fees\..+\.amount$/.test(k)) {
            fees[k.split('.')[1]] = {amount: context.data.$set[k]}
        }
    }
    const subtotal = (amount || 0) * (qty || 0)
    const claim = {
        subtotal,
        amount,
        qty,
        taxes,
        fees
    }
    context.data.subtotal = subtotal
    context.data.total = getClaimPrice(claim).total;
    context.data.balance = context.data.total - paid;
    return context;
}

const syncDocs = async (context: HookContext): Promise<HookContext> => {
    const p = context.data.practitioner || context.data.$set?.practitioner;
    if (p) {
        await new CoreCall('visits', context, {skipJoins: true}).patch(context.result.visit, {$addToSet: {practitioners: p}})
            .catch(err => {
                console.error(`Failed to sync visit with claim practitioner. visit_id: ${context.result.visit}, claim_id: ${context.result._id}, err: ${err.message}`)
                return;
            });
    }
    return context;
}

const trackProcedure = async (context: HookContext): Promise<HookContext> => {
    const p = context.data.procedure || context.data.$set?.procedure;
    if (p) {
        await loadOrCreate(`plan_procedures:plans:${context.result.plan}`, {procedures: p})(context);
    }
    return context;
}

const getDate = async (context: HookContext): Promise<HookContext> => {
    if (!context.data.date) {
        const visit = await new CoreCall('visits', context, {skipJoins: true}).get(context.data.visit);
        context.data.date = visit.date || new Date();
    }
    return context;
}

import {getClaimPrice} from './utils/index.js';

const syncVisitTotals = (claim: any) => {
    return async (context: HookContext): Promise<HookContext> => {
        if (!claim?._id) return context;
        context.params.synced_visit = await new CoreCall('visits', context, {skipJoins: true}).patch(claim.visit, {
            balanceSyncedAt: new Date()
        })
            .catch(err => console.error(`Could not update visit total on claim update: ${err.message}`))
        return context;
    }
}
const totalClaims = async (context: HookContext): Promise<HookContext> => {
    const obj = {...context.data, ...context.data.$set || {}}
    const check = ['amount', 'qty', 'paid', 'taxes', 'fees'];
    const keys = Object.keys(obj);
    let run = false;
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        for (let j = 0; j < check.length; j++) {
            if (key.startsWith(check[j])) {
                run = true;
                break;
            }
        }
        if (run) break;
    }
    if (run) await syncVisitTotals(context.result)(context);
    return context;
}

const getSyncedClaim = (claim: any) => {
    return async (context: HookContext): Promise<HookContext> => {

        if (claim.payments && !context.params.skip_hooks) {
            const payments = await new CoreCall('claim-payments', context)._find({
                skip_hooks: true, admin_pass: true,
                query: {
                    deleted: { $ne: true },
                    _id: {
                        $in: claim.payments.map(a => typeof a === 'string' ? ObjectId.createFromHexString(a) : a)
                    },
                    status: {$ne: 'cancelled'}
                },
                paginate: false,
                pipeline: [
                    {
                        $unwind: 'refunds'
                    },
                    {
                        $match: {
                            'refunds.status': 'complete'
                        }
                    },
                    {
                        $group: {
                            _id: '$_id',
                            refund_coins: {$sum: '$refunds.coins'},
                            refund_ded: {$sum: '$refunds.ded'},
                            refund_amount: {$sum: '$refunds.amount'}
                        }
                    },
                    {
                        $group: {
                            _id: '$status',
                            payments_amount: {$sum: 'amount'},
                            payments_coins: {$sum: 'coins'},
                            payments_ded: {$sum: 'ded'},
                            payments_refunded: {$sum: '$refund_amount'},
                            payments_coins_refunded: {$sum: '$refund_coins'},
                            payments_ded_refunded: {$sum: '$refund_ded'},
                        }
                    }
                ]
            })
            if (payments.length) {
                const patchObj: any = {
                    balanceSyncedAt: new Date(),
                }
                let run;
                const setPath = (path: string) => {
                    const res = payments.filter(a => a._id === path)[0];
                    if (res) {
                        const amount = res.payments_amount - res.payments_refunded;
                        const ded = (res.payments_ded || 0) - (res.payments_ded_refunded || 0)
                        const coins = (res.payments_coins || 0) - (res.payments_coins_refunded || 0);
                        const {ded: cDed, coins: cCoins, amount: cAmount} = claim[path] || {}
                        if (amount !== cAmount || ded !== cDed || coins !== cCoins) {
                            run = true;
                            patchObj[path] = {
                                amount,
                                ded,
                                coins
                            }
                        }
                    }
                }
                const statuses = ['paid', 'pending', 'request', 'offer']
                for (let i = 0; i < statuses.length; i++) {
                    setPath(statuses[i]);
                }
                if (run) {
                    const price = getClaimPrice(claim).total;
                    const paidAmt = (patchObj.paid || claim.paid)?.amount || 0
                    patchObj.balance = price - paidAmt
                    return await new CoreCall('claims', context)._patch(claim._id, {$set: patchObj}, { skip_hooks: true, admin_pass: true})
                        .catch(err => {
                            console.error(`Could not patch claim ${claim._id} in syncing total: ${err.message}`)
                            return claim;
                        })
                }
            }
        }
        return claim;
    }
}
const syncPayments = async (context: HookContext): Promise<HookContext> => {
    const {payments} = context.data.$addToSet || {};
    if (payments) {
        context.result = await getSyncedClaim(context.result)(context);
    }
    return context;
}

const reSyncPayments = async (context: HookContext): Promise<HookContext> => {
    if (context.params.banking?.sync_claims_payments) {
        if (context.method === 'find') {
            if (context.result.total) {
                const runOne = async (claim: any) => {
                    return await getSyncedClaim(claim)(context)
                }
                context.result.data = await Promise.all(context.result.data.map(a => runOne(a)))

                await syncVisitTotals(context.result.data[0])(context)
                    .catch(err => console.error(`Error totaling visit claims after re-syncing payments: ${err.message}`));
            }
        } else {
            context.result = await getSyncedClaim(context.result)(context);
            await syncVisitTotals(context.result)(context)
                .catch(err => console.error(`Error totaling visit claims after re-syncing payments: ${err.message}`));
        }
    }
    return context;
}

import {adjKeys} from './claims.schema.js'
import {ObjectId} from 'mongodb'

export const adjudicate = () => {
    return async (context: HookContext): Promise<HookContext> => {
        if(context.params.skip_hooks) return context;
        const sets = {...context.data.adj, ...context.data.$set?.adj};

        const protect = [['coins', 'coins'], ['ded', 'deductible'], ['amount', 'amount'], ['qty', 'quantity'], ['total', 'total']]

        if (context.params.adj) {
            const ex = await loadExists(context);
            context = setExists(context, ex);

            const caps: Array<[string, string]> = [[`plans:${ex.plan}`, 'planAdmin'], [`plans:${ex.plan}`, 'claimsAdmin'], [`plans:${ex.plan}`, 'medAdmin']]

            const ov = {};
            let run = false;
            for (let i = 0; i < protect.length; i++) {
                const k = protect[i][0]
                if (context.data.$set[`adj.${k}`]) run = true
                else if (sets[k]) run = true;
                else if (context.data.$inc && context.data.$inc[`adj.${k}`]) run = true;
            }
            if (run) {
                //ensure this login can adjudicate claims
                await checkUcan(caps, {adminPass: ['patch'], or: ['patch']})(context)
                    .catch(err => {
                        console.error(`Check ucan failed for login: ${context.params.login._id}: ${err.message}`)
                        throw new Error('Permission to adjudicate claims not granted to this login')
                    })
                for (let i = 0; i < adjKeys.length; i++) {
                    const k = adjKeys[i];
                    ov[k] = _get(ex, `adj.${k}`);
                }
                const exAdj = {...context.data.adj || context.data.$set?.adj}
                const adj = {
                    adjBy: context.params.login._id,
                    adjAt: new Date(),
                    ...exAdj,
                    total: (exAdj.amount || 0) * (exAdj.qty || 0),
                    notes: exAdj.notes ? sanitize(exAdj.notes) : undefined
                }
                context.data.$set = {...context.data.$set, adj}
                context.data = {...context.data, $addToSet: {...context.data.$addToSet, adjHistory: ov}}
            }
        } else {
            for (let i = 0; i < protect.length; i++) {
                if (sets[protect[i][0]]) throw new Error(`Cannot edit ${protect[i][1]} amount without validating adjudication permission`)
                if (sets['adjAt'] || sets['adjBy']) throw new Error('Cannot adjudicate claims without verifying authorization first')
            }
        }
        return context;
    }
}

const claimPrices = async (context: HookContext): Promise<HookContext> => {
    if(context.params.skip_hooks) return context;
    const {claim_prices} = context.params.runJoin || {};
    if (claim_prices) {
        const project = {};
        const arr = Object.keys(claimsSchema.properties);
        for (let i = 0; i < arr.length; i++) {
            project[arr[i]] = 1;
        }
        context.params.pipeline = [
            {
                // Step 2: Perform a lookup in the prices collection to match prices based on
                // provider and either med or procedure from the claim
                $lookup: {
                    from: "prices",
                    let: {
                        providerId: "$provider",
                        medId: "$med",
                        procedureId: "$procedure",
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$eq: ["$provider", "$$providerId"]},
                                        {
                                            $or: [
                                                {$eq: ["$subject", "$$medId"]}, // Match on med if exists
                                                {$eq: ["$subject", "$$procedureId"]}, // Or match on procedure if exists
                                            ],
                                        },
                                    ],
                                },
                            },
                        },
                        {
                            // Only select necessary fields: price, provider, bundle
                            $project: {
                                price: 1,
                                provider: 1,
                                bundle: 1,
                            },
                        },
                    ],
                    as: "matchedPrices",
                },
            },
            {
                // Step 3: Unwind matchedPrices to process each price separately
                $unwind: {
                    path: "$matchedPrices",
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                // Step 4: Perform a lookup in the networks collection based on bundle
                $lookup: {
                    from: "networks",
                    as: "networks",
                    let: {
                        matched_bundle: '$matchedPrices.bundle',
                        matched_plan: '$plan'
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        {$in: ['$$matched_bundle', '$bundles']},
                                        {$in: ['$$matched_plan', '$plans']}
                                    ]
                                }
                            }
                        }
                    ],
                    // localField: "matchedPrices.bundle", // Bundle ID from prices
                    // foreignField: "bundles", // The array of bundles in networks
                },
            },
            {
                // Step 5: Group the results by claim ID and find the lowest price
                $group: {
                    _id: "$_id", // Group by the claim _id
                    claim: {$first: "$$ROOT"}, // Keep the claim information
                    lowestPrice: {$min: "$matchedPrices.price"}, // Find the lowest price
                    networks: {$push: "$networks"} // Collect all networks (still nested arrays)
                },
            },
            {
                // Step 6: Shape the final output as needed
                $project: {
                    ...project,
                    _pipeline: {
                        lowestPrice: "$lowestPrice",
                        networks: {
                            $reduce: {
                                input: '$networks', // Access the first element of networks
                                initialValue: [],
                                in: {
                                    $concatArrays: [
                                        "$$value", // The accumulated value (initially an empty array)
                                        {
                                            $cond: {
                                                if: {$isArray: "$$this"}, // Check if the current value is an array
                                                then: "$$this", // If it is, concatenate it
                                                else: [] // If it's not, return an empty array
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    }
                },
            },
        ]
    }
    return context;
}

const removePipeline = (context: HookContext) => {
    if (context.data._pipeline) delete context.data._pipeline;
    return context;
}


const paths = ['files.*'];
const uploadsConfig = {paths};
// A configure function that registers the service and its hooks via `app.configure`
export const claims = (app: Application) => {
    // Register our service on the Feathers application
    app.use(claimsPath, new ClaimsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: claimsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(claimsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(claimsExternalResolver),
                schemaHooks.resolveResult(claimsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(claimsQueryValidator),
                schemaHooks.resolveQuery(claimsQueryResolver),
                scrubUploads(uploadsConfig)
            ],
            find: [claimPrices],
            get: [],
            create: [
                getDate,
                schemaHooks.validateData(claimsDataValidator),
                schemaHooks.resolveData(claimsDataResolver),
                scrub(['notes']),
                relateVisit,
                addTotal
            ],
            patch: [
                removePipeline,
                schemaHooks.validateData(claimsPatchValidator),
                schemaHooks.resolveData(claimsPatchResolver),
                preventChanges,
                adjudicate(),
                addTotal,
                scrub(['notes']),
                relateVisit
            ],
            remove: [relateVisit]
        },
        after: {
            all: [
                runJoins,
                scrubUploads(uploadsConfig)
            ],
            get: [reSyncPayments],
            find: [reSyncPayments],
            create: [relateVisit, trackProcedure, syncDocs],
            patch: [relateVisit, syncDocs, trackProcedure, syncPayments, totalClaims],
            remove: [relateVisit]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [claimsPath]: ClaimsService
    }
}
