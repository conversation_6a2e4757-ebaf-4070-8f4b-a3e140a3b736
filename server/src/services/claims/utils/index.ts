export const getClaimPrice = (claim:any) => {
    const { subtotal, taxes, fees } = claim || { total: 0 }
    let fee = 0;
    let tax = 0;
    for(const k in taxes || {}){
        tax += taxes[k].amount || 0;
    }
    for(const k in fees || {}){
        fee += fees[k].amount || 0;
    }
    return {
        subtotal: subtotal || 0,
        total: subtotal+fee+tax,
        tax,
        fee
    }
}
const paidSchema = {
    type: 'object',
    properties: {
        amount: { type: 'number' },
        ded: {type: 'number'},
        coins: {type: 'number'}
    }
} as const

export const paids = {
    type: 'object',
    properties: {
        pending: paidSchema,
        request: paidSchema,
        offer: paidSchema,
        paid: paidSchema,
    }
} as const
