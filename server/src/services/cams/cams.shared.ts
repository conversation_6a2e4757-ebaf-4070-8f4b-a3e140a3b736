// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Cams, CamsData, CamsPatch, CamsQuery, CamsService } from './cams.class.js'

export type { Cams, CamsData, CamsPatch, CamsQuery }

export type CamsClientService = Pick<CamsService<Params<CamsQuery>>, (typeof camsMethods)[number]>

export const camsPath = 'cams'

export const camsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const camsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(camsPath, connection.service(camsPath), {
    methods: camsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [camsPath]: CamsClientService
  }
}
