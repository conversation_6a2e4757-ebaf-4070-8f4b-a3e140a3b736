// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import type {Application, HookContext} from '../../declarations.js'
import {BankingService, getOptions} from './banking.class.js'
import {bankingPath, bankingMethods} from './banking.shared.js'

export * from './banking.class.js'

import {bankLookup} from './services/moov/index.js';

const routingLookup = async (context: HookContext): Promise<HookContext> => {
    if (context.params.banking?.lookup?.routing) {
        return bankLookup(context);
    }
    return context;
}

import * as methods from './services/moov/index.js';

const handleMoov = async (context: HookContext): Promise<HookContext> => {
    const {moov} = context.params.banking || {};
    if (moov) {
        // const methods = {
        //     account_setup,
        //     accounts_create,
        //     account_update,
        //     account_link,
        //     account_get,
        //     add_capabilities,
        //     get_account_link,
        //     get_mcc,
        //     get_person,
        //     list_persons,
        //     create_person,
        //     update_person,
        //     delete_person,
        //     create_external_account,
        //     update_external_account,
        //     delete_external_account,
        //     create_financial_account,
        //     update_financial_account,
        //     get_financial_account,
        //     get_multiple_fas,
        //     add_account_pm,
        //     inbound_transfer,
        //     list_transactions,
        //     create_card,
        //     update_card,
        //     create_cardholder,
        //     update_cardholder
        // }
        return methods[moov.method](...moov.args)(context)
    }
    return context;
}

// TODO: incorporate an active org channel update flow for admins on orgs
// import hmacSHA512 from 'crypto-js/hmac-sha512';
import {createHmac} from 'crypto';

const restMiddleware = (app: any) => {
    return async (req, res, next) => {
        const timestamp = req.headers['X-Timestamp'] || req.headers['x-timestamp'];
        const signature = req.headers['X-Signature'] || req.headers['x-signature'];
        const nonce = req.headers['X-Nonce'] || req.headers['x-nonce'];
        const webhookId = req.headers['X-Webhook-ID'] || req.headers['x-webhook-id'];

        const {data} = req.body;
        if (data?.type === 'walletTransaction.updated') {
            const webhookSecret = app.get('banking').moov.wallet_webhook_secret;
            const signString = `${timestamp}|${nonce}|${webhookId}`
            let checkHash;
            try {
                checkHash = createHmac('sha512', webhookSecret)
                    .update(signString)
                    .digest('hex');
            } catch (error) {
                console.log('Error creating hmac', error);
                return next(null, {
                    status: 401,
                    message: 'Signature invalid'
                })
            }
            const match = signature === checkHash;

            if (!match) return next(null, {
                status: 401,
                message: 'Signature invalid'
            })

            let webhook;
            try {
                webhook = JSON.parse(res.body);
            } catch (err) {
                console.log("Invalid JSON for moov webhook");
                next(null, {
                    statusCode: 400,
                    body: "Invalid JSON"
                });
            }

            const accountID = data.data.accountID;
            const transationID = data.data.transactionID;
            const walletID = data.data.walletID;
            const issedCardID = data.data.issuedCardID;
            if (transationID) {
                const transaction = await app.service('banking').get(data.data.accountID, {
                    banking: {
                        moov: {
                            method: 'get_spend_transaction',
                            args: [transationID]
                        }
                    }
                })
                //Possible transaction types: account-funding, ach-reversal, auto-sweep, card-payment, card-decline, card-reversal, cash-out, dispute, dispute-reversal, facilitator-fee, issuing-refund, issuing-transaction, issuing-transaction-adjustment, issuing-auth-hold, issuing-auth-release, issuing-decline, moov-fee, payment, payout, refund, refund-failure, rtp-failure, top-up, wallet-transfer, adjustment

                /**
                 * https://docs.moov.io/guides/issue-cards/manage-issued-cards/
                 * Card issuing activity can produce the following types of wallet transactions:

                 issuing-auth-hold: Funds are reserved on the wallet balance due to an approved authorization. Any incremental authorizations result in additional wallet transactions reflecting the amount of the increment.
                 issuing-auth-release: Held funds are released back to the wallet due to an authorization reversal, expiration, or when the authorization is cleared.
                 issuing-transaction: Reflects funds moving out of the wallet from a cleared authorization, a refund, or a force post.*/

                const checkTransaction = async () => {
                    const careCard = await app.service('care-cards').find({
                        query: {
                            $limit: 1,
                            moov_card: transaction.issuedCardID
                        },
                        admin_pass: true,
                        skip_hooks: true
                    })

                    const amt = data.data.amount || data.data.transactionAmount;
                    if (amt > 0) next(null, {status: 200, message: 'Transaction approved'})

                    if (careCard.amount - (careCard.spent || 0) - (amt || 0) * 100 < 0) {
                        await app.service('care-cards').patch(careCard._id, {status: 'closed'})
                        next(null, {status: 400, message: 'Transaction exceeded balance'})
                    }

                    next(null, {status: 200, message: 'Transaction approved'})
                }

                const transactionTypeFns = {
                    'issuing-auth-hold': checkTransaction,
                    'issuing-transaction': checkTransaction
                }

                const fn = transactionTypeFns[transaction.transactionType]
                if (fn) await fn();

            } else next(null, {
                status: 400,
                message: 'No transaction ID provided'
            })
        }

        next(null, {
            status: 200,
            message: 'Webhook received'
        })
    }
}


// A configure function that registers the service and its hooks via `app.configure`
export const banking = (app: Application) => {
    // Register our service on the Feathers application
    app.use(bankingPath,
        restMiddleware(app),
        new BankingService(getOptions(app)), {
            // A list of all methods this service exposes externally
            methods: bankingMethods,
            // You can add additional custom events to be sent to clients here
            events: []
        })
    // Initialize hooks
    app.service(bankingPath).hooks({
        around: {
            all: []
        },
        before: {
            all: [handleMoov],
            find: [routingLookup],
            get: [],
            create: [],
            patch: [],
            remove: []
        },
        after: {
            all: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [bankingPath]: any
    }
}
