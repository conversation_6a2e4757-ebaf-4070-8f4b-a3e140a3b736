// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#custom-services
import type { Id, NullableId, Params, ServiceInterface } from '@feathersjs/feathers'

import type { Application } from '../../declarations.js'

type Banking = any
type BankingData = any
type BankingPatch = any
type BankingQuery = any

export type { Banking, BankingData, BankingPatch, BankingQuery }

export interface BankingServiceOptions {
  app: Application
}

export interface BankingParams extends Params<BankingQuery> {}

// This is a skeleton for a custom service class. Remove or add the methods you need here
export class BankingService<ServiceParams extends BankingParams = BankingParams>
  implements ServiceInterface<Banking, BankingData, ServiceParams, BankingPatch>
{
  constructor(public options: BankingServiceOptions) {}

  async find(_params?: ServiceParams): Promise<Banking[]> {
    return []
  }

  async get(id: Id, _params?: ServiceParams): Promise<Banking> {
    return {
      id: 0,
      text: `A new message with ID: ${id}!`
    }
  }

  async create(data: BankingData, params?: ServiceParams): Promise<Banking>
  async create(data: BankingData[], params?: ServiceParams): Promise<Banking[]>
  async create(data: BankingData | BankingData[], params?: ServiceParams): Promise<Banking | Banking[]> {
    if (Array.isArray(data)) {
      return Promise.all(data.map((current) => this.create(current, params)))
    }

    return {
      id: 0,
      ...data
    }
  }

  // This method has to be added to the 'methods' option to make it available to clients
  async update(id: NullableId, data: BankingData, _params?: ServiceParams): Promise<Banking> {
    return {
      id: 0,
      ...data
    }
  }

  async patch(id: NullableId, data: BankingPatch, _params?: ServiceParams): Promise<Banking> {
    return {
      id: 0,
      text: `Fallback for ${id}`,
      ...data
    }
  }

  async remove(id: NullableId, _params?: ServiceParams): Promise<Banking> {
    return {
      id: 0,
      text: 'removed'
    }
  }
}

export const getOptions = (app: Application) => {
  return { app }
}
