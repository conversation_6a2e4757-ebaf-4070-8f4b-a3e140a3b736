// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  <PERSON><PERSON>Geo<PERSON>,
  TomtomGeocodeData,
  TomtomGeocodePatch,
  TomtomGeocodeQuery,
  TomtomGeocodeService
} from './tomtom-geocode.class.js'

export type { TomtomGeocode, TomtomGeocodeData, TomtomGeocodePatch, TomtomGeocodeQuery }

export type TomtomGeocodeClientService = Pick<
  TomtomGeocodeService<Params<TomtomGeocodeQuery>>,
  (typeof tomtomGeocodeMethods)[number]
>

export const tomtomGeocodePath = 'tomtom-geocode'

export const tomtomGeocodeMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const tomtomGeocodeClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(tomtomGeocodePath, connection.service(tomtomGeocodePath), {
    methods: tomtomGeocodeMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [tomtomGeocodePath]: TomtomGeocodeClientService
  }
}
