// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  DocTemplates,
  DocTemplatesData,
  DocTemplatesPatch,
  DocTemplatesQuery,
  DocTemplatesService
} from './doc-templates.class.js'

export type { DocTemplates, DocTemplatesData, DocTemplatesPatch, DocTemplatesQuery }

export type DocTemplatesClientService = Pick<
  DocTemplatesService<Params<DocTemplatesQuery>>,
  (typeof docTemplatesMethods)[number]
>

export const docTemplatesPath = 'doc-templates'

export const docTemplatesMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const docTemplatesClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(docTemplatesPath, connection.service(docTemplatesPath), {
    methods: docTemplatesMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [docTemplatesPath]: DocTemplatesClientService
  }
}
