// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Cobras, CobrasData, CobrasPatch, CobrasQuery, CobrasService } from './cobras.class.js'

export type { Cobras, CobrasData, CobrasPatch, CobrasQuery }

export type CobrasClientService = Pick<CobrasService<Params<CobrasQuery>>, (typeof cobrasMethods)[number]>

export const cobrasPath = 'cobras'

export const cobrasMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const cobrasClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(cobrasPath, connection.service(cobrasPath), {
    methods: cobrasMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [cobrasPath]: CobrasClientService
  }
}
