// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    careCardsDataValidator,
    careCardsPatchValidator,
    careCardsQueryValidator,
    careCardsResolver,
    careCardsExternalResolver,
    careCardsDataResolver,
    careCardsPatchResolver,
    careCardsQueryResolver
} from './care-cards.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {CareCardsService, getOptions} from './care-cards.class.js'
import {careCardsPath, careCardsMethods} from './care-cards.shared.js'
import {allUcanAuth, anyAuth, CapabilityParts, CoreCall, getExists, loadExists, setExists} from 'feathers-ucan';
import {getJoin, logChange, relate} from '../../utils/index.js';
import {looseRelateUsers} from '../budgets/hooks/index.js';
import {cascadeSpent} from '../budgets/hooks/index.js';

export * from './care-cards.class.js'
export * from './care-cards.schema.js'

const authenticate = async (context: HookContext) => {
    const writer = [['accounts', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['accounts', '*']] as Array<CapabilityParts>;
    const ucanArgs = {
        get: writer,
        find: writer,
        create: anyAuth,
        patch: writer,
        update: writer,
        remove: deleter
    };
    const cap_subjects: any = []
    if (context.data?.closeMemo) context.params.closeMemo = context.data.closeMemo
    if (context.method !== 'create' && !context.params.admin_pass && !context.params.loopingAuth) {
        const existing = await loadExists(context, {params: {admin_pass: true, loopingAuth: true}});
        context = setExists(context, existing);
        //allow changes before approval
        if (existing) {
            const orgNamespace = `orgs:${existing.owner}`;
            cap_subjects.push(existing.owner);
            const orgWrite: CapabilityParts[] = [[orgNamespace, 'WRITE'], [orgNamespace, 'orgAdmin'], [orgNamespace, 'providerAdmin'], [orgNamespace, 'WRITE']]
            for (const w of orgWrite) {
                ucanArgs.get.unshift(w);
                ucanArgs.find.unshift(w);
                ucanArgs.patch.unshift(w);
                ucanArgs.update.unshift(w);
                ucanArgs.remove.unshift(w);
            }
            // if((existing.managers || []).includes(context.params.login._id)) context.params.admin_pass = true;
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['get', 'find', 'patch', 'create', 'remove'],
        loginPass: [[['managers/owner'], '*']],
        cap_subjects
    })(context) as any;
}

const relateBudget = async (context: HookContext) => {
    return relate('otm', {
        herePath: 'budget',
        therePath: 'cards',
        thereService: 'budgets',
        paramsName: 'cardBudget'
    })(context);
}

const addMoovCard = async (context: HookContext) => {
    const person = await new CoreCall('ppls', context, {skipJoins: true}).get(context.data.cardholder, {query: {$select: ['stripeCardholderId']}})
    const budget = await new CoreCall('budgets', context, {skipJoins: true}).get(context.data.budget, {runJoin: {budget_parent: true}})
    const card: any = {
        authorizedUser: {
            fundingWalletID: budget._fastjoin.careAccount.wallet_id,
            authorizedUser: {
                firstName: person.firstName,
                lastName: person.lastName,
            }
        }
    }
    if (person.dob) {
        const dob = new Date(person.dob);
        card.authorizedUser.birthDate = {
            day: dob.getDate(),
            month: dob.getMonth() + 1,
            year: dob.getFullYear()
        }
    }

    if (context.data.singleUse) card.controls = {singleUse: true}
    card.controls = {
        ...card.controls,
        velocityLimits: [{
            amount: Math.min(context.data.perTransactionLimit || 0, budget.amount || 0),
            interval: 'per-transaction'
        }]
    }

    const moov_card = await new CoreCall('banking', context).get(context.data.moov_id, {
        banking: {
            moov: {
                method: 'issue_card',
                args: [card]
            }
        }
    })
    context.params.banking = {
        ...context.params.banking,
        moov_card
    }
    context.data.moov_card = context.params.banking.moov_card.id;
    context.data.last4 = context.params.banking.stripe_card.last4
    return context;
}

/** attempt to $inc the budget for the amount increase or status update */
const incBudget = async (context: HookContext) => {
    const {$inc, updatedAt, updatedBy, updatedByHistory, status, $set, budget} = context.data || {}
    const newStatus = status || $set?.status
    const newBudget = budget || $set?.newBudget
    let {amount = 0, recurs = 0} = $inc || {};
    if (!context.params.skip_hooks && (amount || recurs || newStatus || newBudget)) {
        const handleError = async (err: any, message: string, parentId?: string) => {
            console.error(`Error adding card amount | ${context.id} | ${message} | ${err.message}`)
            if (parentId) await new CoreCall('budgets', context)._patch(parentId as any, {runSync: new Date()}, {
                skip_hooks: true,
                admin_pass: true
            })
            throw new Error(err.message)
        }
        const patchObj: any = {$inc: {}}
        const existing: any = await loadExists(context)
            .catch(err => handleError(err, 'loading existing'));
        context = setExists(context, existing);
        const sTatus = newStatus || existing.status;
        if (sTatus === 'active') {
            //limit changes when incrementing amount - to limit changes to other properties that might cause balances to get out of sync
            context.data = {$inc: {}, updatedAt, updatedBy, updatedByHistory}
            if (newStatus && newStatus !== existing.status) {
                context.data.status = newStatus;
                amount += newStatus === 'active' ? existing.amount || 0 : (existing.amount || 0) * -1
                recurs += newStatus === 'active' ? existing.recurs || 0 : (existing.recurs || 0) * -1
            }
            //prepare patchObj to assign amount and/or recurs to parent or careAccount
            if (amount) {
                patchObj.$inc.assigned_amount = amount;
                context.data.$inc.amount = amount
            }
            if (recurs) {
                patchObj.$inc.assigned_recurs = recurs;
                context.data.$inc.recurs = recurs;
            }
            if (newBudget && String(newBudget) !== String(existing.budget)) {
                //ON BUDGET CHANGE
                context.data.budget = newBudget;
                amount += existing.amount || 0
                recurs += existing.recurs || 0
                await new CoreCall('budgets', context, {skipJoins: true}).patch(newBudget, {
                    $inc: {
                        assigned_recurs: recurs,
                        assigned_amount: amount
                    }
                }, {admin_pass: true})
                patchObj.$inc.assigned_recurs = recurs * -1;
                patchObj.$inc.assigned_amount = amount * -1;
            }
            await new CoreCall('budgets', context, {skipJoins: true}).patch(existing.budget, patchObj, {admin_pass: true})
                .catch(err => handleError(err, 'patching parent', existing.budget))

            /** make sure the amount being added doesn't exceed the card limit - otherwise the card needs to be re-added
             * TODO: consider automatically adding a new card if the budget balance exists
             */
            const incAmount = context.data.$inc?.amount
            if (incAmount && incAmount > 0) {
                const card = context.params.banking?.moov_card || await new CoreCall('banking', context).get(context.result.moov_id, {
                    banking: {
                        moov: {
                            method: 'get_card',
                            args: [context.result.moov_card]
                        }
                    }
                })
                if ((card.controls?.velocityLimits || []).sort((a, b) => b.amount - a.amount)[0]?.amount < ((context.result.amount || 0) + incAmount)) throw new Error('Card limit is too low to increase amount - issue a new card')
            }
        }
    }
    if (newStatus) {
        await new CoreCall('banking', context).get(context.result.moov_id, {
            banking: {
                moov: {
                    method: 'update_card',
                    args: [context.result.moov_card, {state: newStatus}]
                }
            }
        })
            .catch(err => console.error(`Error updating card status: ${err.message}`))
    }
    return context;
}

const deactivate = async (context: HookContext) => {
    if ((context.data.status || context.data.$set?.status) === 'closed') {
        const ex = await loadExists(context);
        context = setExists(context, ex);

        const closeCard = async () => {
            await new CoreCall('banking', context).get(context.result.moov_id, {
                banking: {
                    moov: {
                        method: 'update_card',
                        args: [context.result.moov_card, {state: 'closed'}]
                    }
                }
            })
            context.data.$addToSet = {
                closedCardIds: context.result.moov_card,
                closedCards: {
                    id: context.result.moov_card,
                    closedAt: new Date(),
                    memo: context.params.closeMemo || 'No memo provided'
                }
            }
            return;
        }
        if (ex.status !== 'closed') {
            await closeCard()
        } else {
            const card = await new CoreCall('banking', context).get(context.result.moov_id, {
                banking: {
                    moov: {
                        method: 'get_card',
                        args: [context.result.moov_card]
                    }
                }
            })
            if (card.state !== 'closed') await closeCard()
        }
    }
    return context;

}

const runJoins = async (context: HookContext) => {
    const {card_holder, card_budget, card_budget_owner, card_moov_card, all_parents} = context.params.runJoin || {}
    if (card_moov_card && context.result.moov_card) {
        const card = await new CoreCall('banking', context).get(context.result.moov_id as any, {
            banking: {
                moov: {
                    method: 'get_card',
                    args: [context.result.moov_card]
                }
            }
        })
            .catch(err => console.error(`Error joining stripe card to care card: ${err.message}`))
        context.result._fastjoin = {...context.result._fastjoin, moov_card: card}
    }
    if (card_holder) return getJoin({herePath: 'cardholder', service: 'ppls'})(context)
    if (card_budget_owner) {
        return getJoin({herePath: 'budget', service: 'budgets', params: {runJoin: {budget_owner: true}}})(context)
    }
    if (card_budget) return getJoin({herePath: 'budget', service: 'budgets'})(context)
    if (all_parents) return getJoin({
        herePath: 'budget',
        service: 'budgets',
        params: {runJoin: {all_parents: true}}
    })(context)

    return context;
}

const cascadeSpentCard = async (context: HookContext) => {
    const {lastSync} = context.data || {};
    if (lastSync) {
        context = await cascadeSpent(true)(context);
        if (lastSync) context.data.lastSync = lastSync
        return context;
    }
    return context;
}

const syncTransactions = async (context: HookContext) => {
    if (context.params.banking?.moov?.sync_transactions) {
        const loadTransactions = async (addQuery: any) => {
            let fromDate = new Date().setDate(1);
            if (context.result.lastSync) {
                const syncDate = new Date(context.result.value.lastSync).getTime();
                if (syncDate > fromDate) fromDate = syncDate;
            }
            const q = {
                count: 100,
                startDateTime: new Date(fromDate).toISOString(),
                issuedCardID: context.result.moov_card, ...addQuery
            }
            return await new CoreCall('banking', context).get(context.result.moov_id, {
                banking: {
                    moov: {
                        method: 'list_spend_card_transactions',
                        args: [q]
                    }
                }
            })
        }

        /** list of transactions ids to check against pending authorizations */

        let patchObj: any;
        let spent_sync = 0;
        /** go get all transactions since last sync */
        const iterativeSyncTransactions = async (data: any[] = []) => {
            const addQuery: any = {};
            if (data.length) addQuery.startDateTime = data[data.length - 1].createdOn;
            let res = await loadTransactions(addQuery)
            res.data = [...data, ...res.data.filter(a => !(data || []).map(a => a.cardTransactionID).includes(a.cardTransactionID))]
            if (res.has_more) return await iterativeSyncTransactions(res.data);
            else {
                for (const t of res.data) {
                    spent_sync -= (t.amount || 0) * 100;
                }
            }
        }

        await iterativeSyncTransactions([])

        const pending_auths = await new CoreCall('banking', context).get(context.result.moov_id, {
            banking: {
                moov: {
                    method: 'list_spend_card_auths',
                    args: [{count: 200, issuedCardID: context.result.moov_card, statuses: ['pending']}]
                }
            }
        })

        let pending_sync = 0;
        for (const pt of pending_auths.data) {
            pending_sync -= (pt.authorizedAmount || 0) * 100;
        }

        const budget = await new CoreCall('budgets', context, {skipJoins: true}).get(context.result.budget, {runJoin: {budget_parent: true}})
        /** not to exceed */
        const budgetBalance = (budget.amount || 0) - (budget.spent || 0) - (budget.spent_pending || 0)
        const cardBalance = (context.result.amount || 0);
        const nte = Math.min(budgetBalance, cardBalance);

        if (spent_sync + pending_sync > nte) {
            if (!patchObj) patchObj = {};
            patchObj.status = 'closed';
            /** close card if balance is exceeded */
            await new CoreCall('banking', context).get(context.result.moov_id, {
                banking: {
                    moov: {
                        method: 'update_card',
                        args: [context.result.moov_card, {state: 'closed'}]
                    }
                }
            })
                .catch(err => {
                    console.error(`Error closing card ${context.result._id} - issuedCardID: ${context.result.moov_card}: ${err.message}`)
                    throw new Error(`Card spend exceeded budget - closing card recommended`)
                })
            /** log card closure */
            patchObj.$addToSet = {
                closedCardIds: context.result.moov_card,
                closedCards: {
                    id: context.result.moov_card,
                    closedAt: new Date(),
                    memo: 'Budget balance was exceeded transactions/pending transactions'
                }
            }

        }

        let {spent, spent_pending} = context.result
        if (!spent || isNaN(spent)) spent = 0;
        if (!spent_pending || isNaN(spent_pending)) spent_pending = 0;
        if (spent_sync !== spent) {
            if (!patchObj) patchObj = {};
            patchObj.$inc = {spent: spent * -1}
        }
        if (pending_sync !== spent_pending) {
            if (!patchObj) patchObj = {$set: {}}
            patchObj.$inc = {...patchObj.$inc, spent_pending: spent_pending * -1}
        }

        if (patchObj) {
            patchObj.lastSync = new Date();
            context.result = await new CoreCall('care-cards', context).patch(context.result._id as any, patchObj)
                .catch(err => {
                    console.error(`Could not sync spent from transactions: ${err.message}`)
                    throw new Error(`Could not sync spent from transactions: ${err.message}`)
                });
        }
    }
    return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const careCards = (app: Application) => {
    // Register our service on the Feathers application
    app.use(careCardsPath, new CareCardsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: careCardsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(careCardsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(careCardsExternalResolver),
                schemaHooks.resolveResult(careCardsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(careCardsQueryValidator),
                schemaHooks.resolveQuery(careCardsQueryResolver)
            ],
            find: [],
            get: [],
            create: [
                schemaHooks.validateData(careCardsDataValidator),
                schemaHooks.resolveData(careCardsDataResolver),
                addMoovCard,
                relateBudget
            ],
            patch: [
                schemaHooks.validateData(careCardsPatchValidator),
                schemaHooks.resolveData(careCardsPatchResolver),
                deactivate,
                cascadeSpentCard,
                incBudget,
                relateBudget
            ],
            remove: [relateBudget]
        },
        after: {
            all: [runJoins],
            get: [syncTransactions],
            create: [looseRelateUsers('card_user'), relateBudget],
            patch: [looseRelateUsers('card_user'), relateBudget],
            remove: [looseRelateUsers('card_user'), relateBudget],
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations' {
    interface ServiceTypes {
        [careCardsPath]: CareCardsService
    }
}
