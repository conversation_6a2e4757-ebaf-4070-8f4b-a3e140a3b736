// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, commonQueries, pull} from '../../utils/common/schemas.js';

const closedCardSchema = {
    type: 'object', properties: {
        id: {type: 'string'},
        closedAt: {},
        memo: {type: 'string'}
    }
} as const
// Main data model schema
export const careCardsSchema = {
    $id: 'CareCards',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'name', 'owner', 'budget', 'cardholder', 'moov_id'],
    properties: {
        _id: ObjectIdSchema(),
        amount: {type: 'number'},
        singleUse: {type: 'boolean'},
        singleVendor: { type: 'boolean' },
        perTransactionLimit: {type: 'number'},
        approvers: {type: 'array', items: ObjectIdSchema()},
        connect_id: {type: 'string'},
        moov_id: {type: 'string'},
        budget: ObjectIdSchema(),
        cardholder: ObjectIdSchema(),
        closedCardIds: {type: 'array', items: {type: 'string'}},
        closedCards: {
            type: 'array', items: closedCardSchema
        },
        last4: {type: 'string'},
        lastSync: {},
        managers: {type: 'array', items: ObjectIdSchema()},
        mcc_whitelist: {type: 'array', items: {type: 'string'}},
        mcc_blacklist: {type: 'array', items: {type: 'string'}},
        members: {type: 'array', items: ObjectIdSchema()},
        name: {type: 'string'},
        owner: ObjectIdSchema(),
        preAuth: {type: 'number'},
        recurs: {type: 'number'},
        spent: {type: 'number'},
        spent_pending: {type: 'number'},
        status: {type: 'string', enum: ['active', 'inactive', 'canceled', 'pending-verification']},
        moov_card: {type: 'string'},
        stripe_card: {type: 'string'},
        users: {type: 'array', items: ObjectIdSchema()},
        ...commonFields.properties
    }
} as const
export type CareCards = FromSchema<typeof careCardsSchema>
export const careCardsValidator = getValidator(careCardsSchema, dataValidator)
export const careCardsResolver = resolve<CareCards, HookContext>({})

export const careCardsExternalResolver = resolve<CareCards, HookContext>({})

const {amount, recurs, ...createPatchSchema} = careCardsSchema.properties;
// Schema for creating new data
export const careCardsDataSchema = {
    $id: 'CareCardsData',
    type: 'object',
    additionalProperties: false,
    required: ['name', 'owner', 'budget', 'cardholder', 'moov_id'],
    properties: {
        ...createPatchSchema
    }
} as const
export type CareCardsData = FromSchema<typeof careCardsDataSchema>
export const careCardsDataValidator = getValidator(careCardsDataSchema, dataValidator)
export const careCardsDataResolver = resolve<CareCardsData, HookContext>({
    members: async (val, data) => {
        return Array.from(new Set([...val || [], data.cardholder]))
    },
    status: async (val) => {
        if (val) return val;
        return 'active'
    }
})
const listArgs = [
    {path: 'mcc_whitelist', type: {type: 'string'}},
    {path: 'mcc_blacklist', type: {type: 'string'}},
    {path: 'closedCards', type: closedCardSchema},
    {path: 'closedCardIds', type: {type: 'string'}}
]
// Schema for updating existing data
export const careCardsPatchSchema = {
    $id: 'CareCardsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...createPatchSchema,
        ...commonPatch(createPatchSchema).properties,
        $inc: {},
        $addToSet: addToSet(listArgs),
        $pull: pull(listArgs)
    }
} as const
export type CareCardsPatch = FromSchema<typeof careCardsPatchSchema>
export const careCardsPatchValidator = getValidator(careCardsPatchSchema, dataValidator)
export const careCardsPatchResolver = resolve<CareCardsPatch, HookContext>({})

// Schema for allowed query properties
export const careCardsQuerySchema = {
    $id: 'CareCardsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax({...careCardsSchema.properties, ...commonQueries.properties}),
        name: {}

    }
} as const
export type CareCardsQuery = FromSchema<typeof careCardsQuerySchema>
export const careCardsQueryValidator = getValidator(careCardsQuerySchema, queryValidator)
export const careCardsQueryResolver = resolve<CareCardsQuery, HookContext>({})
