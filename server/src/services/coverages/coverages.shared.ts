// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Coverages, CoveragesData, CoveragesPatch, CoveragesQuery, CoveragesService } from './coverages.class.js'

export type { Coverages, CoveragesData, CoveragesPatch, CoveragesQuery }

export type CoveragesClientService = Pick<CoveragesService<Params<CoveragesQuery>>, (typeof coveragesMethods)[number]>

export const coveragesPath = 'coverages'

export const coveragesMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const coveragesClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(coveragesPath, connection.service(coveragesPath), {
    methods: coveragesMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [coveragesPath]: CoveragesClientService
  }
}
