export const hs_financials_table = (financials:any) => {
    const headers = [
        'Year',
        'Total Revenue',
        '<PERSON>ng (Expense)',
        'Admin Expense',
        'Total Expense',
        'Profit',
        'Profit Margin',
        'MLR',
        'Admin %',
        'Net Assets (Start)',
        'Net Assets (End)',
        'Asset Growth',
        'Cash on Hand (End)',
        '% of Assets as Cash',
        'Assets as % of Expense',
        'Cash as % of Expense',
        'Assets as % of Medical Bills',
        'Cash as % of Medical Bills',
        'Highest Paid Executive'
    ];

    const rows = [headers.join(' | '), headers.map(() => '---').join(' | ')];

    const format = (num) => isFinite(num) ? num.toFixed(2) : '';

    Object.keys(financials).sort().forEach(year => {
        const f = financials[year];
        const totalRevenue = f.total_revenue || 0;
        const sharingExpense = f.sharing_expense || 0;
        const adminExpense = f.admin_expense || 0;
        const totalExpense = sharingExpense + adminExpense;
        const profit = totalRevenue - totalExpense;

        const row = [
            year,
            format(totalRevenue),
            format(sharingExpense),
            format(adminExpense),
            format(totalExpense),
            format(profit),
            format(totalRevenue ? (profit / totalRevenue) * 100 : 0) + '%',
            format(totalRevenue ? (sharingExpense / totalRevenue) * 100 : 0) + '%',
            format(totalRevenue ? (adminExpense / totalRevenue) * 100 : 0) + '%',
            format(f.net_assets_start || 0),
            format(f.net_assets || 0),
            format((f.net_assets || 0) - (f.net_assets_start || 0)),
            format(f.cash_on_hand || 0),
            format((f.net_assets || 0) ? (f.cash_on_hand || 0) / f.net_assets * 100 : 0) + '%',
            format(totalExpense ? (f.net_assets || 0) / totalExpense * 100 : 0) + '%',
            format(totalExpense ? (f.cash_on_hand || 0) / totalExpense * 100 : 0) + '%',
            format(sharingExpense ? (f.net_assets || 0) / sharingExpense * 100 : 0) + '%',
            format(sharingExpense ? (f.cash_on_hand || 0) / sharingExpense * 100 : 0) + '%',
            format(f.highest_paid_executive || 0)
        ];
        rows.push(row.join(' | '));
    });

    return rows.join('\n');
}
