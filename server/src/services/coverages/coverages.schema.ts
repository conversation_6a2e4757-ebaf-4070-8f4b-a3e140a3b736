// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {
    addToSet,
    commonFields, commonPatch,
    commonQueries,
    geoJsonSchema,
    imageSchema,
    pull,
    videoSchema
} from '../../utils/common/schemas.js';
import {costLimits} from '../plans/utils/rules.js';
import {benSchema, coins_categories} from '../marketplace/utils/index.js';

export const rateSchema = {
    type: 'object',
    patternProperties: {
        '^(1?[0-9]|20)$': {type: 'number'}
    }
} as const;

const rateByAge = {
    type: 'object',
    $comment: 'flat rate by age - key is the age, value is the rate. This is how ACA plans are priced. For multi-person discounts use multiDiscount.',
    patternProperties: {
        "^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": {type: 'number'} /** any key is the age and the value is the rate */
    }
} as const;

export const fixedRate = {
    type: 'object', properties: {
        single: {type: 'number', $comment: 'rate for a single person'},
        plus_spouse: {type: 'number', $comment: 'rate for a single person plus a spouse'},
        plus_child: {type: 'number', $comment: 'rate for a single person plus a child'},
        plus_child__2: {type: 'number', $comment: 'rate for a single person plus 2 children'},
        plus_child__3: {type: 'number', $comment: 'rate for a single person plus 3 children'},
        family: {type: 'number', $comment: 'rate for a family'}
    }
} as const
const fixedRates = {
    type: 'object',
    $comment: 'this is the structure for coverages that set the premium based on the oldest/head of household age - a simpler way of calculating rates vs age of every household member see fixedRates schema.',
    patternProperties: {
        "^([0-9]|[1-9][0-9]|1[01][0-9]|120)$": fixedRate
    }
} as const

const premiumStructures = {
    type: 'object',
    $comment: 'Each coverage chooses one of these 3 rate structures. All amounts are monthly',
    properties: {
        flatPremium: {type: 'object', properties: fixedRate.properties, $comment: 'if the premium is a flat number per person, no age banding, no multi-person discount'},
        rateByAge,
        fixedRates,
    }
} as const

const premiumUtils = {
    type: 'object',
    $comment: 'These are settings for adjusting the rates in the chosen premium structure based on count, age, etc.',
    properties: {
        rateType: { type: 'string', enum: ['flatPremium', 'rateByAge', 'fixedRates'], $comment: 'which premium structure is being used - default is flatPremium if set and then fixedRates if set then rateByAge. Can use with multi-discount for a discount to apply based on number of enrollees'},
        multiDiscount: {
            type: 'object',
            patternProperties: rateSchema.patternProperties,
            $comment: 'Used to discount rateByAge for multi-person households. So {1:1, 2:.1, 3:.2} means 10% discount on the total rate at 2 people, 20$ discount at 3 people, etc.'
        },
        weights: {
            type: 'object',
            patternProperties: rateSchema.patternProperties,
            $comment: 'A complex option for weighting rates by age such that multiDiscount doesn\'t apply equally to all ages'
        },
        baseDefault: {
            type: 'boolean',
            $comment: 'To be used with flatPremium - if true, this is the default rate if no rates are found'
        },
        breakpointAges: {
            type: 'array',
            items: {type: 'number'},
            $comment: 'This is used to manage generating rates for fixedRates with rateBreak set to breakpoint. This tells us which ages are a new breakpoint and we auto-fill all the ages in that age-band with the same fixed rates'
        },
        rateBreak: {
            type: 'string',
            enum: ['graduated', 'breakpoint'],
            $comment: 'Used with fixedRates. How the rates are broken up - graduated is a new rate for each age, breakpoint is a rate for each age group on the breakpointAges property'
        },
        smokerFactor: {
            type: 'number',
            $comment: 'increase percentage for smoking status ie: 1.5 would be 50% more for smokers'
        }
    }
} as const

export const premiumSchema = {
    type: 'object',
    $comment: `Choose one of the premium structures ${Object.keys(premiumStructures.properties).join(', ')}. The remaining properties are utilities for adjusting rates based on age, household count, etc.`,
    properties: {
        ...premiumStructures.properties,
        ...premiumUtils.properties
    }
} as const

export const dedSchema = {
    type: 'object', properties: {
        name: {type: 'string', $comment: 'deductible name (ie: family major medical)'},
        waivable: {type: 'boolean', $comment: 'waive-able for a preferred network/behavior'},
        detail: {type: 'string', $comment: 'descriptive detail'},
        cats: {type: 'array', items: ObjectIdSchema(), $comment: 'to be added later by a user'},
        single: {type: 'number', $comment: 'single amount'},
        family: {type: 'number', $comment: 'family amount - do not add 0, do not include if not applicable'},
        type: {type: 'string', enum: ['event', 'annual'], $comment: 'whether this is per event or per year'},
    }
} as const

const dedsSchema = {
    $comment: 'any key with dedSchema as the details of the deductible',
    type: 'object', patternProperties: {
        "^.*$": dedSchema
    }
} as const

const coinsSchema = {
    type: 'object',
    properties: {
        name: { type: 'string', $comment: 'coinsurance name (ie: emergency room)'},
        detail: {type: 'string', $comment: 'descriptive detail'},
        cats: {type: 'array', items: ObjectIdSchema(), $comment: 'to be added later by a user'},
        amount: { type: 'number' },
        category: { type: 'string', enum: coins_categories}
    }
} as const

const coins_s_schema = {
    $comment: 'any key with coinsSchema as the details of the coinsurance',
    type: 'object', patternProperties: {
        "^.*$": coinsSchema,
    }
} as const

export const baseCoverageSchema = {
    type: 'object',
    properties: {
        carrierName: {type: 'string', $comment: 'name of the insurance company'},
        webpage: {type: 'string', $comment: 'link to details webpage if available'},
        name: {type: 'string', $comment: 'name of the coverage'},
        openNetwork: {type: 'boolean'},
        plan_type: { type: 'string', $comment: 'network type such as HMO, PPO, EPO, POS'},
        type: {
            type: 'string',
            enum: ['mm', 'mec', 'hs', 'dc', 'eb', 'hra'],
            $comment: 'major medical, health share, direct care, excepted benefit'
        },
        description: {
            type: 'string',
            $comment: 'brief coverage description'
        },
        hsaQualified: {
            type: 'boolean',
            $comment: 'high deductible health plan - eligible for HSA contributions'
        },
        productDetailRef: { type: 'string', $comment: 'For health shares - this is the health share `_id:product_id`' },
        fortyPremium: {type: 'number'},
        maxAge: {type: 'number'},
        preventive: {type: 'boolean'},
        coinsurance: coinsSchema, //what the participant pays
        coins: coins_s_schema,
        deductible: dedSchema,

        deductibles: dedsSchema,
        copays: coins_s_schema,
        premium: premiumSchema,
        benefits: benSchema,
        rates: {
            type: 'array',
            items: ObjectIdSchema(),
            $comment: 'special rate areas - to be added by a user later',
        },
        deductibleType: {
            type: 'string',
            enum: ['annual', 'event'],
            $comment: 'deprecated - now see deductible.type'
        },
        moop: {
            type: 'object',
            $comment: 'medical or combined moop amount for individuals and families respectively',
            properties: {
                ...dedSchema.properties
            }
        },
        moops: {
            $comment: 'any key with dedSchema as the details of the moop',
            type: 'object', patternProperties: {
                "^.*$": dedSchema,
            }
        },
        monthsSinceSmoked: {type: 'number', $comment: 'at how many months someone is considered non-tobacco user'}
    }
} as const

export const coverageCalcSchema = {
    type: 'object',
    properties: {
        ...baseCoverageSchema.properties,
        covered: {type: 'string', enum: ['individual', 'group']},

    }
} as const

/** Adjudication flow can simply be: this coverage has the following special deductible categories - does this claim match any of the following */
export const coverCopySchema = {
    type: 'object',
    properties: {
        ...coverageCalcSchema.properties,
        carrierLogo: imageSchema,
        documents: {type: 'array', items: imageSchema},
        postTax: {type: 'boolean'},
        video: videoSchema,
        geo: geoJsonSchema,
        issuer: ObjectIdSchema(), //orgId of insurance company
        org: ObjectIdSchema(), //added by
        lastSync: {type: 'string'},
        provider: ObjectIdSchema(),
        template: {type: 'boolean'},
        fromTemplate: ObjectIdSchema(),
        public: {type: 'boolean'},
        sim: {type: 'boolean'},
        group_sim_only: {type: 'boolean', $comment: 'For products only available to groups - even if its an individual contract. Won\'t show in individual shop experience not tied to a group'},
        contract: ObjectIdSchema(),
        listBillDiscount: {type: 'number'}, //0 to 100 percent
        av: {
            type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object', properties: {
                        value: {type: 'number'}, //0 to 100
                        by: ObjectIdSchema(),
                        at: {}
                    }
                }
            }
        },
        dpc: {type: 'boolean'},
        ichra: {type: 'boolean'},
        shop: {type: 'boolean'},
        adj: {
            type: 'object',
            properties: {
                autoMax: {type: 'number'},
                authProviders: {type: 'array', items: ObjectIdSchema()}
            }
        },
        networks: {
            type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object', properties: {
                        coins_discount: {type: 'number'},
                        coins_discount_type: {type: 'string', enum: ['percent', 'flat']},
                        ded_discount: {type: 'number'},
                        ded_discount_type: {type: 'string', enum: ['percent', 'flat']},
                    }
                }
            }
        },
        disability: {
            type: 'object',
            properties: {
                coverOverRequiredAge: {type: 'boolean'},
                incomeLimit: {type: 'number'}
            }
        },
        catsWhitelist: {type: 'array', items: ObjectIdSchema()},
        catsBlacklist: {type: 'array', items: ObjectIdSchema()},
        procedureWhitelist: {type: 'array', items: ObjectIdSchema()},
        procedureBlacklist: {type: 'array', items: ObjectIdSchema()},
    }
} as const
// Main data model schema
export const coveragesSchema = {
    $id: 'Coverages',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'name', 'covered', 'type'],
    properties: {
        _id: ObjectIdSchema(),
        vectorIds: { type: 'object', patternProperties: {
                "^.*$": {
                    type: 'object', properties: {
                        uploadIds: {type: 'array', items: ObjectIdSchema()},
                        id: {type: 'string'}, /** Vector store id */
                        fileIds: {type: 'array', items: { type: 'string'}}, /** file id so they can be removed upon update */
                        updatedAt: {}
                    }
                }
            }
        },
        ...coverCopySchema.properties,
        ...commonFields.properties
    }
} as const
export type Coverages = FromSchema<typeof coveragesSchema>
export const coveragesValidator = getValidator(coveragesSchema, dataValidator)

const limitOop = (val) => {
    if (!val) return val;
    const {single = 0, family = 0} = val;
    return {
        single: Math.min(single, costLimits.moop.single),
        family: Math.min(family, costLimits.moop.family),
        type: val.type || 'annual'
    }
}
export const coveragesResolver = resolve<Coverages, HookContext>({
    properties: {
        moop: async (val) => {
            return limitOop(val);
        },
        deductible: async (val) => {
            return limitOop(val)
        }
    }
})

export const coveragesExternalResolver = resolve<Coverages, HookContext>({})

// Schema for creating new data
export const coveragesDataSchema = {
    $id: 'CoveragesData',
    type: 'object',
    additionalProperties: false,
    required: ['type', 'covered', 'name'],
    properties: {
        ...coveragesSchema.properties
    }
} as const
export type CoveragesData = FromSchema<typeof coveragesDataSchema>
export const coveragesDataValidator = getValidator(coveragesDataSchema, dataValidator)
export const coveragesDataResolver = resolve<CoveragesData, HookContext>({})

// Schema for updating existing data
export const coveragesPatchSchema = {
    $id: 'CoveragesPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...coveragesSchema.properties,
        ...commonPatch(coveragesSchema.properties).properties,
        $addToSet: addToSet([{path: 'rates', type: ObjectIdSchema()}]),
        $pull: pull([{path: 'rates', type: ObjectIdSchema()}])
    }
} as const
export type CoveragesPatch = FromSchema<typeof coveragesPatchSchema>
export const coveragesPatchValidator = getValidator(coveragesPatchSchema, dataValidator)
export const coveragesPatchResolver = resolve<CoveragesPatch, HookContext>({})

// Schema for allowed query properties
export const coveragesQuerySchema = {
    $id: 'CoveragesQuery',
    type: 'object',
    additionalProperties: true,
    properties: {
        ...querySyntax({
            ...coveragesSchema.properties,
            ...commonQueries.properties,
            name: {},
            geo: {},
            // geo: operatorQuery(geoJsonSchema, [existsQuery()]),
            'geo.geometry': {},
            fortyPremium: {}

        }),
        'carrierLogo.uploadId': {
            anyOf: [ObjectIdSchema(), {
                type: 'object',
                properties: {
                    $in: {type: 'array', items: ObjectIdSchema()},
                    $nin: {type: 'array', items: ObjectIdSchema()}
                }
            }]
        }
    }
} as const
export type CoveragesQuery = FromSchema<typeof coveragesQuerySchema>
export const coveragesQueryValidator = getValidator(coveragesQuerySchema, queryValidator)
export const coveragesQueryResolver = resolve<CoveragesQuery, HookContext>({})
