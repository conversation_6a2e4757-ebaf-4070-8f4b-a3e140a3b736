// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client'
import type { Drops, DropsData, DropsPatch, DropsQuery, DropsService } from './drops.class'

export type { Drops, DropsData, DropsPatch, DropsQuery }

export type DropsClientService = Pick<DropsService<Params<DropsQuery>>, (typeof dropsMethods)[number]>

export const dropsPath = 'drops'

export const dropsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const dropsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(dropsPath, connection.service(dropsPath), {
    methods: dropsMethods
  })
}

// Add this service to the client service type index
declare module '../../client' {
  interface ServiceTypes {
    [dropsPath]: DropsClientService
  }
}
