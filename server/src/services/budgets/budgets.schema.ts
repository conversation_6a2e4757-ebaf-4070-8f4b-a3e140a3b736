// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import {resolve, getValidator, querySyntax} from '@feathersjs/schema'
import {ObjectIdSchema} from '@feathersjs/schema'
import type {FromSchema} from '@feathersjs/schema'

import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {addToSet, commonFields, commonPatch, commonQueries, exists, pull} from '../../utils/common/schemas.js';
import {lastSync} from '../care-accounts/care-accounts.schema.js'

export const priorAuth = {
    type: 'object',
    properties: {
        min: {type: 'number'}, //smallest transaction requiring approval
        max: {type: 'number'} //largest allowable transaction
    }
} as const
// Main data model schema
export const budgetsSchema = {
    $id: 'Budgets',
    type: 'object',
    additionalProperties: false,
    required: ['_id', 'owner', 'name', 'moov_id'],
    properties: {
        _id: ObjectIdSchema(),
        amount: {type: 'number'},
        approvers: {type: 'array', items: ObjectIdSchema()},
        assigned_amount: {type: 'number'},
        assigned_recurs: {type: 'number'},
        cards: {type: 'array', items: ObjectIdSchema()},
        careAccount: ObjectIdSchema(),
        category: {type: 'string'},
        children: {type: 'array', items: ObjectIdSchema()},
        moov_id: {type: 'string'},
        connect_id: {type: 'string'},
        lastInc: {type: 'string'},
        lastSync: lastSync,
        managers: {type: 'array', items: ObjectIdSchema()},
        mcc_whitelist: {type: 'array', items: {type: 'string'}},
        mcc_blacklist: {type: 'array', items: {type: 'string'}},
        members: {type: 'array', items: ObjectIdSchema()},
        name: {type: 'string'},
        owner: ObjectIdSchema(),
        parent: ObjectIdSchema(),
        priorAuth,
        recurs: {type: 'number'},
        runSync: {type: 'string'},
        spent: {type: 'number'},
        spent_pending: {type: 'number'},
        spent_pending_sub: {type: 'number'},
        spent_sub: {type: 'number'},
        syncHistory: {type: 'array', items: lastSync},
        priorMonths: {
            type: 'array', items: {
                type: 'object', properties: {
                    amount: {type: 'number'},
                    recurs: {type: 'number'},
                    parent: ObjectIdSchema(),
                    careAccount: ObjectIdSchema(),
                    spent_sub: {type: 'number'},
                    spent_pending: {type: 'number'},
                    spent_pending_sub: {type: 'number'},
                    spent: {type: 'number'}
                }
            }
        },
        ...commonFields.properties
    }
} as const
export type Budgets = FromSchema<typeof budgetsSchema>
export const budgetsValidator = getValidator(budgetsSchema, dataValidator)
export const budgetsResolver = resolve<Budgets, HookContext>({})

export const budgetsExternalResolver = resolve<Budgets, HookContext>({})

const {amount, recurs, assigned_amount, assigned_recurs, ...createPatchSchema} = budgetsSchema.properties
// Schema for creating new data
export const budgetsDataSchema = {
    $id: 'BudgetsData',
    type: 'object',
    additionalProperties: false,
    required: ['owner', 'name', 'moov_id'],
    properties: {
        ...createPatchSchema
    }
} as const
export type BudgetsData = FromSchema<typeof budgetsDataSchema>
export const budgetsDataValidator = getValidator(budgetsDataSchema, dataValidator)
export const budgetsDataResolver = resolve<BudgetsData, HookContext>({})

const listArgs = [
    {path: 'syncHistory', type: lastSync},
    {path: 'mcc_whitelist', type: {type: 'string'}},
    {path: 'mcc_blacklist', type: {type: 'string'}},
    {path: 'budgets', type: {type: 'array', items: ObjectIdSchema()}},
    {path: 'cards', type: ObjectIdSchema()}
]
// Schema for updating existing data
export const budgetsPatchSchema = {
    $id: 'BudgetsPatch',
    type: 'object',
    additionalProperties: false,
    required: [],
    properties: {
        ...createPatchSchema,
        ...commonPatch(createPatchSchema, [{path: 'syncHistory', type: {type: 'array', items: lastSync}}]).properties,
        $inc: {},
        $pull: pull(listArgs),
        $addToSet: addToSet(listArgs)
    }
} as const
export type BudgetsPatch = FromSchema<typeof budgetsPatchSchema>
export const budgetsPatchValidator = getValidator(budgetsPatchSchema, dataValidator)
export const budgetsPatchResolver = resolve<BudgetsPatch, HookContext>({
    owner: async (val) => {
        return undefined
    }
})

// Schema for allowed query properties
export const budgetsQuerySchema = {
    $id: 'BudgetsQuery',
    type: 'object',
    additionalProperties: false,
    properties: {
        ...querySyntax({...budgetsSchema.properties, ...commonQueries.properties}),
        ...exists(['parent'], budgetsSchema.properties),
        name: {}
    }
} as const
export type BudgetsQuery = FromSchema<typeof budgetsQuerySchema>
export const budgetsQueryValidator = getValidator(budgetsQuerySchema, queryValidator)
export const budgetsQueryResolver = resolve<BudgetsQuery, HookContext>({})
