 //ensure there is a parent or careAccount for each budget and manage syncing balances when budgets are moved from their previous parent;
import {HookContext} from '../../../declarations';
 import {CoreCall, getExists, loadExists, setExists} from 'feathers-ucan';

 export const parentOrCareAccount = async (context: HookContext) => {
    const { careAccount, parent, $set, $unset } = context.data;
    let set:{path?:'parent'|'careAccount', id?: string } = {};
    let unset:{path?:'parent'|'careAccount' } = {};
    if(parent || $set?.parent){
        set = { path: 'parent', id: parent || $set?.parent };
        const existing = await loadExists(context);
        context = setExists(context, existing);
        if($set?.careAccount) delete context.data.$set.careAccount;
        if(careAccount) delete context.data.careAccount;
        if($unset?.parent) delete context.data.$unset.parent;
        if(existing.careAccount){
            unset = { path: 'careAccount' }
            context.data.$unset = { ...context.data.$unset, careAccount: '' }
        }
        //cannot have a prent and assigned_amount due to layering restriction
        context.data.$unset = { ...context.data.$unset, assigned_amount: '', assigned_recurs: '' }
    }
    else if(careAccount || $set?.careAccount){
        set = { path: 'careAccount', id: careAccount || $set?.careAccount };
        const existing = await loadExists(context);
        context = setExists(context, existing);
        if(existing.parent){
            context.data.$unset = { ...context.data.$unset, parent: '' }
            if($unset?.careAccount) delete context.data.$unset.careAccount;
            unset = { path: 'parent' };
        }
    } else if($unset?.careAccount || $unset?.parent) throw new Error('Budgets must either have a parent or a CareAccount')
    if(set.path || unset.path){
        const services = { 'parent': 'budgets', 'careAccount': 'care-accounts' }
        const existing:any = await loadExists(context);
        context = setExists(context, existing);
        if(set.path){
            if(existing[set.path] !== set.id){
                //path is changing - need to account for balances;
                const { amount, recurs } = existing;
                if(amount || recurs){
                    const oldPatch:any = { $inc: {} };
                    const newPatch:any = { $inc: {} };
                    if(amount){
                        oldPatch.$inc.assigned_amount = amount * -1;
                        newPatch.$inc.assigned_amount = amount;
                    }
                    if(recurs){
                        oldPatch.$inc.assigned_recurs = recurs * -1;
                        newPatch.$inc.assigned_recurs = recurs;
                    }
                    //remove amount from old id
                    if(existing[set.path]) await new CoreCall(services[set.path], context, { skipJoins: true }).patch(existing[set.path], oldPatch, { admin_pass: true })
                    //add amount to new id
                    await new CoreCall(services[set.path], context, { skipJoins: true }).patch(set.id as any, newPatch, { admin_pass: true })
                        .catch(async (e) => {
                            console.error(`Error syncing new assigned account for ${set.path}: ${set.id} - ${e.message}`)
                            //likely what was done on the old patch needs to be undone/re-synced
                            if(existing[set.path as any]) await new CoreCall(services[set.path as any], context, { skipJoins: true })._patch(existing[set.id as any] as any, { runSync: new Date() }, { skip_hooks: true, admin_pass: true});
                        })
                }
            }
        }
        if(unset.path){
            if(existing[unset.path]){
                const { amount, recurs } = existing;
                if(amount || recurs) {
                    const patchObj: any = {$inc: {}};
                    if (amount) patchObj.$inc.assigned_amount = amount
                    if (recurs) patchObj.$inc.assigned_recurs = recurs
                    await new CoreCall(services[unset.path], context).patch(existing[unset.path], patchObj,{ admin_pass: true })
                }
            }
        }
    }
    return context;
}

export const handleRemove = async (context:HookContext) => {
    let run;
    let reverse;
    if(context.method === 'remove') run = true;
    else if(context.data.deleted) run = true;
    else if(context.data.deleted === false) {
        run = true;
        reverse = true;
    }
    if(run){
        const existing = await loadExists(context)
        context = setExists(context, existing);

        const { amount, recurs } = existing;
        if(!amount && !recurs) run = false;
        if(reverse && !existing.deleted) run = false;

        if(run) {
            const patchObj:any = {$inc: {}}
            if(amount) patchObj.$inc.amount = amount * -1;
            if(recurs) patchObj.$inc.recurs = recurs * -1;
            if(existing.parent) await new CoreCall('budgets', context, {skipJoins: true}).patch(existing.parent, patchObj, { admin_pass: true });
            if(existing.careAccount) await new CoreCall('care-accounts', context, {skipJoins: true}).patch(existing.careAccount, patchObj, { admin_pass: true });
        }
    }
}

export const cascadeTotals = () => {
    return async (context: HookContext) => {
        const {$inc, updatedAt, updatedBy, updatedByHistory} = context.data || {}
        const { amount = 0, recurs = 0, assigned_amount = 0, assigned_recurs = 0 } = $inc || {};
        let run = false;
        const handleError = async (err:any, message:string, parentId?:string, service?:string) => {
            console.error(`Error adding card amount | ${context.id} | ${message} | ${err.message}`)
            if(parentId) await new CoreCall(service || 'budgets', context)._patch(parentId as any, { runSync: new Date() }, { skip_hooks: true, admin_pass: true})
            throw new Error(err.message)
        }
        const patchObj: any = {$inc: {}, lastInc: new Date() }
        const params:any = {};
        //if amount or recurs has been updated or if we're in the middle of the cascading totals already
        if (amount || recurs) {
            run = true;

            const existing:any = await loadExists(context)
                .catch(err => handleError(err, 'loading existing', context.id as any));
            context = setExists(context, existing);

            //limit changes when incrementing amount - to limit changes to other properties that might cause balances to get out of sync
            context.data = {$inc: {}, updatedAt, updatedBy, updatedByHistory}
            //prepare patchObj to assign amount and/or recurs to parent or careAccount
            if (amount) {
                patchObj.$inc.assigned_amount = amount;
                context.data.$inc.amount = amount
            }
            if (recurs) {
                patchObj.$inc.assigned_recurs = recurs;
                context.data.$inc.recurs = recurs;
            }
        } else if(assigned_amount || assigned_recurs) {
            run = true;

            const existing = await loadExists(context)
                .catch(err => handleError(err, 'loading existing', context.id as any));
            context = setExists(context, existing);

            //limit changes when incrementing amount - to limit changes to other properties that might cause balances to get out of sync
            context.data = {$inc: {}, updatedAt, updatedBy, updatedByHistory}
            //prepare patchObj to assign amount and/or recurs to parent or careAccount
            if (assigned_amount) {
                //for a sub budget, there is no need to update the assigned_amount, only to pass it along in params to make sure the budget integrity of funds is there.
                params.assigned_amount = assigned_amount;
                context.data.$inc.assigned_amount = assigned_amount
            }
            if (assigned_recurs) {
                params.assigned_recurs = assigned_recurs;
                context.data.$inc.assigned_recurs = assigned_recurs;
            }

            if (assigned_amount + existing.assigned_amount > existing.amount) throw new Error(`Budget ${existing.name} does not have allowance for ${assigned_amount} to be added to it`)
            if (assigned_recurs + existing.assigned_recurs > existing.recurs) throw new Error(`Budget ${existing.name} does not have allowance for ${assigned_amount} to be added to recurring amount`)

        }
        if(!run && context.data.lastInc){
            run = true;
            delete patchObj.$inc
        }
        if(run){
            const existing = getExists(context)
            if (existing.parent) {
                await new CoreCall('budgets', context, {skipJoins: true}).patch(existing.parent, patchObj, { admin_pass: true, ...params })
                    .catch(err => handleError(err, 'patching parent', existing.parent))
            } else if (existing.careAccount) {

                await new CoreCall('care-accounts', context, {skipJoins: true}).patch(existing.careAccount, patchObj, { admin_pass: true, ...params })
                    .catch(err => handleError(err, 'patching care account', existing.careAccount, 'care-accounts'));
            } else throw new Error('Budgets must have a care account or parent budget to be assigned an amount');
        }
        return context;
    }
}

 export const cascadeSpent = (noSub?:boolean) => {
     return async (context: HookContext) => {
         const {$inc, updatedAt, updatedBy, updatedByHistory} = context.data || {}
         const { spent = 0, spent_pending = 0 } = $inc || {};
         //if amount or recurs has been updated or if we're in the middle of the cascading totals already
         //TODO: need a global sync for this
         if (spent || spent_pending) {
             const handleError = async (err:any, message:string, parentId?:string, service?:string) => {
                 console.error(`Error cascading spent | ${context.id} | ${message} | ${err.message}`)
                 if(parentId) await new CoreCall(service || 'budgets', context)._patch(parentId as any, { runSync: new Date() }, { skip_hooks: true, admin_pass: true})
                 throw new Error(err.message)
             }
             const patchObj: any = {$inc: {}}

             const existing:any = await loadExists(context)
                 .catch(err => handleError(err, 'loading existing'));
             context = setExists(context, existing);

             //limit changes when incrementing amount - to limit changes to other properties that might cause balances to get out of sync
             context.data = {$inc: {}, updatedAt, updatedBy, updatedByHistory}
             //prepare patchObj to assign amount and/or recurs to parent or careAccount
             if (spent) {
                 if(noSub) patchObj.$inc.spent = spent;
                 else patchObj.$inc.spent_sub = spent;
                 context.data.$inc.spent = spent
             }
             if (spent_pending) {
                 if(noSub) patchObj.$inc.spent_pending = spent_pending;
                 else patchObj.$inc.spent_pending_sub = spent_pending;
                 context.data.$inc.spent_pending = spent_pending;
             }

             if (existing.parent) {
                 await new CoreCall('budgets', context, {skipJoins: true}).patch(existing.parent, patchObj, { admin_pass: true })
                     .catch(err => handleError(err, 'patching parent', existing.parent))
             } else if(existing.budget){
                 await new CoreCall('budgets', context, {skipJoins: true}).patch(existing.budget, patchObj, { admin_pass: true })
                     .catch(err => handleError(err, 'patching parent', existing.parent))
             }
         }
         return context;
     }
 }
