import {HookContext} from '../../../declarations.js';
import OpenAi from 'openai';
import {FormDataEncoder} from 'form-data-encoder';
import {Readable} from 'stream';
import axios from 'axios';
import {CoreCall, getExists, setExists} from 'feathers-ucan';

export const vectorizePlanDocs = async (context: HookContext) => {
    const {docs_as_md} = context.params.runJoin || {};
    if (docs_as_md) {
        const {key, org} = context.app.get('openai');
        const openai = new OpenAi({apiKey: key, organization: org})

        context.params.runJoin = {...context.params.runJoin, plan: true}
        const ex = await getExists(context);
        context = setExists(context, ex);
        const plan = ex._fastjoin?.plan || await new CoreCall('plans', context).get(ex.plan);
        const { id, updatedAt, fileIds } = (plan.vectorStoreIds || {}).plan_docs || {};
        let vectorStoreId = id;

        const allPlanDocs = await new CoreCall('plan-docs', context, {skipJoins: true}).find({
            query: {
                plan: ex.plan,
                $limit: 25
            }
        })

        let run = !updatedAt;
        if(!run) {
            /** see if any of the plan docs are out of date */
            const latest = new Date(updatedAt || 0).getTime();
            for (const d of allPlanDocs.data) {
                if (new Date(d.sectionsUpdatedAt || latest).getTime() > latest) {
                    run = true;
                    break;
                }
            }
        }


        // if(!run) return context;
        /** Get markdown content and generate file buffer for upload */
        const b = Buffer.from(docs_as_md, 'utf-8');
        const f = new File([b], 'plan_docs.md', {type: 'text/markdown'});
        const frm = new FormData()
        frm.append('file', f)
        frm.append('purpose', 'assistants')

        const encoder = new FormDataEncoder(frm as any);

        /** create file in openai  */
        const docsRes: any = await axios.post(
            'https://api.openai.com/v1/files',
            Readable.from(encoder.encode()),
            {
                headers: {
                    'Authorization': `Bearer ${key}`,
                    ...encoder.headers
                }
            }).catch(err => {
            console.log(`Error uploading plan docs: ${err.message}`)
            throw new Error(`Error uploading plan docs: ${err.message}`)
        })
        const docsFile = docsRes.data;
        if(fileIds?.length && vectorStoreId){
            for(const fileId of fileIds) {
                await openai.files.del(vectorStoreId, fileId)
                    .catch(err => {
                        console.log(`Error deleting old plan docs file id ${fileId}: ${err.message}`)
                    })
            }
        }
        /** if no vector store id - create vector store */
        if (!vectorStoreId) {
            const res = await openai.vectorStores.create({
                name: String(plan._id)
            })
                .catch(async err => {
                    console.error(`Error initializing vector store for plan docs: ${err.message}`);
                    await openai.files.del(vectorStoreId, docsFile.id)
                        .catch(err => {
                            console.log(`Error deleting old plan docs file id on vector store fail ${docsFile.id}: ${err.message}`)
                        })
                    throw new Error(`Error initializing vector store for plan docs: ${err.message}`)
                })
            vectorStoreId = res.id;
        }
        /** add file to vector store */
        await openai.vectorStores.files.create(vectorStoreId, {
            file_id: docsFile.id
        })
            .catch(err => {
                console.error(`Could not add plan docs file to vector store: ${err.message}`)
            })
        await new CoreCall('plans', context, {skipJoins: true}).patch(plan._id, {
            $set: {
                [`vectorStoreIds.plan_docs`]: {
                    id: vectorStoreId,
                    fileIds: [docsFile.id].filter(a => !!a),
                    updatedAt: new Date()
                }
            }
        }, { admin_pass: true, skip_hooks: true })
            .catch(err => {
                console.error(`Error saving vector store id for plan docs: ${err.message}`)
            })
    }
    return context;
}

export const ai_query = async (context: HookContext) => {
    const {ai_query} = context.params.runJoin || {};
    if (ai_query) {
        const {key, org} = context.app.get('openai');
        const openai = new OpenAi({apiKey: key, organization: org})

        const plan = await new CoreCall('plans', context, {skipJoins: true}).get(context.result.plan as any);
        const vectorId = plan.vectorStoreIds?.plan_docs?.id;
        if(!vectorId) throw new Error(`This plan has not yet been indexed for AI interactions`);
        // const fileId = context.result.fileId;

        const input = `
        ## Preface
        I have a question regarding my group health plan.
        
        ## Question
        ${ai_query.text}
        `
        const response = await openai.responses.create({
            model: 'gpt-4o',
            input,
            tools: [
                {
                   type: 'file_search',
                   vector_store_ids: [vectorId]
                }
            ]
        })
            .catch(err => {
                console.error(`Error querying plan docs: ${err.message}`)
                throw new Error(`Error querying plan docs: ${err.message}`)
            })

        const output:any = response.output.filter(a => a.type === 'message');
        const content:any = output[0].content.filter(a => a.type === 'output_text')[0];

        if(context.params.login?.owner) {
            const chats = await new CoreCall('ai-chats', context).find({
                query: {
                    $limit: 1,
                    chatId: `${context.params.login.owner}|${context.result.plan}|plan_docs`
                }
            })

            let method = 'create';
            const args = [{
                chats: {
                    $slice: 50,
                    $position: 0,
                    $each: [{
                        createdAt: new Date(),
                        chatName: 'plan_docs',
                        subject: context.result.plan,
                        person: context.params.login.owner,
                        question: ai_query.text,
                        answer: content.text,
                        annotations: content.annotations
                    }]
                }
            }, { admin_pass: true, skip_hooks: true }]
            if(chats.data?.length) {
                method = 'patch';
                args.unshift(chats.data[0]._id);
            }
            await new CoreCall('ai-chats', context)[method](...args)
                .catch(err => console.log(`Could not update plan doc ai chat history: ${context.params.login.owner} - ${err.message}`))

        }
        context.result = { ...context.result, _fastjoin: { ...context.result._fastjoin, ai_response: content} };

    }
    return context;
}
