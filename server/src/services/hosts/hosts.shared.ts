// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Hosts, HostsData, HostsPatch, HostsQuery, HostsService } from './hosts.class.js'

export type { Hosts, HostsData, HostsPatch, HostsQuery }

export type HostsClientService = Pick<HostsService<Params<HostsQuery>>, (typeof hostsMethods)[number]>

export const hostsPath = 'hosts'

export const hostsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const hostsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(hostsPath, connection.service(hostsPath), {
    methods: hostsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [hostsPath]: HostsClientService
  }
}
