// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { DocRequests, DocRequestsData, DocRequestsPatch, DocRequestsQuery, DocRequestsService } from './doc-requests.class.js'

export type { DocRequests, DocRequestsData, DocRequestsPatch, DocRequestsQuery }

export type DocRequestsClientService = Pick<DocRequestsService<Params<DocRequestsQuery>>, (typeof docRequestsMethods)[number]>

export const docRequestsPath = 'doc-requests'

export const docRequestsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const docRequestsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(docRequestsPath, connection.service(docRequestsPath), {
    methods: docRequestsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [docRequestsPath]: DocRequestsClientService
  }
}
