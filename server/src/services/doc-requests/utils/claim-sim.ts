import spendSim from './moderate.js';
import { HookContext} from '../../../declarations.js';

export const runSim = (context: HookContext) => {
    const { claimSim } = context.params.runJoin || {};
    if(claimSim){
        const { stats, nonRandom, log, rando } = claimSim;
        const spendAvg = spendSim.reduce((a, v) => a + v.spend, 0) / spendSim.length
        const spendFactor = stats.avgSpend ? stats.avgSpend / spendAvg : 1;

        const dedFn = ({num, ded, moop, coins}:any) => {
            const n = num * spendFactor;
            const deductible = Math.min(n, ded);
            const co = Math.min(moop - deductible, (n - deductible) * coins);
            const insured = n - deductible - co;
            // if(num !== deductible+co+insured) console.log('def fn error --------------- ', num);
            if(log) console.log('dedfn', n, deductible, co, insured)
            return {deductible, coins:co, insured, total: deductible + co + insured}
        }

        const familyDed = stats.fDed || stats.ded * 2.55;

        const getIndividualCost = (num:number) => {
            const { moop, coins, ded } = stats
            return dedFn({num, moop, coins, ded})
        }
        const getFamilyCost = (v:any, mix:boolean) => {
            const { fMoop, coins, moop, ded } = stats
            const fDed = familyDed;

            const tallyObj = {
                usedDed: 0,
                usedCoins: 0,
                usedIns: 0,
                fMoopCount: 0,
                usedFDed: 0,
                usedFMoop: 0
            }

            const tallyRes = ({ deductible, coins:co, insured }:any, tally:any) => {
                // console.log('before1', tally.usedIns + tally.usedDed + tally.usedCoins);
                // console.log('before2', deductible + co + insured);
                const newDed = Math.min(deductible, Math.max(0, fDed - tally.usedDed)) //fMoop cannot be less than fDed
                tally.usedDed += newDed;
                //single coinsurance + coinsurance applied to the amount no longer applied to the single decuctible due to maxing out family ded - subject to the limit of the fMoop less the ded
                const newCoins = Math.min(co + ((deductible - newDed) * coins), Math.max(0, fMoop - tally.fMoopCount - newDed))
                tally.usedCoins += newCoins;
                tally.fMoopCount += (newDed + newCoins);
                //add any additional insured amounts due to applying family deductible/coins/moop
                const newInsured = insured + (deductible - newDed) + Math.max(0, (co - newCoins));
                tally.usedIns += newInsured;
                //will always add or minus 0
                tally.usedFDed -= (deductible - newDed);
                //will always add or minus 0
                tally.usedFMoop -= (newCoins - co);
                // console.log('after1', tally.usedIns + tally.usedDed + tally.usedCoins, (tally.usedIns + tally.usedDed + tally.usedCoins) - (deductible + co + insured))
                // console.log('after2', newDed + newCoins + newInsured, (deductible + co + insured))
                return tally
            }
            let t = 0;
            //Calculate individual premium
            const self = dedFn({ num: v.spend, ded, moop, coins })
            t += v.spend;
            let runningTally = tallyRes(self, tallyObj);
            //spouse if there is enrolled spouse
            if(!mix){
                t += v.spouse.spend;
                const spouse = dedFn({ num: v.spouse.spend, ded, moop, coins })
                runningTally = tallyRes(spouse, runningTally);
            }

            //calculate all children premium
            for(let i = 0; i < v.children.length; i++){
                const num = v.children[i].spend
                t += num;
                const c = dedFn({ num, ded, moop, coins})
                runningTally = tallyRes(c, runningTally);
            }

            // console.log('check family total', mix ? v.total - v.spouse.spend : v.total, usedIns + usedDed + usedCoins)
            // if((mix ? v.total - v.spouse.spend : v.total) !== usedIns + usedDed + usedCoins) console.log('---------error here-----------')
            return { total: t, insured: runningTally.usedIns, deductible: runningTally.usedDed, coins: runningTally.usedCoins, fDed: runningTally.usedFDed, fMoop: runningTally.usedFMoop }
        }

        const cats = [
            ['Inpatient', .18, .37, 'orange'],
            ['Outpatient', .21, .34, 'cyan'],
            ['Specialists', .17, .37, 'purple'],
            ['Primary Care', .11, .13, 'teal'],
            ['Pharmacy', .20, .44, 'blue'],
            ['Emergency', .1, .7, 'red'],
            ['Other', .06, .4, 'pink']
        ]
        const getMapPct = () => {
            let map = 0;
            for (let i = 0; i < cats.length; i++) {
                const val:any = cats[i];
                const to = 1 * val[1];
                map += (to - to * val[2])
            }
            return map;
        }

        const setTotals = () => {
            const simList = spendSim
            const l = simList.length;
            const familyPct = (1 - stats.singleEnrollPct);
            const depOnlyPct = (stats.depOnlyPct);

            let all = 0;
            let children = 0;
            let spouse = 0;
            let single = 0;
            let enrolled = (l || 1) + 0;
            let singleEnrolled = 0;
            let famDeductible = 0;
            let deductible = 0;
            let singleDed = 0;
            let familyDedOverride = 0;
            let fMoopUsed = 0;
            let coins = 0;
            let singleCoins = 0;
            let insured = 0;
            let singleInsured = 0;

            //Parity
            const pArity:any = {
                ov: 0,
                nv: 0,
                oFam: 0,
                nFam: 0
            }

            //Affordability
            const aff:any = {
                ov: 0,
                nv: 0,
                oldOver: [],
                newOver: []
            }
            const mpct = getMapPct()


            let bills:any = [];
            for (let i = 0; i < l; i++) {
                const v:any = simList[i];
                bills.push(v.spend * spendFactor);
                //statistically simulate whether this person has a family enrolled
                const random = rando || nonRandom ? v.rando : Math.random();
                //mix = dependents but no spouse
                const mix = rando < depOnlyPct;
                const famCost = getFamilyCost(v, mix);
                const singleCost = getIndividualCost(v.spend);
                //apply family or single totals
                if (random < familyPct) {
                    children += v.childrenTotal;
                    const sp = v.spouse?.spend || 0
                    if (!mix) {
                        spouse += sp * spendFactor
                        enrolled++
                        bills.push(sp * spendFactor);
                    }

                    for (let ci = 0; ci < v.children.length; ci++) {
                        bills.push(v.children[ci].spend * spendFactor);
                    }

                    deductible += famCost.deductible;
                    famDeductible += famCost.deductible;
                    insured += famCost.insured;
                    coins += famCost.coins;
                    familyDedOverride += famCost.fDed;
                    fMoopUsed += famCost.fMoop;
                    all += famCost.total;

                    enrolled += v.children.length || 0
                    single += v.spend * spendFactor;
                } else {
                    all += singleCost.total;
                    // if(v.spend !== singleCost.deductible + singleCost.insured + singleCost.coins) console.log('error here single -------- ', v.spend, singleCost.deductible + singleCost.insured + singleCost.coins)
                    single += singleCost.total;
                    singleEnrolled++;
                    deductible += singleCost.deductible;
                    singleDed += singleCost.deductible;
                    insured += singleCost.insured;
                    coins += singleCost.coins;
                    singleInsured += singleCost.insured;
                    singleCoins += singleCost.coins;
                }

                //apply parity stats
                const amt = singleCost.insured * spendFactor;
                const f = famCost.insured * spendFactor

                pArity.ov += amt;
                pArity.nv += amt * mpct
                pArity.oFam += f
                pArity.nFam += f * mpct
            }

            for(const k in pArity){
                pArity[k] = pArity[k] / l
            }
            //Calculate Affordability
            for(let i = 0; i < stats.count; i++){
                const v = simList[i];
                const calc = stats.affPct * v.wage;
                const oov = Math.max(0, (pArity.ov) - calc);
                const onv = Math.max(0, (pArity.nv) - calc);
                aff.ov += oov
                aff.nv += onv
                if (oov > 0) aff.oldOver.push(v.wage);
                if (onv > 0) aff.newOver.push(v.wage);
            }
            const map = all * mpct;

            //do spend distribution calculation
            bills = bills.sort((a:any, b:any) => a - b)
            const billLength = bills.length;

            const suMap:any = [['bottom50', 0], ['top50', billLength * .5], ['top20', billLength * .8], ['top15', billLength * .85], ['top10', billLength * .9], ['top5', billLength * .95], ['top1', billLength * .99]]
            const sums:any = {
                bottom50: 0,
                top50: 0,
                top20: 0,
                top15: 0,
                top10: 0,
                top5: 0,
                top1: 0
            }
            const mapSums:any = {
                ...sums
            }
            const counts:any = {
                ...sums
            }
            for (let i = 0; i < billLength; i++) {
                const bill = bills[i];
                if (i <= billLength * .5) {
                    sums.bottom50 += bill;
                    mapSums.bottom50 += bill * mpct;
                    counts.bottom50++
                } else {
                    //start on item 2 because bottom 50 works opposite from the top categories
                    for (let f = 1; f < suMap.length; f++) {
                        // console.log('sum map', suMap[f][1], i);
                        if (i > Math.floor(suMap[f][1])) {
                            sums[suMap[f][0]] += bill;
                            // console.log('set sums', bill, sums[suMap[f][0]])
                            mapSums[suMap[f][0]] += bill * mpct;
                            counts[suMap[f][0]]++;
                        }
                    }
                }
            }
            const dist:any = {};
            const adjustedCounts:any = {};
            for (let i = 0; i < suMap.length; i++) {
                const k = suMap[i][0];
                dist[k] = (sums[k] / all).toFixed(4)
                adjustedCounts[k] = counts[k];
            }

            //adjust parity totals;
            return {
                nonRandom,
                aff,
                sums,
                mapSums,
                dist,
                counts,
                adjustedCounts,
                // bills,
                recAll: all,
                all: insured + deductible + coins,
                enrolled,
                dependents: (spouse + children),
                spouse,
                map,

                single,
                singleInsured,
                singleCoins,
                singleDed,

                deductible,
                familyDedOverride,
                famDeductible,
                singleEnrolled,
                fMoopUsed,
                insured,
                coins: coins,
                parity: pArity
            }
        };

        context.result = setTotals();
    }
}
