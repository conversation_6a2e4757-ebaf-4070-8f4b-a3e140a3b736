// import tesseract from 'node-tesseract-ocr';
// import {pdfToPng} from 'pdf-to-png-converter';
// import axios from 'axios'
//
// const urlToBuffer = async (url: string) => {
//     try {
//         const response = await axios.get(url, {
//             responseType: 'arraybuffer', // Ensures binary data is returned
//         });
//
//         return response.data; // This is the ArrayBuffer
//     } catch (error: any) {
//         console.error("Error fetching URL:", error.message);
//         return null;
//     }
// }
//
// const uploads: any = [];
// const images: any = [];
// const pdfs: any = [];
// const parseImage = async (bufferOrUrl: any) => {
//     return await tesseract.recognize(bufferOrUrl)
//         .catch(err => {
//             console.log(`Error ocr for image: ${err.message}`)
//         })
// }
// const parsePdf = async (bufferOrUrl: any) => {
//     let buffer
//     if (typeof bufferOrUrl === 'string') {
//
//     } else buffer = bufferOrUrl;
//     const pdfparse = await import('pdf-parse');
//     return await pdfparse(buffer)
//         .catch(err => {
//             console.log(`Error parsing pdf: ${err.message}`)
//         });
// }
// //for existing files, pass the file upload object and we can use the url
// for (let i = 0; i < files.length; i++) {
//     uploads.push(files[i]);
//     const url = files[i].url;
//     let buffer:ArrayBufferLike;
//     if(url) {
//         buffer = await urlToBuffer(url);
//         await uploadOne(files[i]);
//     } else buffer = files[i].buffer;
//     const filetype = files[i].mimetype || files[i]?.info?.type;
//     if (filetype.includes('pdf')) {
//         const p = await parsePdf(buffer);
//         const hasTextLayer = p.text && p.text.trim().length > 0;
//         if (hasTextLayer) {
//             pdfs.push({source: 'pdf', data: p.text})
//         } else {
//             const pngPages = await pdfToPng(buffer, {
//                 disableFontFace: false,
//             });
//             const dataArr: any = [];
//             for (let idx = 0; idx < pngPages.length; idx++) {
//                 const imgData = await parseImage(pngPages[idx].content)
//                 if (imgData) images.push({ source: 'image', data: imgData})
//             }
//         }
//     } else if (filetype.includes('image')) {
//         const p = await parseImage(buffer);
//         images.push({source: 'image', data: p});
//     }
// }

// const data:any = [...images, ...pdfs]
// role: 'user',
//     content: `Please extract, organize, and return the billing lines and total from the following parsed records. The records are an array of objects structured as { source: "pdf" or "image" (pdfs parsed by the npm package "pdf-parse" and images parsed by "tesseract ocr"), data: the parsed data as text }. Each record is a page of the same single bill. Here are the records ${uploads.map(a => a.url)}. Return the parsed results as an object with the following properties (if you can identify them from the files): { provider: the medical provider, facility or hospital who is billing the patient, zip_code: the provider zip code, state: the 2 digit state code for the provider, codes: an array of objects the following properties { cpt: cpt code if applicable, billing_code: other billing code listed such as hospital billing, price: line item unit cost, qty: quantity (if no quantity, list 1 and the line total for price), total: line total}, patient_responsibility: amounts such as "you pay" or "patient share", bill_total: the bill total }. Return the list as a JSON list. No commentary and no markdown - just a JSON list`
