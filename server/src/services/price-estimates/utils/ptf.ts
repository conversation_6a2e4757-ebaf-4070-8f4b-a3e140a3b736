import FormData from 'form-data'
import xlsx from 'node-xlsx';
import { HookContext} from '../../../declarations.js';
import axios from 'axios';
import {levenSort} from '../../../utils/common/filters.js';

export const getPtFile = async (context: HookContext): Promise<HookContext> => {
    const {pt_search} = context.params.runJoin || {};
    if (pt_search) {
        const addlConfigs:any = {}
        const runSearch = async ({code, state, msa}: any) => {
            const url = 'https://healthcostlabsapp-hclapi.azurewebsites.net/api/Expose/GetRateDataURL';
            const formData = new FormData();
            // console.log('pt search', pt_search);
            if (code) formData.append('Procedurecode', code.trim());
            formData.append('StateName', state);
            formData.append('MSAName', msa);
            for(const k in addlConfigs){
                formData.append(k, addlConfigs[k]);
            }
            const headers = {
                SecretKey: '7560F0C9-0F1C-47DA-8E59-2B7D5911DA62',
                ...formData.getHeaders()
            }
            const res: any = await axios.post(url, formData, {headers, responseType: 'arraybuffer'})
                .catch(err => {
                    console.log('Error calling ptf api: ', err.message);
                })
            let file: any = undefined;
            try {
                file = xlsx.parse(res.data)
            } catch (e: any) {
                console.log('Error parsing ptf response: ', e.message);
            }
            // console.log('file', file[0].data[0]);
            const mapObj: any = {};
            const result: any = {};
            const data = file[0]?.data || [[]]

            //handle filtering by hospital name (will run on closest match - potential errors if no actual matching hospitals are returned
            if(pt_search.hospital_name){
                //[HospitalID, FacilityName]
                const idxs = [data[0].indexOf('HospitalID'), data[0].indexOf('FacilityName')];

                const filterByHospitalID = (id) => {
                    for(let i = data.length - 1; i > 0; i--){
                        if(data[i][idxs[0]] !== id) data.splice(i, 1)
                    }
                }
                if(!addlConfigs.HospitalName) {
                    const hospitalList = data.slice(1).map(a => {
                        return {
                            HospitalID: a[idxs[0]],
                            FacilityName: a[idxs[1]],
                            lowercase: a[idxs[1]].toLowerCase(),
                        }
                    })
                    const uniqueHospitals: Array<[string, string]> = [];
                    const hospitalIDs: Array<string> = [];
                    for (let i = 0; i < hospitalList.length; i++) {
                        if (!hospitalIDs.includes(hospitalList[i].HospitalID)) {
                            uniqueHospitals.push(hospitalList[i]);
                            hospitalIDs.push(hospitalList[i].HospitalID);
                        }
                    }
                    const sorted = levenSort(pt_search.hospital_name.toLowerCase(), uniqueHospitals, {key: 'lowercase'});
                    addlConfigs.HospitalID = (sorted[0] as any).HospitalID
                }
                filterByHospitalID(addlConfigs.HospitalID)
            }

            for (let i = 0; i < data[0].length; i++) {
                mapObj[data[0][i]] = i
            }
            const hospitalKeys = ['HospitalID', 'FacilityName', 'Location', 'HospitalType', 'HealthsystemType', 'MsaName', 'CityName', 'StateName', 'Address', 'ZIP', 'Phone', 'Latitude', 'Longitude', 'Website', 'Compliant', 'NPI']
            const payerKeys = ['PlanName', 'PaymentMethod', 'Carrier', 'LOB', 'Product', 'Rate', 'Additional payer notes', 'Importtime', 'GrossCharges', 'CmsRate', 'Methodology', 'DrugUnitMeasurement', 'DrugTypeMeasurement'];
            const topKeys = ['ProcedureCode', 'Location', 'CodeDescription', 'Category', 'CodeType', 'DrugUnitMeasurement', 'DrugTypeMeasurement']
            const keys = Object.keys(mapObj);
            const hospitalIds: any = [];
            const hospitalNames: any = [];
            const hospitals: any = {};
            let cms_rate;
            const mapRow = (row: any, idx: number) => {
                const obj: any = {};
                for (let k = 0; k < keys.length; k++) {
                    obj[keys[k]] = row[mapObj[keys[k]]];
                }
                if (idx === 0) {
                    for (let i = 0; i < topKeys.length; i++) {
                        result[topKeys[i]] = obj[topKeys[i]]
                    }
                }
                const hospitalId = obj.HospitalID;
                //make sure the hospital is setup already
                if (!hospitals[hospitalId]) {
                    hospitals[hospitalId] = {payers: {}, cash_rate: 0};
                    hospitalIds.push(hospitalId);
                    hospitalNames.push(obj.FacilityName);
                    for (let hi = 0; hi < hospitalKeys.length; hi++) {
                        const key = hospitalKeys[hi];
                        hospitals[obj.HospitalID][key] = obj[key];
                    }
                }
                //setup the payer
                const payer: any = {}
                const payerId = obj.PlanName || ''
                let isCash;
                for(const d of ['discounted_cash', 'self pay', 'self_pay', 'cash']){
                    if(payerId.toLowerCase().includes(d)){
                        isCash = true;
                        break;
                    }
                }
               if(isCash) hospitals[hospitalId].cash_rate = obj.Rate || obj.GrossCharges
                for (let pi = 0; pi < payerKeys.length; pi++) {
                    const key = payerKeys[pi];
                    payer[key] = obj[key]
                }
                if(obj.CmsRate && obj.CmsRate < (obj[hospitalId].cms_rate || Infinity)) obj[hospitalId].cms_rate = obj.CmsRate
                hospitals[hospitalId].payers[payerId] = payer;
            }

            const slc = data.slice(1);
            for (let idx = 0; idx < slc.length; idx++) {
                const slc_2 = slc[idx];
                mapRow(slc_2, idx);
            }
            // console.log('return hospitals', hospitals);
            result.hospitals = hospitals;
            result.code = code;
            result.msa = msa;
            result.HospitalID = addlConfigs.HospitalID;
            context.result = {total: (context.result?.total || 0) + 1, data: [...context.result?.data || [], result]}
        }

        const uniqueConfigs:any[] = [];
        const uniqueCodes:any[] = [];
        for(let i = 0; i < pt_search.configs.length; i++) {
            const config = pt_search.configs[i];
            if(!uniqueCodes.includes(config.code)) {
                uniqueCodes.push(config.code);
                uniqueConfigs.push(config);
            }
        }
        await Promise.all(uniqueConfigs.map(a => runSearch(a)))
    }
    return context;
}
