// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  PriceEstimates,
  PriceEstimatesData,
  PriceEstimatesPatch,
  PriceEstimatesQuery,
  PriceEstimatesService
} from './price-estimates.class.js'

export type { PriceEstimates, PriceEstimatesData, PriceEstimatesPatch, PriceEstimatesQuery }

export type PriceEstimatesClientService = Pick<
  PriceEstimatesService<Params<PriceEstimatesQuery>>,
  (typeof priceEstimatesMethods)[number]
>

export const priceEstimatesPath = 'price-estimates'

export const priceEstimatesMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const priceEstimatesClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(priceEstimatesPath, connection.service(priceEstimatesPath), {
    methods: priceEstimatesMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [priceEstimatesPath]: PriceEstimatesClientService
  }
}
