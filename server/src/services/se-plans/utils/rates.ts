import {CoreCall} from 'feathers-ucan';

import {HookContext} from '../../../declarations.js';

// const headers = {
//     business_year: 0,
//     state_code: 1,
//     issuer_id: 2,
//     import_date: 4,
//     rate_expiration: 5,
//     rate_effective_date: 6,
//     plan_id: 7,
//     rating_area: 8,
//     age: 9,
//     individual_rate: 11
// }

const trimString = (val) => {
    if (!val) return '';
    return String(val).trim()
}
const toDate = (val) => {
    if (!val) return undefined;
    return new Date(val);
}

type FormatOpts = {
    ex: any, row: Array<any>, headers:any, year?:string
}

/** This file followed a little more of a thoughtful format than the others - which just copied the headers from the CMS data. In this one, the headers are more unique - but still try to match the row names as closey as possible so the client can auto-recognize with toLowerCase().split(' ').join('_').includes() */
const format = ({ ex, row, headers, year }:FormatOpts) => {
    return {
        business_year: () => {
            const val = row[headers.plan_id];
            if(!val) throw new Error(`${val}: No year provided - and no year override`)
           return  Number(year) || Number(row[headers.business_year] || '1980')
        },
        state_code: () => {
            const val = row[headers.state_code]
            if(!val) throw new Error(`${row[headers.plan_id]}: No state code provided`)
            return trimString(val).toUpperCase()
        },
        issuer_id: () => {
            const val = row[headers.issuer_id]
            if(!val) {
                throw new Error(`${row[headers.plan_id]}: No issuer id provided`)
            }
           return  trimString(val)
        },
        import_date: () => {
            return toDate(row[headers.import_date] || new Date())
        },
        rate_expiration: () => {
            return toDate(row[headers.rate_expiration] || new Date())
        },
        rate_effective_date: () => {
            return toDate(row[headers.rate_effective_date] || new Date())
        },
        plan_id: () => {
            const val = row[headers.plan_id]
            if(!val) {
                throw new Error(`${row[headers.plan_id]}: No plan id provided`)
            }
            return trimString(val)
        },
        rating_areas: () => {
            const val = row[headers.individual_rate]
            if (!val) return ex?.rating_areas || {}
            const rating_area = row[headers.rating_area].replace(/\D+/g, '');

            const num = typeof val === 'number' ? val : Number(val.toString().replace(/^\d+$/, ''))

            const age = row[headers.age]

            const exRa = (ex?.rating_areas || {})[rating_area] || {}
            const obj = {[rating_area]: {name: row[headers.rating_area], ...exRa, rates: {...exRa.rates }}}
            /** Handle range (0-14) age scenarios */
            const spl = String(age).split('-')
            if(spl.length > 1){
                const from = Number(spl[0])
                const to = Number(spl[1])

                for(let i = from; i <= to; i++){
                    obj[rating_area].rates[i] = num;
                }
            } else if(String(age).includes(' and')){
                const from = Number(String(age).split(' and')[0])
                const to = 100

                for(let i = from; i <= to; i++){
                    obj[rating_area].rates[i] = num;
                }
            }
            /** handle single age scenarios */
            else obj[rating_area].rates[age] = num || obj[rating_area].rates[age-1] || obj[rating_area].rates[age+1];

            return obj
        }
    }
}

/** currently just a single value - but we may want to update more paths for an existing policy so will leave the logic as extensible - add custom paths where the key is the format path and the value is the header path. This way we can retrieve the correct value from the csv row and format it correctly with any custom value - even if it isn't included in the headers. It  */
const rate_update_paths = ['rating_areas']

export const uploadRates = ({csvData, headers, year}: any) => {
    return async (context: HookContext) => {
        const byPlanId: any = {};
        for (let i = 0; i < csvData.length; i++) {
            const row = csvData[i];
            const plan_id = row[headers.plan_id];
            const f = format({
                ex: byPlanId[plan_id], row, headers, year
            })
            /** just adding a rating area */
            if (byPlanId[plan_id]) {
                for (const k of rate_update_paths) {
                    byPlanId[plan_id][k] = f[k]()
                    byPlanId[plan_id].state_code = f.state_code()
                }
            } else {
                /** add new plan */
                byPlanId[plan_id] = {}
                for (const k in f) {
                    byPlanId[plan_id][k] = f[k]()
                }
            }
        }

        const statePlanIds = Object.keys(byPlanId).map(a => `${byPlanId[a].state_code.toLowerCase()}:${byPlanId[a].business_year}:${a}`)

        const existing = await new CoreCall('se-plans', context).find({
            query: {
                $limit: statePlanIds.length,
                state_plan_id: {$in: statePlanIds}
            }
        })

        const stateCode = byPlanId[Object.keys(byPlanId)[0]].state_code

        const jds = await new CoreCall('junk-drawers', context).find({
            query: {
                $limit: 1,
                itemId: `rating_areas|${stateCode.toLowerCase()}`
            }
        })
        const jd = jds.data[0]

        const saveFormat = (nv: any, ov: any) => {
            const {rating_areas, ...rest} = nv;
            const obj = { ...rest, rating_areas: {} }
            for (const k in rating_areas || {}) {
                obj.rating_areas[k] = {
                    ...(ov.rating_areas || {})[k],
                    ...rating_areas[k],
                    zips: jd.data[`Rating Area ${k}`].zips || [],
                    fips: jd.data[`Rating Area ${k}`].fips || [],
                    rates: {
                        ...(ov.rating_areas || {})[k]?.rates,
                        ...rating_areas[k]?.rates
                    }
                }
            }
            return obj;
        }

        const errs: Array<{ id: string, message: string }> = [];
        const promises: any = [];
        for (const ov of existing.data) {
            const nv = byPlanId[ov.plan_id];
            promises.push(new CoreCall('se-plans', context).patch(ov._id, saveFormat(nv, ov)).catch(err => errs.push({
                id: ov.plan_id,
                message: err.message
            })))
            delete byPlanId[ov.plan_id]
        }
        for (const k in byPlanId) {
            promises.push(new CoreCall('se-plans', context).create(saveFormat(byPlanId[k], {})))
        }
        const data = await Promise.all(promises)
        context.result = {data, errs}
        return context;
    }
}

