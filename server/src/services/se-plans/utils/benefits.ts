import {CoreCall} from 'feathers-ucan';
import {HookContext} from '../../../declarations.js';

interface BenefitRow {
    'BUSINESS YEAR': string;
    'STATE CODE': string;
    'ISSUER ID': string;
    'SOURCE NAME': string;
    'VERSION NUMBER': string;
    'IMPORT DATE': string;
    'STANDARD COMPONENT ID': string;
    'PLAN ID': string;
    'BENEFIT NAME': string;
    'IS EHB': string;
    'IS STATE MANDATE': string;
    'IS COVERED': string;
    'QUANTITY LIMIT ON SVC': string;
    'LIMIT QUANTITY': string;
    'LIMIT UNIT': string;
    'MINIMUM STAY': string;
    'EXCLUSIONS': string;
    'EXPLANATION': string;
    'EHB VAR REASON': string;
    'IS SUBJECTED TO DED TIER 1': string;
    'IS SUBJECTED TO DED TIER 2': string;
    'IS EXCLUDED FROM INN MOOP': string;
    'IS EXCLUDED FROM OON MOOP': string;

    [key: string]: string | number | boolean | undefined;
}
//
// const headers = {
//     'STATE CODE': 1,
//     'PLAN ID': 1,
//     'STANDARD COMPONENT ID': 6,
//     'BENEFIT NAME': 8,
//     'IS COVERED': 10,
//     'QUANTITY LIMIT ON SVC': 12,
//     'LIMIT QUANTITY': 13,
//     'LIMIT UNIT': 14,
//     'MINIMUM STAY': 15,
//     'EXCLUSIONS': 16,
//     'EXPLANATION': 17,
//     'IS SUBJECTED TO DED TIER 1': 19,
//     'IS SUBJECTED TO DED TIER 2': 20,
//     'IS EXCLUDED FROM INN MOOP': 21,
//     'IS EXCLUDED FROM OON MOOP': 22
// }
const excludeFromMap = ['STATE CODE', 'STANDARD COMPONENT ID', 'PLAN ID', 'BENEFIT NAME', 'IS COVERED'];


const format = () => {
    return {
        'EXPLANATION': (val) => {
            if (!val) return '';
            if (val.toLowerCase().indexOf('please') > -1) return ''
            return val.trim();
        }
    }
}

export const uploadBenefits = ({csvData, headers, year}: any) => {
    return async (context: HookContext) => {
        const state_code = csvData[0][headers['STATE CODE']];
        if (!state_code) throw new Error('State code not found in data')
        const state_plan_ids: Array<string> = [];
        const byId: any = {};
        for (let i = 0; i < csvData.length; i++) {
            const row = csvData[i];
            const spId = `${state_code.toLowerCase()}:${year}:${row[headers['STANDARD COMPONENT ID']]}`
            const plan_id = row[headers['PLAN ID']]
            if(!state_plan_ids.includes(spId)) state_plan_ids.push(spId)
            const formatObj = format();
            const obj: any = {
                label: row[headers['BENEFIT NAME']] || '',
                detail: '',
                covered: !!row[headers['IS COVERED']]?.toLowerCase().includes('y')
            }
            for (const k in headers) {
                if (!excludeFromMap.includes(k)) {
                    const val = row[headers[k]]
                    const str = formatObj[k] ? formatObj[k](val) : val
                    if(str && str.toLowerCase() !== 'no') {
                        if (obj.detail) obj.detail += ' | '
                        obj.detail += `${k}: ${str}`
                    }
                }
            }
            if(obj.detail) byId[plan_id] = {...byId[plan_id], [obj.label.split('.')[0]]: obj};

        }
        const ex = await new CoreCall('se-plans', context).find({
            query: {
                $limit: state_plan_ids.length,
                state_plan_id: {$in: state_plan_ids}
            }
        })

        const errs: any = [];
        const promises: any = [];
        for (let i = 0; i < ex.data.length; i++) {
            const {_id} = ex.data[i];
            const patchObj = {};
            const changes = byId[`${ex.data[i].plan_id}-01`]

            /** Check CSR -02 -03 */
            const key_02 = `${ex.data[i].plan_id}-02`
            const changes_02 = byId[key_02]
            if(changes_02){
                for (const k in changes_02) {
                    patchObj[`csr.02.benefits.${k}`] = changes_02[k]
                }
            }
            const key_03 = `${ex.data[i].plan_id}-03`
            const changes_03 = byId[key_03]
            if(changes_03){
                for (const k in changes_03) {
                    patchObj[`csr.03.benefits.${k}`] = changes_03[k]
                }
            }
            /** Standard changes */
            if (changes) {
                for (const k in changes) {
                    patchObj[`benefits.${k}`] = changes[k]
                }
            }
            if (Object.keys(patchObj).length) promises.push(new CoreCall('se-plans', context).patch(_id, {$set: patchObj})
                .catch(err => errs.push({id: ex.data[i].row['PLAN ID'], message: err.message})))
        }

        const data = await Promise.all(promises)
        context.result = {data, errs}
        return context;
    }
}
