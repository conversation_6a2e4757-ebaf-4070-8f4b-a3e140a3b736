import {HookContext} from '../../../declarations.js';
import {CoreCall} from 'feathers-ucan';
import xlsx from 'node-xlsx';

// ['Rating Area', 'County Name', '3 Digit Zip Code']
const headers = {
    rating_area: 0,
    county_name: 1,
    zip_first_3: 2
}
export const ratingAreaUpload = async (context: HookContext) => {
    const {rating_area_upload} = context.params.runJoin || {}
    if (rating_area_upload) {
        const {state, overwrite, omitFirstRow, sheet, updatePlans} = rating_area_upload;
        if (context.params.file) {
            const sheets = xlsx.parse(context.params.file.buffer);
            const csvData = sheets.filter(a => a.name === sheet)[0].data.slice(omitFirstRow ? 1 : 0);

            const byRateArea: any = {};
            const errs: Array<{ id: string, message: string }> = [];
            let stateDrawer = {total: 0, data: []}

            const hasZip: any = [];
            const noZip: any = [];
            for (let i = 0; i < csvData.length; i++) {
                if (csvData[i][headers.zip_first_3]) hasZip.push(csvData[i])
                else noZip.push(csvData[i])
            }
            const zipList = hasZip.map(a => `zips|${a[headers.zip_first_3].split(' ')[0]}`)
            const zipDrawers = await new CoreCall('junk-drawers', context).find({
                query: {
                    itemId: {$in: zipList},
                    $limit: zipList.length
                }
            })


            const getOneArea = async (row: Array<string>) => {
                const key = row[headers.rating_area].replace(/^\d+$/, '')
                if (row[headers.zip_first_3]) {
                    const jd = zipDrawers.data.filter(a => a.itemName === row[headers.zip_first_3].split(' ')[0])[0]
                    if (jd) {
                        const data = jd.data;

                        for (const k in data) {
                            /** set data at 'zips' so the for loop works at data[k].zips */
                            const ra = byRateArea[key] || {}
                            /** ADD FIPS */
                            if (data[k].all_fips?.length) {
                                for (const fip of data[k].all_fips) {
                                    if (ra.fips && !ra.fips.includes(fip)) byRateArea[key].fips.push(fip)
                                    else byRateArea[key] = {...byRateArea[key], fips: [fip]}
                                }
                            } else {
                                if (ra.fips && !ra.fips.includes(data[k].fip)) byRateArea[key].fips.push(data[k].fip)
                                else byRateArea[key] = {...byRateArea[key], fips: [data[k].fip]}
                            }
                            /** ADD COUNTY */
                            for (const co of data[k].counties || []) {
                                if (ra.counties && !ra.counties.includes(co)) byRateArea[key].counties.push(co)
                                else byRateArea[key] = {...byRateArea[key], counties: [co]}
                            }
                            /** ADD CITY */
                            if (ra.cities && !ra.cities.includes(data[k].city)) byRateArea[key].cities.push(data[k].city)
                            else byRateArea[key] = {...byRateArea[key], cities: [data[k].city]}
                            /** ADD ZIP */
                            if (ra.zips && !ra.zips.includes(k)) byRateArea[key].zips.push(k)
                            else byRateArea[key] = {...byRateArea[key], zips: [k]}
                        }
                    }
                } else if (row[1]) {
                    const jd = stateDrawer.total ? stateDrawer : await new CoreCall('junk-drawers', context).find({
                        query: {
                            $limit: 1,
                            itemId: `states|${state.toLowerCase()}`
                        }
                    })
                        .catch(err => errs.push({id: key, message: err.message}))


                    if (jd) {
                        stateDrawer = jd;
                        const data = jd.data[0].data;

                        /**Get fips data matched to the county and include fips and all zips */

                        for (const k in data.fips) {
                            const fip = data.fips[k];
                            /** Only use cities where the county is a match */
                            if (!Array.isArray(fip)) {

                                /** Match on the rating area county name */
                                const rowCounty = row[headers.county_name]?.toLowerCase().replace('county', '').trim() || '***'
                                if (!(fip.county?.toLowerCase().includes(rowCounty) || (fip.counties || []).map(a => a.toLowerCase()).includes(rowCounty))) continue;

                                const ra = byRateArea[key] || {}

                                /** ADD FIPS */
                                if (ra.fips && !ra.fips.includes(k)) byRateArea[key].fips.push(k)
                                else if (!ra.fips) byRateArea[key] = {...byRateArea[key], fips: [k]}

                                const pathMap = {
                                    'zip': 'zips',
                                    'county': 'counties',
                                    'zips': 'zips',
                                    'counties': 'counties'
                                }
                                const paths = ['zip', 'county', 'zips']
                                for (const path of paths) {
                                    const raPath = pathMap[path]
                                    if (fip[path]) {
                                        if (ra[raPath]) {
                                            if (Array.isArray(fip[path])) {
                                                for (const val of fip[path] || []) {
                                                    if (!ra[raPath].includes(val)) byRateArea[key][raPath].push(val)
                                                }
                                            } else if (!ra[raPath].includes(fip[path])) byRateArea[key][raPath].push(fip[path])
                                        } else if (!ra[raPath]) byRateArea[key] = {
                                            ...byRateArea[key],
                                            [raPath]: fip[path] ? Array.isArray(fip[path]) ? fip[path] : [fip[path]] : []
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            const ex = await new CoreCall('junk-drawers', context).find({
                query: {
                    $limit: 1,
                    itemId: `rating_areas|${state.toLowerCase()}`
                }
            })

            for (let i = 0; i < csvData.length; i++) {
                const row = csvData[i];
                if (row) await getOneArea(row)
            }

            let added;
            if (ex.total) {
                const patchObj: any = {};
                for (const k in byRateArea) {
                    if (overwrite) {
                        for (const sub in byRateArea[k]) {
                            patchObj[`data.${k}.${sub}`] = {$each: byRateArea[k][sub]}
                        }
                    } else patchObj[`data.${k}`] = byRateArea[k]
                }
                added = await new CoreCall('junk-drawers', context).patch(ex.data[0]._id, patchObj)
            } else added = await new CoreCall('junk-drawers', context).create({
                drawer: 'rating_areas',
                itemName: state.toLowerCase(),
                data: byRateArea
            })

            /** Patch all affected plans */
            const affectedPlans = await new CoreCall('se-plans', context).find({
                query: {
                    state_code: state,
                },
                paginate: false
            })
            const {data} = added;

            const promises: any = []
            for (let i = 0; i < affectedPlans.length; i++) {
                const {rating_areas, _id} = affectedPlans[i];
                const patchObj: any = {all_zips: [], all_fips: [], first_3_zips: []};
                for (const k in rating_areas || {}) {
                    if (!data[`Rating Area ${k}`]) {
                        continue
                    }
                    const {zips, fips} = data[`Rating Area ${k}`]
                    patchObj[`rating_areas.${k}.zips`] = zips || []
                    patchObj[`rating_areas.${k}.fips`] = fips || []
                    for (const zip of zips || []) {
                        patchObj.all_zips.push(zip)
                        patchObj.first_3_zips.push(zip.slice(0, 3))
                    }
                    for (const fip of fips) patchObj.all_fips.push(fip)
                }
                promises.push(new CoreCall('se-plans', context)._patch(_id, {$set: patchObj}, {
                    skip_hooks: true,
                    admin_pass: true
                }))
            }

            await Promise.all(promises)

            context.result = {data: added, errs}

        }
    }
    return context;
}
