// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { SePlans, SePlansData, SePlansPatch, SePlansQuery, SePlansService } from './se-plans.class.js'

export type { SePlans, SePlansData, SePlansPatch, SePlansQuery }

export type SePlansClientService = Pick<SePlansService<Params<SePlansQuery>>, (typeof sePlansMethods)[number]>

export const sePlansPath = 'se-plans'

export const sePlansMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const sePlansClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(sePlansPath, connection.service(sePlansPath), {
    methods: sePlansMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [sePlansPath]: SePlansClientService
  }
}
