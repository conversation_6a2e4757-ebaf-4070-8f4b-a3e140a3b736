// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Comps, CompsData, CompsPatch, CompsQuery, CompsService } from './comps.class.js'

export type { Comps, CompsData, CompsPatch, CompsQuery }

export type CompsClientService = Pick<CompsService<Params<CompsQuery>>, (typeof compsMethods)[number]>

export const compsPath = 'comps'

export const compsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const compsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(compsPath, connection.service(compsPath), {
    methods: compsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [compsPath]: CompsClientService
  }
}
