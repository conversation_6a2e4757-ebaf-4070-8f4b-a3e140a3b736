// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  JunkDraw<PERSON>,
  JunkDrawersData,
  JunkDrawersPatch,
  JunkDrawersQuery,
  JunkDrawersService
} from './junk-drawers.class.js'

export type { JunkDrawers, JunkDrawersData, JunkDrawersPatch, JunkDrawersQuery }

export type JunkDrawersClientService = Pick<
  JunkDrawersService<Params<JunkDrawersQuery>>,
  (typeof junkDrawersMethods)[number]
>

export const junkDrawersPath = 'junk-drawers'

export const junkDrawersMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const junkDrawersClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(junkDrawersPath, connection.service(junkDrawersPath), {
    methods: junkDrawersMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [junkDrawersPath]: JunkDrawersClientService
  }
}
