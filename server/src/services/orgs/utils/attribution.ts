import {_flatten, _get} from 'symbol-ucan';
import {AnyObj} from 'feathers-ucan'

type OrgObj = {
    [key:string]: {
        [key:string]: number
    }
}

type MgmtObj = {
    [key:string]: Array<string>
}

type Ctrl = {
    identical: number,
    parent?: string,
    brotherSister?:boolean,
    parentSub?:boolean,
    common: number,
    control: boolean,
    orgs: {
        [key:string]: { owners?: Array<string>, total?: number, identical?: number, parent?:string, asg?: 'A'|'B'|'M'|'FSO' }
    },
    key:string
}

type CtrlResult = { [key:string]: Ctrl }
type Owner = {
    id: string,
    percent: number,
    position?: string,
    attribute?: Array<string>,
    idService: string,
    [key:string]:any
}
type Org = { _id: string, owners: Array<Owner>, attributedOwners?:Array<Owner> } & AnyObj

const getAttributedOrgs = (orgs:Array<Org>) => {
    const attributeOwner = (org_set:Array<Org>):Array<Org> => {
        for(const org of org_set){
            //get all related owners and attribute ownership to each
            for(const ownr of org.attributedOwners || []){
                for(const org2 of org_set){
                    for (const owner of org2.attributedOwners || []){
                        if(owner.id !== ownr.id && owner.attribute?.includes(ownr.id)) ownr.percent += owner.percent || 0;
                    }
                }
            }
        }
        return org_set;
    }

    //get full orgs that own the org and split ownership up to the individual members for purposes of brother-sister control group calcs
    const indirectOwners = (org:Org):Org => {
        const ownerOrgs:Array<Org> = (_get(org, '_fastjoin.owners.orgs.data', []) || []);
        const individualOwners:Array<Owner|Array<Owner>> = [];
        for(const owner of (org.owners || []) as Array<Owner>){
            if(owner.idService === 'ppls') individualOwners.push(owner);
            else {
                for(const ownerOrg of ownerOrgs){
                    if(ownerOrg._id === owner.id){
                        const indirect = indirectOwners(ownerOrg);
                        if(indirect.attributedOwners) individualOwners.push(indirect.attributedOwners)
                    }
                }
            }
        }
        return { ...org, attributedOwners: _flatten(individualOwners.map(a => Array.isArray(a) ? a : [a])) };
    }

    return attributeOwner(orgs.map(a => indirectOwners(a)));
}
export const getCapTable = (oRgs:Array<Org>):{ orgs: OrgObj, owners: OrgObj, management:  MgmtObj, orgRecords: Array<Org>, attributedOrgs: Array<Org> } => {
    const orgs = getAttributedOrgs(oRgs);
    const obj:{ orgs: OrgObj, owners: OrgObj, management: MgmtObj } = {
        orgs: {},
        owners: {},
        management: {}
    };

    const setOrgs = (org:Org, owner:Owner) => {
        //add percentages by org id
        obj.orgs[org._id] ? obj.orgs[org._id][owner.id] = owner.percent : obj.orgs[org._id] = { [owner.id]: owner.percent};

        //add percentages by owner id
        obj.owners[owner.id] ? obj.owners[owner.id][org._id] = owner.percent : obj.owners[owner.id] = { [org._id]: owner.percent };

        //add managment orgs
        obj.management[org._id] = (org.managementOrgs || [])
    }

    for(const org of orgs){
        (org?.attributedOwners || []).filter(a => !!a).map(a => setOrgs(org, a));
    }

    return {...obj, orgRecords: oRgs, attributedOrgs: orgs };
}
export const getControls = (orgObj:OrgObj, mgmtObj:MgmtObj): { data: CtrlResult, ctrls: CtrlResult } => {

    const obj:CtrlResult = {};
    const ctrls:string[] = [];
    const addCommon = (ids:Array<string>) => {

        const com:Ctrl = {
            identical: 0,
            common: 0,
            control: false,
            orgs: {},
            key: ids.join('.')
        }


        //check for control group among any list of org ids - pass/fail all ids are control group, no partial matches
        for(const id of ids){
            //get top owners sorted by common in all orgs

            const ownrs = Object.keys(orgObj[id] || {}).sort((a, b) => {
                return (orgObj[id][b] || 0 - orgObj[id][a] || 0) || (!ids.filter(d => d !== id).some(b => !orgObj[b][a]) ? 1 : -1)
            }).slice(0, 5);

            //determine commono ownership between each respective combination
            for(const owner of ownrs){
                const val = orgObj[id][owner] || 0;
                const parent = val >= 80 ? owner : '';
                //least common ownership
                const identical = Math.min(...ids.map(a => orgObj[a][owner] || 0))
                // console.log('identical', id, owner, identical, val, com.orgs);
                //total ownership from 5 highest common owners or if no common owners - 5 highest by percent.
                com.orgs[id] = { total: (_get(com.orgs, [id, 'total'], 0) || 0) + val, identical: (_get(com.orgs, [id, 'identical'], 0) || 0) + identical, owners: ownrs, parent }
            }
            // for(const mOrg of (mgmtObj || {[id]: []})[id]){
            //     com.orgs[mOrg] = {
            //         ...com.orgs[mOrg],
            //         asg: 'M'
            //     }
            // }
        }


        const oRGs = com.orgs || {};
        const orgKeys = Object.keys(oRGs);
        const common = Math.min(...orgKeys.map(a => oRGs[a]?.total || 0))
        const identical = Math.min(...orgKeys.map(a => oRGs[a]?.identical || 0))

        com.identical = identical;
        com.common = common;

        const brotherSister = identical > 50 && common >= 80;
        com.brotherSister = brotherSister;
        const parentSubsidiary = orgKeys.filter(a => !!com.orgs[a].parent && ids.includes(com.orgs[a].parent as string)).length === orgKeys.length - 1;
        com.parentSub = parentSubsidiary


        //TODO: this is not perfect because the top 5 owners don't have to be identical between businesses
        if(brotherSister || parentSubsidiary){
            com.control = true;
            // ctrls.push(com.key);
        }

        return com;
    };


    const loop = (loopObj:AnyObj):CtrlResult => {
        const keyCount = Object.keys(orgObj).length;

        for(let i = 1; i < keyCount; i++) {
            const orgKeys = Object.keys(loopObj);
            for (const key of orgKeys) {
                for (const otherKey of orgKeys.filter(a => a !== key)) {
                    //only check if the individual parts of the new key are individual groups or control groups
                    const keyIsOrgOrControlGroup = !obj[key] || obj[key].control;
                    const otherKeyIsOrgOrControlGroup = !obj[key] || obj[key].control;
                    const isPossible = keyIsOrgOrControlGroup && otherKeyIsOrgOrControlGroup;

                    //only check if there isn't already an org key that contains all components of the new key - run the control check
                    const groupCombinationHasNotBeenChecked = !Object.keys(obj).map(a => a.split('.')).some(a => ![...key.split('.'), otherKey].some(k => !a.includes(k)))


                    if (isPossible && groupCombinationHasNotBeenChecked) {
                        const combinedKey = `${key}.${otherKey}`;
                        const com = addCommon(combinedKey.split('.'));
                        obj[`${combinedKey}`] = com;
                        if (com.control) {
                            ctrls.push(`${key}.${otherKey}`);
                        }
                    }
                }
            }

            //if we have not gone through a combination of every possible control group adding up to a group with as many sub-groups as there are groups in question - loop again
            const keyLength = Object.keys(obj).map(a => a.split('.').sort((a, b) => b.length - a.length))[0]?.length;
            if(keyLength >= keyCount) break;
            else break;
        }
        return obj;
    }

    const loopObj = loop(orgObj);
    const controlObj:CtrlResult = {};
    for(const key of ctrls){
        for(const cg in loopObj[key]){

        }
    }
    ctrls.forEach(key => controlObj[key] = loopObj[key]);
    return { data: loopObj, ctrls: controlObj }
}
