// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Markets, MarketsData, MarketsPatch, MarketsQuery, MarketsService } from './markets.class.js'

export type { Markets, MarketsData, MarketsPatch, MarketsQuery }

export type MarketsClientService = Pick<MarketsService<Params<MarketsQuery>>, (typeof marketsMethods)[number]>

export const marketsPath = 'markets'

export const marketsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const marketsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(marketsPath, connection.service(marketsPath), {
    methods: marketsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [marketsPath]: MarketsClientService
  }
}
