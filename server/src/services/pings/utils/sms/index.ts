// Download the helper library from https://www.twilio.com/docs/node/install
// Find your Account SID and Auth Token at twilio.com/console
// and set the environment variables. See http://twil.io/secure
// @ts-ignore
import tClient  from 'twilio';


type SendOptions = {
  message:string,
  link:string,
  to:string,
  key:string,
  id:string,
  management_url: string
}
export const sendPingSms = async ({ message, link, to, key, id, management_url}:SendOptions) => {
  const client = tClient(id, key);
  return await client.messages
      .create({from: '+***********', body: `${message}.\nGo to ${link}.\n Unsubscribe here: ${management_url}`, to })
      .then(message => console.log(message.sid))
      .catch(err => console.log(err));
};


