// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client'
import type { Ledgers, LedgersData, LedgersPatch, LedgersQuery, LedgersService } from './ledgers.class'

export type { Ledgers, LedgersData, LedgersPatch, LedgersQuery }

export type LedgersClientService = Pick<LedgersService<Params<LedgersQuery>>, (typeof ledgersMethods)[number]>

export const ledgersPath = 'ledgers'

export const ledgersMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const ledgersClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(ledgersPath, connection.service(ledgersPath), {
    methods: ledgersMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [ledgersPath]: LedgersClientService
  }
}
