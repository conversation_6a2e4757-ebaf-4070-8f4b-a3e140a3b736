// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Issues, IssuesData, IssuesPatch, IssuesQuery, IssuesService } from './issues.class.js'

export type { Issues, IssuesData, IssuesPatch, IssuesQuery }

export type IssuesClientService = Pick<IssuesService<Params<IssuesQuery>>, (typeof issuesMethods)[number]>

export const issuesPath = 'issues'

export const issuesMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const issuesClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(issuesPath, connection.service(issuesPath), {
    methods: issuesMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [issuesPath]: IssuesClientService
  }
}
