// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Ims, ImsData, ImsPatch, ImsQuery, ImsService } from './ims.class.js'

export type { Ims, ImsData, ImsPatch, ImsQuery }

export type ImsClientService = Pick<ImsService<Params<ImsQuery>>, (typeof imsMethods)[number]>

export const imsPath = 'ims'

export const imsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const imsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(imsPath, connection.service(imsPath), {
    methods: imsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [imsPath]: ImsClientService
  }
}
