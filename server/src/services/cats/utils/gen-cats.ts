import {HookContext} from '../../../declarations.js';
import {CoreCall} from 'feathers-ucan';
import {ObjectId} from 'mongodb';

const codeCategories: any = [
    {
        key: "surgery",
        name: "Surgery",
        regex: /^(10021|[1-6]\d{4}|69990)$/
    },
    {
        key: "radiology",
        name: "Radiology",
        regex: /^(70010|7\d{4}|79999)$/
    },
    {
        key: "labs",
        name: "Pathology & Laboratory",
        regex: /^(80047|8\d{4}|89398)$/
    },
    {
        key: "medicine",
        name: "Medicine (Including Vaccines & Psychiatry)",
        regex: /^(90281|9\d{4}|99199)$/
    },
    {
        key: "em",
        name: "Evaluation & Management (E/M)",
        regex: /^(99202|99[2-4]\d{2}|99499)$/
    },
    {
        key: "anesthesia",
        name: "Anesthesia",
        regex: /^(00[1-9]\d{2}|0[1-9]\d{3}|01999)$/
    },
    {
        key: "pt",
        name: "Physical Medicine & Rehabilitation",
        regex: /^(97010|97[0-7]\d{2}|97799)$/
    },
    {
        key: "vaccine",
        name: "Immunization Administration",
        regex: /^(90460|9046[0-9]|9047[0-4])$/
    },
    {
        key: "mental",
        name: "Mental Health Services",
        regex: /^(90785|90[7-8]\d{2}|90899)$/
    },
    {
        key: "special_eval",
        name: "Special Evaluation Services",
        regex: /^(99450|994[5-9]\d)$/
    },
    // HCPCS Categories
    {
        key: "a_codes",
        name: "Medical Supplies & Transportation",
        regex: /^A\d{4}$/
    },
    {
        key: "b_codes",
        name: "Enteral & Parenteral Therapy",
        regex: /^B\d{4}$/
    },
    {
        key: "c_codes",
        name: "Hospital Outpatient Procedures",
        regex: /^C\d{4}$/
    },
    {
        key: "d_codes",
        name: "Dental Services",
        regex: /^D\d{4}$/
    },
    {
        key: "e_codes",
        name: "Durable Medical Equipment (DME)",
        regex: /^E\d{4}$/
    },
    {
        key: "g_codes",
        name: "Miscellaneous Procedures & Services",
        regex: /^G\d{4}$/
    },
    {
        key: "h_codes",
        name: "Mental Health & Substance Abuse",
        regex: /^H\d{4}$/
    },
    {
        key: "j_codes",
        name: "Medications & Injectable Drugs",
        regex: /^J\d{4}$/
    },
    {
        key: "k_codes",
        name: "DME for Medicare Contractors",
        regex: /^K\d{4}$/
    },
    {
        key: "l_codes",
        name: "Orthotics & Prosthetics",
        regex: /^L\d{4}$/
    },
    {
        key: "m_codes",
        name: "Medical Services Not in CPT",
        regex: /^M\d{4}$/
    },
    {
        key: "p_codes",
        name: "Pathology & Laboratory Services",
        regex: /^P\d{4}$/
    },
    {
        key: "q_codes",
        name: "Temporary Codes for Miscellaneous Services",
        regex: /^Q\d{4}$/
    },
    {
        key: "r_codes",
        name: "Radiology & Imaging",
        regex: /^R\d{4}$/
    },
    {
        key: "s_codes",
        name: "Temporary Private Payer Codes",
        regex: /^S\d{4}$/
    },
    {
        key: "t_codes",
        name: "Medicaid-Only Services",
        regex: /^T\d{4}$/
    }
];
const emergency = {
    code_regex: /^9928[1-5]$/,
    name: 'Emergency Room'
}

/**Used to add searchable categories to procedsures so cats can have mass procedure ids added by category. If more are added to the codeCateogires above, can be used to add to procedures */
export const nameCats = async (context: HookContext) => {
    const ps: any = await context.app.service('procedures')._find({
        skip_hooks: true, admin_pass: true,
        query: {
            $skip: context.params.query.$skip,
            $limit: context.params.query.$limit,
            $select: ['_id', 'code']
        }
    } as any)

    const entries: any = {};

    for (let i = 0; i < ps.data.length; i++) {
        if (emergency.code_regex.test(ps.data[i].code)) {
            if (entries.emergency) entries.emergency.push(ps.data[i]._id)
            else entries.emergency = [ps.data[i]._id]
            break;
        }
        for (let c = 0; c < codeCategories.length; c++) {
            if (codeCategories[c].regex.test(ps.data[i].code)) {
                if (entries[codeCategories[c].key]) entries[codeCategories[c].key].push(ps.data[i]._id)
                else entries[codeCategories[c].key] = [ps.data[i]._id]
                break;
            }
        }
    }

    const promises: any = [];
    for (const k in entries) {
        if(entries[k].length) promises.push(new CoreCall('procedures', context)._patch(null, {category: codeCategories.filter(a => a.key === k)[0].name}, {skip_hooks: true, admin_pass: true, query: {_id: {$in: entries[k].map(a => typeof a === 'string' ? ObjectId.createFromHexString(a) : a)}}})
            .catch(err => console.log(`Could not create cat: ${entries[k]} ----- ${err.message}`)))
    }
    await Promise.all(promises)
}

/**Generate new cats from codeCategories */
export const initCats = async (context: HookContext) => {
    const ps: any = await context.app.service('procedures')._find({
        skip_hooks: true,
        admin_pass: true,
        query: {
            $skip: context.params.query.$skip,
            $limit: context.params.query.$limit,
            $select: ['_id', 'code']
        }
    } as any)


    const entries: any = {}

    for (let i = 0; i < ps.data.length; i++) {
        if (emergency.code_regex.test(ps.data[i].code)) {
            if (entries.emergency) entries.emergency.procedures.push(ps.data[i]._id)
            else entries.emergency = {
                ...emergency,
                code_regex: String(emergency.code_regex),
                procedures: [ps.data[i]._id]
            }
            break;
        }
        for (let c = 0; c < codeCategories.length; c++) {
            if (codeCategories[c].regex.test(ps.data[i].code)) {
                if (entries[codeCategories[c].key]) {
                    entries[codeCategories[c].key].procedures.push(ps.data[i]._id)
                } else entries[codeCategories[c].key] = {
                    name: codeCategories[c].name,
                    code_regex: String(codeCategories[c].regex),
                    procedures: [ps.data[i]._id]
                }
            }
        }
    }

    const promises: any = [];
    const existing = await new CoreCall('cats', context).find({
        query: {
            $limit: 30,
            name: {$in: Object.keys(entries).map(a => entries[a].name)}
        }
    })
    for (const k in entries) {
        const filtered = existing.data.filter(a => a.name === entries[k].name)
        if (filtered[0]) {
            promises.push(new CoreCall('cats', context).patch(filtered[0]._id, {$addToSet: {procedures: {$each: entries[k].procedures}}})
                .catch(err => console.log(`Could not patch cat: ${entries[k]} ----- ${err.message}`)))
        } else promises.push(new CoreCall('cats', context).create(entries[k])
            .catch(err => console.log(`Could not create cat: ${entries[k]} ----- ${err.message}`)))
    }
    await Promise.all(promises)
    return context;
}
