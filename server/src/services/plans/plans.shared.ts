// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Plans, PlansData, PlansPatch, PlansQuery, PlansService } from './plans.class.js'

export type { Plans, PlansData, PlansPatch, PlansQuery }

export type PlansClientService = Pick<PlansService<Params<PlansQuery>>, (typeof plansMethods)[number]>

export const plansPath = 'plans'

export const plansMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const plansClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(plansPath, connection.service(plansPath), {
    methods: plansMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [plansPath]: PlansClientService
  }
}
