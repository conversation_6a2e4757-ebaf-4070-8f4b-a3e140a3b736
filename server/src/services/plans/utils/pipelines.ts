export const no_enrollments = (versions: Array<string>) => [
    //ALL PLAN ENROLLMENTS
    {
        $lookup: {
            from: 'enrollments',
            as: 'plan_enrollments',
            let: {'planId': '$_id'},
            pipeline: [
                {
                    $match: {
                        $and: [
                            {deleted: {$ne: true}},
                            {$expr: {$eq: ['$plan', '$$planId']}},
                            {version: {$in: versions || []}}
                        ]
                    }
                }
            ]
        }
    },
    {
        $addFields: {
            enrollment_people: {
                $map: {
                    input: '$plan_enrollments',
                    as: 'item',
                    in: '$$item.person'
                }
            }
        }
    },
    {
        $lookup: {
            from: 'groups',
            as: 'plan_groups',
            localField: 'groups',
            foreignField: '_id'
        }
    },
    {
        $addFields: {
            groups_with_members: {
                $filter: {
                    input: '$plan_groups',
                    as: 'item',
                    cond: {
                        $gte: ['$$item.members', 0]
                    }
                }
            }
        }
    },
    {
        $addFields: {
            deep_members: {
                $map: {
                    input: '$groups_with_members',
                    as: 'item',
                    in: '$$item.members'
                }
            }
        }
    },
    {
        $addFields: {
            all_members: {
                $reduce: {
                    input: '$deep_members',
                    initialValue: [],
                    in: {$concatArrays: ['$$value', '$$this']}
                }
            }
        }
    },
    {
        $lookup: {
            from: 'ppls',
            as: 'no_enrollment',
            let: {all_members: '$all_members', enrollment_people: '$enrollment_people'},
            pipeline: [
                {
                    $match: {
                        $expr: {
                            $and: [
                                {$ne: ['$deleted', true]},
                                {$in: ['$_id', '$$all_members']},
                                {$not: {$in: ['$_id', '$$enrollment_people']}}
                            ]
                        }
                    }
                }
            ]
        }
    },
    {
        $project: {
            'all_members': 0,
            'deep_members': 0,
            'plan_groups': 0,
            'plan_enrollments': 0,
            'enrollment_people': 0,
            'groups_with_members': 0
        }
    }
]

export const noCams = () => [
    {
        $lookup: {
            from: 'groups',
            as: 'plan_groups',
            localField: 'groups',
            foreignField: '_id'
        }
    },
    {
        $addFields: {
            groups_with_members: {
                $filter: {
                    input: '$plan_groups',
                    as: 'item',
                    cond: {
                        $gte: ['$$item.members', 0]
                    }
                }
            }
        }
    },
    {
        $addFields: {
            deep_members: {
                $map: {
                    input: '$groups_with_members',
                    as: 'item',
                    in: '$$item.members'
                }
            }
        }
    },
    {
        $addFields: {
            all_members: {
                $reduce: {
                    input: '$deep_members',
                    initialValue: [],
                    in: {$concatArrays: ['$$value', '$$this']}
                }
            }
        }
    },
    {
        $lookup: {
            from: 'cams',
            as: 'plan_member_cams',
            let: {all_members: '$all_members'},
            pipeline: [
                {
                    $match: {
                        $expr: {
                            $and: [
                                {$in: ['$person', '$$all_members']},
                                {$eq: ['$org', '$org']}
                            ]
                        }
                    }
                }
            ]
        }
    },
    {
        $addFields: {
            plan_member_cams_people: {
                $map: {
                    input: '$plan_member_cams',
                    as: 'item',
                    in: '$$item.person'
                }
            }
        }
    },
    {
        $lookup: {
            from: 'ppls',
            as: 'no_cams',
            let: {all_members: '$all_members', plan_member_cams_people: '$plan_member_cams_people'},
            pipeline: [
                {
                    $match: {
                        $expr: {
                            $and: [
                                {$in: ['$_id', '$$all_members']},
                                {$not: {$in: ['$_id', '$$plan_member_cams_people']}}
                            ]
                        }
                    }
                }
            ]
        }
    },
    {
        $project: {
            plan_member_cams_people: 0,
            plan_member_cams: 0,
            deep_members: 0,
            all_members: 0,
            plan_groups: 0
        }
    }
]

export const totalContributions = (plan:any) => {
    const { coverages, cafe } = plan;
    const flatCoverageGroup:any = {};
    const flatCoverageProject:any = {};
    for(const k in coverages){
        flatCoverageGroup[`er_coverage_${k}`] = { $sum: `$contributions.byCoverage.${k}.employer`};
        flatCoverageGroup[`ee_coverage_${k}`] = {$sum: `$contributions.byCoverage.${k}.employee`};
        flatCoverageGroup[`coverage_${k}`] = {$sum: `$contributions.byCoverage.${k}.needed`};
        flatCoverageProject[k] = {
            employer: `$er_coverage_${k}`,
            employee: `$ee_coverage_${k}`,
            needed: `$coverage_${k}`
        }
    }
    const flatCafeGroup:any = {};
    const flatCafeProject:any = {};
    for(const k in cafe){
        flatCafeGroup[k] = {$sum: `$contributions.byPlan.${k}`}
        flatCafeProject[k] = `$${k}`
    }
    return [
        // {
        // $match: {
        //     // Add your match conditions here
        //     version: {$eq: version},
        //     plan: {$eq: planId}
        // }
        // },
        {
            $sort: {
                person: 1,
                version: -1
            }
        },
        {
            $group: {
                _id: "$person",
                latestEnrollment: { $first: "$$ROOT"}
            }
        },
        {
            "$replaceRoot": { "newRoot": "$latestEnrollment" }
        },
        {
            $group: {
                _id: null,
                employee_total: {$sum: "$contributions.employee.total"},
                employee_postTax: {$sum: "$contributions.employee.postTax"},
                employee_def: {$sum: "$contributions.employee.def"},
                employee_preTax: {$sum: "$contributions.employee.preTax"},
                employer_cafe: {$sum: "$contributions.employer.cafe"},
                employer_coverages: {$sum: "$contributions.employer.coverages"},
                needed_total: {$sum: "$contributions.needed.total"},
                needed_postTax: {$sum: "$contributions.needed.postTax"},
                needed_preTax: {$sum: "$contributions.needed.preTax"},
                ...flatCoverageGroup,
                ...flatCafeGroup
            }
        },
        {
            $project: {
                _id: 0,
                contributions: {
                    employee: {
                        total: "$employee_total",
                        postTax: "$employee_postTax",
                        def: "$employee_def",
                        preTax: "$employee_preTax"
                    },
                    employer: {
                        cafe: "$employer_cafe",
                        coverages: "$employer_coverages"
                    },
                    needed: {
                        total: "$needed_total",
                        postTax: "$needed_postTax",
                        preTax: "$needed_preTax"
                    },
                    byPlan: flatCafeProject,
                    byCoverage: flatCoverageProject
                }
            }
        }
    ]
}
