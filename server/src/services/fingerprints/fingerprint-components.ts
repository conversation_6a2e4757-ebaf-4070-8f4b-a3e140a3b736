import {ObjectIdSchema} from "@feathersjs/schema";

export default {
    type: 'object',
    additionalProperties: true,
    properties: {
        audio: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'number'}
            }
        },
        canvas: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {
                        winding: {type: 'boolean'},
                        geometry: {type: 'string'},
                        text: {type: 'string'}
                    }
                }
            }
        },
        colorDepth: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'number'}
            }
        },
        colorGamut: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'string'}
            }
        },
        contrast: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'number'}
            }
        },
        cookiesEnabled: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'boolean'}
            }
        },
        cpuClass: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'string'}
            }
        },
        deviceMemory: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'number'}
            }
        },
        domBlockers: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'array', items: {type: 'string'}}
            }
        },
        fontPreferences: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {
                        default: {type: 'number'},
                        apple: {type: 'number'},
                        serif: {type: 'number'},
                        sans: {type: 'number'},
                        mono: {type: 'number'},
                        min: {type: 'number'},
                        system: {type: 'number'}
                    }
                }
            }
        },
        fonts: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'array', items: {type: 'string'}}
            },
        },
        forcedColors: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'boolean'}
            }
        },
        hardwareConcurrency: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'number'}
            }
        },
        hdr: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'boolean'}
            }
        },
        indexedDB: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'boolean'}
            }
        },
        invertedColors: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'boolean'}
            }
        },
        languages: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {
                    type: 'array', items: {type: 'array', items: {type: 'string'}}
                }
            }
        },
        localStorage: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'boolean'}
            }
        },
        math: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {
                        acos: {type: 'number'},
                        acosh: {type: 'number'},
                        acoshPf: {type: 'number'},
                        asin: {type: 'number'},
                        asinh: {type: 'number'},
                        asinhPf: {type: 'number'},
                        atan: {type: 'number'},
                        atanh: {type: 'number'},
                        atanhPf: {type: 'number'},
                        cos: {type: 'number'},
                        cosh: {type: 'number'},
                        coshPf: {type: 'number'},
                        exp: {type: 'number'},
                        exm1: {type: 'number'},
                        expm1Pf: {type: 'number'},
                        log1p: {type: 'number'},
                        log1pPf: {type: 'number'},
                        powPI: {type: 'number'},
                        sin: {type: 'number'},
                        sinh: {type: 'number'},
                        sinhPf: {type: 'number'},
                        tan: {type: 'number'},
                        tahh: {type: 'number'},
                        tanhPf: {type: 'number'}
                    }
                }
            }
        },
        monochrome: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'number'}
            }
        },
        openDatabase: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'boolean'}
            }
        },
        osCpu: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'string'}
            }
        },
        platform: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'string'}
            }
        },
        plugins: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {
                    type: 'array', items: {
                        type: 'object',
                        additionalProperties: true,
                        properties: {
                            name: {type: 'string'},
                            description: {type: 'string'},
                            mimetypes: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    additionalProperties: true,
                                    properties: {type: {type: 'string'}, suffixes: {type: 'string'}}
                                }
                            }
                        }
                    }
                }
            }
        },
        refNames: {type: 'array', items: {type: 'string'}},
        refIds: {type: 'array', items: ObjectIdSchema()},
        reducedMotion: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'boolean'}
            }
        },
        screenFrame: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'array', items: {type: 'number'}}
            }
        },
        screenResolution: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'array', items: {type: 'number'}}
            }
        },
        sessionStorage: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'boolean'}
            }
        },
        timezone: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'string'}
            }
        },
        touchSupport: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {
                    type: 'object',
                    additionalProperties: true,
                    properties: {
                        maxTouchPoints: {type: 'number'},
                        touchEvent: {type: 'boolean'},
                        touchStart: {type: 'boolean'}
                    }
                }
            }
        },
        vendor: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {type: 'string'}
            }
        },
        vendorFlavors: {
            type: 'object',
            additionalProperties: true,
            properties: {
                duration: {type: 'number'},
                value: {
                    type: 'array',
                    items: {type: 'string'}
                }
            }
        }
    }
} as const;
