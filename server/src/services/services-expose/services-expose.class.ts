// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#custom-services
import type { Id, NullableId, Params, ServiceInterface } from '@feathersjs/feathers'


import type { Application } from '../../declarations.js'

type ServicesExpose = any
type ServicesExposeData = any
type ServicesExposePatch = any
type ServicesExposeQuery = any

export type { ServicesExpose, ServicesExposeData, ServicesExposePatch, ServicesExposeQuery }

export interface ServicesExposeServiceOptions {
  app: Application
}

export interface ServicesExposeParams extends Params<ServicesExposeQuery> {}

// This is a skeleton for a custom service class. Remove or add the methods you need here
export class ServicesExposeService<ServiceParams extends ServicesExposeParams = ServicesExposeParams>
  implements ServiceInterface<ServicesExpose, ServicesExposeData, ServiceParams, ServicesExposePatch>
{
  constructor(public options: ServicesExposeServiceOptions) {}

  async find(_params?: ServiceParams): Promise<ServicesExpose[]> {
    return [];
  }

  async get(id: Id, _params?: ServiceParams): Promise<ServicesExpose> {
    return {
      id: 0,
      text: `A new message with ID: ${id}!`
    }
  }

  async create(data: ServicesExposeData, params?: ServiceParams): Promise<ServicesExpose>
  async create(data: ServicesExposeData[], params?: ServiceParams): Promise<ServicesExpose[]>
  async create(
    data: ServicesExposeData | ServicesExposeData[],
    params?: ServiceParams
  ): Promise<ServicesExpose | ServicesExpose[]> {
    if (Array.isArray(data)) {
      return Promise.all(data.map((current) => this.create(current, params)))
    }

    return {
      id: 0,
      ...data
    }
  }

  // This method has to be added to the 'methods' option to make it available to clients
  async update(id: NullableId, data: ServicesExposeData, _params?: ServiceParams): Promise<ServicesExpose> {
    return {
      id: 0,
      ...data
    }
  }

  async patch(id: NullableId, data: ServicesExposePatch, _params?: ServiceParams): Promise<ServicesExpose> {
    return {
      id: 0,
      text: `Fallback for ${id}`,
      ...data
    }
  }

  async remove(id: NullableId, _params?: ServiceParams): Promise<ServicesExpose> {
    return {
      id: 0,
      text: 'removed'
    }
  }
}

export const getOptions = (app: Application) => {
  return { app }
}
