// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import {hooks as schemaHooks} from '@feathersjs/schema'

import {
    gpsDataValidator,
    gpsPatchValidator,
    gpsQueryValidator,
    gpsResolver,
    gpsExternalResolver,
    gpsDataResolver,
    gpsPatchResolver,
    gpsQueryResolver
} from './gps.schema.js'

import {Application, HookContext} from '../../declarations.js'
import {GpsService, getOptions} from './gps.class.js'
import {gpsPath, gpsMethods} from './gps.shared.js'

export * from './gps.class.js'
export * from './gps.schema.js'

import {allUcanAuth, CapabilityParts, CoreCall, loadExists, noThrow, setExists} from 'feathers-ucan';
import {logChange, _set} from '../../utils/index.js';

const authenticate = async (context: HookContext) => {
    const writer = [['ims', 'WRITE']] as Array<CapabilityParts>;
    const deleter = [['ims', '*']] as Array<CapabilityParts>;
    const ucanArgs: any = {
        create: noThrow,
        patch: [...writer],
        update: [...writer],
        remove: [...deleter]
    };
    const cap_subjects: any = []

    if (context.params.special_change) ucanArgs.patch = noThrow
    else if (context.method === 'patch') {
        const existing = await loadExists(context, {params: {runJoin: {ims_person: true}}});
        context = setExists(context, existing);
        if (!existing.owner && !existing.org) {
            ucanArgs.patch = noThrow
        } else {
            if (existing.org) {
                cap_subjects.push(existing.org)
                ucanArgs.patch.unshift([`orgs:${existing.org}`, 'orgAdmin'])
                ucanArgs.patch.unshift([`orgs:${existing.org}`, 'planAdmin'])
            }
        }
    }
    return await allUcanAuth<HookContext>(ucanArgs, {
        adminPass: ['patch', 'create', 'remove'],
        or: '*',
        loginPass: [[['owner/owner'], '*']],
        creatorPass: '*',
        cap_subjects
    })(context)
}

import { aiUpload } from './utils/ai-upload.js';
import multer from 'multer';
import {employer_plan_compare} from './utils/ai-chat.js';
import {fillFixedRates, fixFixedRates} from '../coverages/utils/rates.js';

const multipartMiddleware = multer();
const restMiddleware = async (req, res, next) => {
    if(req.query.runJoin?.singleFile) await new Promise((resolve) => multipartMiddleware.single('file')(req, res, resolve));
    else await new Promise((resolve) => multipartMiddleware.array('files', 5)(req, res, resolve));
    req.feathers.files = req.files;
    if(!req.files?.length) {
        req.feathers.file = req.file;
        req.feathers.files = [req.file];
    }
    delete req.file
    req.feathers.runJoin = req.query.runJoin;
    req.feathers.core = req.query.core
    return next();
}

const checkEmployees = async (context: HookContext) => {
    let path = 'employees'
    let employees = context.data.employees;
    if(!employees){
        path = '$set.employees';
        employees = context.data.$set?.employees;
    }
    if(!employees){
        path = '$addToSet.employees.$each';
        employees = context.data.$addToSet?.employees?.$each;
    }
    if(!employees){
        path = '$addToSet.employees';
        employees = context.data.$addToSet?.employees;
        if(employees) employees = [employees];
    }
    if(employees?.length){
        const noGpps:any = [];
        const yesGpps:any = [];
        for(const ee of employees){
            if(ee.gpps) yesGpps.push(ee);
            else noGpps.push(ee);
        }
        if(noGpps.length){
            for(let i = 0; i < noGpps.length; i++){
                const ee = noGpps[i];
                const zips = noGpps.map(a => a.zip).filter(a => !!a);
                let drawers:any = { data: [] };
                if(zips.length){
                    drawers = await new CoreCall('junk-drawers', context).find({
                        query: {
                            $limit: zips.length,
                            itemId: {$in: zips.map(a => `zips|${a.slice(0, 3)}`)}
                        }
                    })
                        .catch(err => {
                            console.log(`Error getting zip drawer: ${err.message}`)
                            return drawers;
                        })

                }
                /** convert employee for gpps */
                const obj:any = { gps: context.id, name: ee.firstName + ' ' + ee.lastName, email: ee.email || undefined, income: ee.income || undefined }
                /** construct people */
                const people:any = []
                if(ee.married?.toLowerCase() === 'y') people.push({ age: ee.age, relation: 'spouse'})
                if(ee.deps) {
                    for(let i = 0; i < ee.deps; i++) people.push({ age: 10, relation: 'child' })
                }
                const household:any = { people }
                /** add place if a zip is present */
                if(ee.zip){
                    const drawer:any = drawers.data.find(a => a.itemId === `zips|${ee.zip.slice(0, 3)}`)
                    if(drawer) {
                        household.place = { zipcode: ee.zip, countyfips: drawer.data[ee.zip].fips, state: drawer.data[ee.zip].state }
                    }
                }
                obj.household = household
                const added = await new CoreCall('gpps', context).create(obj).catch(err => console.log(`Error adding gpps: ${err.message}`));
                if(added?._id) context.data = _set(context.data, `${path}.${i}.gpps`, added._id)
            }
        }
        if(yesGpps.length){
            const promises:any = [];
            for(const ee of yesGpps){
                if(!ee.coverage) continue;
                promises.push(new CoreCall('gpps', context).patch(ee.gpps, { majorMedical:  ee.coverage })
                    .catch(err => console.log(`Error patching gpps with employee coverage: ${err.message}`)))
            }
            await Promise.all(promises)
        }

    }
    return context;
}

const fillRates = async (context: HookContext) => {
    if(context.data.$set){
        for(const k in context.data.$set){
            if(/^coverages\.[^.]+\.premium$/.test(k)){
                let existing = context.data;
                if(context.method !== 'create'){
                    existing = await loadExists(context);
                    context = setExists(context, existing);
                }
                const midKey = k.split('.')[1];
                const ex = (existing.coverages || {})[midKey] || {}
                const maxAge = context.data.$set[`coverages.${midKey}.maxAge`] || ex.maxAge || 120;
                context.data.$set[k] = fixFixedRates(context.data.$set[k], { maxAge, existing: ex.premium });

            }
        }
    }
    return context;
}

const addCompareId = (context:HookContext) => {
    if(context.data.coverges){
        for(const k in context.data.coverages){
            context.data.coverages[k].compare_id = k;
        }
    }
    if(context.data.$set){
        for(const k in context.data.$set){
            if(/^coverages\.[^.]+$/.test(k)){
                const id = k.split('.')[1];
                context.data.$set[`coverages.${id}.compare_id`] = id
            }
        }
    }
    return context;
}

import { uploadEmployees } from '../doc-requests/utils/upload-employees.js';
import {retotalSims, runGroupCostSim} from './utils/group-cost-sim.js';

// A configure function that registers the service and its hooks via `app.configure`
export const gps = (app: Application) => {
    // Register our service on the Feathers application
    app.use(gpsPath, restMiddleware, new GpsService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: gpsMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(gpsPath).hooks({
        around: {
            all: [
                schemaHooks.resolveExternal(gpsExternalResolver),
                schemaHooks.resolveResult(gpsResolver)
            ]
        },
        before: {
            all: [
                authenticate,
                logChange(),
                schemaHooks.validateQuery(gpsQueryValidator),
                schemaHooks.resolveQuery(gpsQueryResolver)
            ],
            find: [],
            get: [runGroupCostSim()],
            create: [
                addCompareId,
                schemaHooks.validateData(gpsDataValidator),
                schemaHooks.resolveData(gpsDataResolver)
            ],
            patch: [
                addCompareId,
                aiUpload,
                checkEmployees,
                schemaHooks.validateData(gpsPatchValidator),
                schemaHooks.resolveData(gpsPatchResolver),
                fillFixedRates,
                uploadEmployees,
                retotalSims
            ],
            remove: []
        },
        after: {
            all: [],
            patch: [aiUpload, uploadEmployees],
            get: [employer_plan_compare],
            create: []
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [gpsPath]: any
    }
}
