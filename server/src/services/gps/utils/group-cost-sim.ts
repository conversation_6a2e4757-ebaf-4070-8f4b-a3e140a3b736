import {HookContext} from '../../../declarations.js';
import {CoreCall, loadExists, setExists} from 'feathers-ucan';
import {fakeId, pointToGeo} from '../../../utils/index.js';
import {getCoverageRate, getFixedRateKey} from '../../enrollments/utils/index.js';
import {ObjectId} from 'mongodb';
import {cmsMarketplaceSearch} from '../../marketplace/cms/index.js'
type TotalSims = {
    ees:Array<any>,
    householdByEe: {[key:string]:any},
    shopObj: {[key:string]:any}
    coverages: {[key:string]:any}
}
const totalSims = ({ees, householdByEe, shopObj, coverages}:TotalSims) => {
    return async (context: HookContext) => {
        const prmDef = () => {
            return {
                single: 0,
                plus_spouse: 0,
                plus_child: 0,
                plus_child__2: 0,
                plus_child__3: 0,
                family: 0
            }
        }
        const currentStats: any = {
            count: 0,
            premium: 0,
            premiumByKey: {...prmDef()},
            countByKey: {...prmDef()},
            spend: 0,
            spendCount: 0
        };
        const simStats: any = {
            count: 0,
            spend: 0,
            premium: 0,
            altSpend: 0,
            altPremium: 0,
            premiumPtc: 0,
            spendPtc: 0,
            premiumByKey: {...prmDef()},
            altByKey: {...prmDef()},
            premiumByKeyPtc: {...prmDef()},
            ptc: 0,
            ptc_likely: 0,
            countByKey: {...prmDef()}
        };

        const coverageIds: any = {};
        for (const k in shopObj) {
            for (const id of shopObj[k].coverages) {
                coverageIds[id] = true;
            }
        }
        const fetchedCoverages = await new CoreCall('static-plans', context).find({
            query: {
                $limit: Object.keys(coverageIds).length,
                _id: {
                    $in: Object.keys(coverageIds),
                }
            }
        });

        const coveragesObj: any = {...coverages};
        for (const cov of fetchedCoverages.data) coveragesObj[cov._id] = cov;
        for (const ee of ees) {
            const enrolled = householdByEe[ee.uid].people
            const frKey = getFixedRateKey({enrolled})

            let premium;
            if (ee.coverage) {
                const cov = coveragesObj[ee.coverage];
                premium = getCoverageRate({coverage: cov, enrolled, def_key: frKey.key, def_age: ee.age})

                if (premium) {
                    currentStats.count++
                    currentStats.premium += premium
                    currentStats.countByKey[frKey.key]++;
                    currentStats.premiumByKey[frKey.key] += premium;
                }

            }
            if (ee.sim) {
                const shop = shopObj[ee.sim];
                simStats.ptc += (shop.aptc || 0) * 12;
                simStats.spend += shop.spend
                simStats.count++;
                let best: any = {};
                let alt: any = {};
                /** loop through and set best and alt coverages */
                for (const id of shop.coverages) {
                    const cov = coveragesObj[id];
                    const covId = cov.plan_id || cov.coverage || cov.compare_id
                    if (covId === ee.coverage) {
                        currentStats.spend += shop.coverage_scores[covId].average;
                        currentStats.spendCount++;
                        currentStats.spendPremium += premium;
                    }
                    const orig = shop.coverage_scores[covId];
                    const obj = {_id: covId, average: orig.average, premium: cov.premium};
                    if (cov.type === 'hs') {
                        if (!alt.average || alt.average > obj.average) alt = obj;
                        else if (cov.premium < best.premium) best = cov;
                    } else {
                        if (!best.average || best.average > obj.average) best = obj;
                    }
                }
                if (best.acaPlan) simStats.ptc_likely += (shop.aptc || 0) * 12;
                const w_ptc = shop.coverage_scores_ptc[best._id];
                if (w_ptc) {
                    simStats.premiumPtc += w_ptc.premium;
                    simStats.spendPtc += w_ptc.average;
                    simStats.premiumByKeyPtc[frKey.key] += w_ptc.premium;
                }
                simStats.spend += best.average;
                simStats.premium += best.premium;
                simStats.premiumByKey[frKey.key] += best.premium;
                simStats.countByKey[frKey.key]++;
                simStats.altSpend += alt.average;
                simStats.altPremium += alt.premium;
                simStats.altByKey[frKey.key] += alt.premium;

                ee.bestPlan = best._id;
                ee.altPlan = alt._id;
            }
        }
        return {ees, simStats, currentStats};
    }
}

const getZip = (z: any, zs:any) => {
    if(!zs) return;
    const tryZip = (z:any, tries = 0) => {
        const obj = zs[z];
        if (obj && typeof obj === 'object') return obj;
        else if (tries < 50) return tryZip(Number(z) + 1, tries + 1)
        else return;
    }
    return tryZip(z, 0);
}

export const retotalSims = async (context: HookContext) => {
    if(context.params.runJoin?.re_total_sims) {
       const ex = await loadExists(context);
       context = setExists(context, ex);
       const { employees = [] } = ex;
        const zips = employees.map(a => a.zip).filter(a => !!a);
        const zipIds = Array.from(new Set(zips.map(a => `zips|${a.slice(0, 3)}`)));
        const zipDrawers = await new CoreCall('junk-drawers', context).find({
            query: {
                itemId: { $in: zipIds },
                $limit: zipIds.length
            }
        })
        const zipObj: any = {};
        for (const d of zipDrawers.data) {
            zipObj[d.itemName] = d.data;
        }
        const householdByEe: any = {}

        for(const ee of employees) {
            const zipData = getZip(ee.zip, zipObj[ee.zip.slice(0, 3)])
            if(!zipData) {
                ee.simError = 'Invalid zip code'
                continue;
            }
            const household: any = {place: {zipcode: ee.zip, countyfips: zipData.fips, state: zipData.state}};
            const people: any = [{age: ee.age, relation: 'self', gender: ee.gender || 'male'}];
            if (ee.married?.toLowerCase().includes('Y')) people.push({
                age: ee.spouseAge || ee.age,
                gender: ee.gender === 'male' ? 'female' : 'male',
                relation: 'spouse'
            });
            for (let i = 0; i < ee.deps || 0; i++) {
                people.push({age: 10, relation: 'child', child: true, gender: 'male'})
            }
            household.people = people;
            if (ee.income) household.income = ee.hh_income || ee.income;
            householdByEe[ee.uid] = household;
        }

        const shopIds = employees.map(a => a.sim).filter(a => !!a);
        const shops = await new CoreCall('shops', context).find({ query: { _id: {$in: shopIds}, $limit: shopIds.length }})
        const shopObj:any = {}
        for(const shop of shops.data) shopObj[shop._id] = shop;

        const {ees, currentStats, simStats} = await totalSims({ees:employees, householdByEe, coverages: ex.coverages, shopObj})(context);

        context.data = { ...context.data, employees: ees, simStats, currentStats};
    }
    return context;
}

export const runGroupCostSim = () => {
    return async (context: HookContext) => {
        const {cost_sim} = context.params.runJoin || {};
        if (cost_sim) {

            let {exclude = [], employees, exclude_issuers, limit, risk, cms_issuers} = cost_sim;
            const ex = await loadExists(context);
            context = setExists(context, ex);

            const {coverages, plan:planId, host, ale} = ex;
            let plan:any = {};
            if(planId) {
                plan = await new CoreCall('plans', context).get(planId, {admin_pass: true})
                    .catch(err => console.log(`Could not get plan for cost sim: ${err.message}`))
            }
            if(!employees) employees = ex.employees;
            const zips = employees.map(a => a.zip).filter(a => !!a);
            const zipIds = Array.from(new Set(zips.map(a => `zips|${a.slice(0, 3)}`)));
            const zipDrawers = await new CoreCall('junk-drawers', context).find({
                query: {
                    itemId: { $in: zipIds },
                    $limit: zipIds.length
                }
            })
            const zipObj: any = {};
            for (const d of zipDrawers.data) {
                zipObj[d.itemName] = d.data;
            }

            const shopObj: any = {};
            const householdByEe: any = {};

            const eeCoverages:any = {};
            const eeCtx:any = {};
            const allCoverages:any = {};
            const allCms:any = {};
            const query:any = {
                    $or: [{geo: {$exists: false}}],
                    type: {$in: cost_sim.ichra ? ['mm'] : ['hs', 'mm']},
                    sim: true,
                    public: true,
                    $limit: 25,
                    $sort: {fortyPremium: 1},
                    fortyPremium: {$exists: true}
            }
            if (plan?._id) {
                if (!cost_sim.showPublic) delete query.public;
                query._id = {$in: Object.keys(plan.coverages || {}).map(a => ObjectId.createFromHexString(a))}
            }
            if (exclude_issuers) query.carrierName = {$nin: exclude_issuers}

            for(const ee of employees) {
                const zipData = getZip(ee.zip, zipObj[ee.zip.slice(0,3)])
                if (!zipData || typeof zipData !== 'object') {
                    ee.simError = 'Invalid zip code'
                    return ee;
                }
                const household: any = {place: {zipcode: ee.zip, countyfips: zipData.fips, state: zipData.state}};
                const people: any = [{age: ee.age, relation: 'self', gender: ee.gender || 'male'}];
                if (ee.married?.toLowerCase().includes('Y')) people.push({
                    age: ee.spouseAge || ee.age,
                    gender: ee.gender === 'male' ? 'female' : 'male',
                    relation: 'spouse'
                });
                for (let i = 0; i < ee.deps || 0; i++) {
                    people.push({age: 10, relation: 'child', child: true, gender: 'male'})
                }
                household.people = people;
                if (ee.income) household.income = ee.hh_income || ee.income;
                householdByEe[ee.uid] = household;
                const eeQuery:any = {...query}
                eeQuery.$or.push({['geo.geometry']: {$geoIntersects: {$geometry: pointToGeo(zipData.lngLat)?.geometry}}})

                const private_policies = await new CoreCall('coverages', context)._find({
                    skip_hooks: true, admin_pass: true,
                    query: eeQuery
                })

                for(const cov of private_policies.data) {
                    allCoverages[cov._id] = cov;
                }

                eeCoverages[ee.uid] = private_policies.data;
                context.params.query = {$limit: 80, household, place: household.place}

                context.params.quick_quote = {limit}
                if(cms_issuers?.length) context.params.query = { ...context.params.query, filter: { ...context.params.query.filter, issuers: cms_issuers }};
                const ctx = await cmsMarketplaceSearch(context)
                    .catch(err => console.log(`Error running cms marketplace search for employee: ${ee.firstName + ' ' + ee.lastName} - ${err.message}`))
                if(ctx) {
                    eeCtx[ee.uid] = ctx;
                    for(const cov of ctx.result.gold.data || []) allCms[cov.plan_id] = cov;
                    for(const cov of ctx.result.silver.data || []) allCms[cov.plan_id] = cov;
                    for(const cov of ctx.result.bronze.data || []) allCms[cov.plan_id] = cov;
                }
            }

            const add_static_plans:any = {};
            for(const k in allCoverages) {
                add_static_plans[k] = {...allCoverages[k], coverage: k};
            }
            for(const k in allCms) {
                add_static_plans[k] = {...allCms[k], plan_id: k};
            }
            for(const k in coverages || {}){
                add_static_plans[k] = {...coverages[k], compare_id: k};
            }

            const static_plans = await new CoreCall('static-plans', context).create(Object.values(add_static_plans))

            const spByCoverage: any = {};
            const spByPlanId: any = {};
            const spByCompareId: any = {};
            for(const cov of static_plans) {
                if(cov.coverages) spByCoverage[cov.coverage] = cov;
                else if(cov.plan_id) spByPlanId[cov.plan_id] = cov;
                else if(cov.compare_id) spByCompareId[cov.compare_id] = cov;
            };


            const oneSim = async (emp: any) => {
                const ee = { ...emp }
                if (!ee.zip) {
                    ee.simError = 'No zip code provided'
                    return ee;
                }
                let shopId = ee.sim;
                const newShop: any = {}
                if (ee.person) newShop.person = ee.person;
                if (planId) newShop.plan = planId;
                if (host) newShop.host = host;
                const aptc = ale === false ? true : ale ? false : employees.length <= 50;

                const household = householdByEe[ee.uid];

                let simError = undefined
                const pass_private_policies = eeCoverages[ee.uid];
                const pass_ctx = eeCtx[ee.uid];
                const sp:any = [];
                for(const cov of pass_private_policies) sp.push(spByCoverage[cov._id]);
                const { gold, silver, bronze } = pass_ctx?.result || {};
                for(const cov of gold?.data || []) sp.push(spByPlanId[cov.plan_id]);
                for(const cov of silver?.data || []) sp.push(spByPlanId[cov.plan_id]);
                for(const cov of bronze?.data || []) sp.push(spByPlanId[cov.plan_id]);
                for(const k in coverages || {}) sp.push(spByCompareId[k]);
                const usableCoverageIds = Object.keys(coverages).filter(a => !exclude.includes(a));
                const shop = await new CoreCall('shops', context).get(shopId || fakeId, {
                    query: {$limit: 150, household, place: household.place},
                    runJoin: {
                        cost_sim: {
                            static_plans: sp.filter(a => !!a),
                            pass_private_policies,
                            pass_ctx,
                            tax_rate: .05,
                            skip_aptc: !aptc,
                            compare_ids: usableCoverageIds,
                            coverages: usableCoverageIds.map(a => {
                                return {
                                    ...coverages[a],
                                    _id: a
                                }
                            }),
                            data: newShop,
                            stats: {
                                people: household.people.filter(a => a.relation !== 'self'),
                                age: ee.age,
                                gender: ee.gender || 'male',
                                place: household.place,
                                income: household.income || 1
                            },
                            household,
                            risk: ee.risk || 5
                        }
                    }
                })
                    .catch(err => {
                        simError = err.message;
                        console.log(`Error running sim for employee: ${ee.firstName + ' ' + ee.lastName} - ${err.message}`)
                    })
                if (shop?._id) {
                    ee.sim = shop._id;
                    shopObj[shop._id] = shop
                } else ee.simError = simError
                return ee;
            }

            const ees = await Promise.all(employees.map(ee => oneSim(ee)))

            const {ees:ees2, simStats, currentStats} = await totalSims({ees, coverages: ex.coverages, householdByEe, shopObj})(context)

            context.result = await new CoreCall('gps', context).patch(context.id as any, {
                employees: ees2,
                simStats,
                currentStats,
                lastSim: new Date()
            });

        }
        return context;
    }
}
