// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import type {Application, HookContext} from '../../declarations.js'
import {MarketplaceService, getOptions} from './marketplace.class.js'
import {marketplacePath, marketplaceMethods} from './marketplace.shared.js'

export * from './marketplace.class.js'

import {cmsMarketplaceSearch, nonMarketplaceStates, marketplaceDefMap, cmsMarketplaceGet} from './cms/index.js';
import {getAptc, getSlcsp} from './utils/hooks.js';
import {stateExchangeSearch} from './state-exchange/index.js';
import {find_ptc_with_income_and_slcsp} from './utils/aca.js';
// import {ideonPolicyGet} from 'src/services/marketplace/ideon/index.ts';
// import {ideonMarketplaceSearch} from './ideon/index.js';


export const searchPlans = async (context: HookContext): Promise<HookContext> => {

    if (!context.params.runJoin?.find_by_id) {
        const {place} = context.params.query || {};

        if (nonMarketplaceStates.includes(place.state))
            return await stateExchangeSearch(context);

        return await cmsMarketplaceSearch(context)
    }
    return context;
}

const justPtc = async (context: HookContext): Promise<HookContext> => {
    const {ptc_est} = context.params.runJoin || {}
    if (ptc_est) {
        const {household, place} = ptc_est;
        context.result = {_id: new Date().getTime().toString(), ptc: await getAptc({household, place})(context)}
    }
    return context;
}

const slcspGet = async (context: HookContext): Promise<HookContext> => {
    const {get_slcsp} = context.params.runJoin || {};
    if(get_slcsp) {
        const {household, place} = get_slcsp;
        context.result = await getSlcsp({ household, place })(context);
        const ptc = find_ptc_with_income_and_slcsp({
            income: household.income,
            members: household.people.length,
            slcsp: context.result?.premium * 12
        }).ptc
        context.result._fastjoin = { ptc }
    }
    return context;
}

const getPlan = async (context: HookContext): Promise<HookContext> => {
    const {get_plan} = context.params.runJoin || {};
    if (get_plan) {
        const {id, household, place} = get_plan;
        // if(nonMarketplaceStates.includes(state)) {
        //     context.result = await ideonPolicyGet(id)(context);
        // }
        const plans: any = await cmsMarketplaceGet(id, household, place)(context);
        context.result = plans[0];
    }
    return context;
}
const findById = async (context: HookContext): Promise<HookContext> => {
    const {find_by_id} = context.params.runJoin || {};
    if (find_by_id) {
        const {state, id, household, place} = find_by_id;
        // if(nonMarketplaceStates.includes(state)) {
        //     context.result = await ideonPolicyGet(id)(context);
        // }
        const plans: any = await cmsMarketplaceGet(id, household, place)(context);
        context.result = {data: plans, total: plans?.length || 0};
    }
    return context;
}

import { ai_chat_get} from './ai/index.js';

// A configure function that registers the service and its hooks via `app.configure`
export const marketplace = (app: Application) => {
    // Register our service on the Feathers application
    app.use(marketplacePath, new MarketplaceService(getOptions(app)), {
        // A list of all methods this service exposes externally
        methods: marketplaceMethods,
        // You can add additional custom events to be sent to clients here
        events: []
    })
    // Initialize hooks
    app.service(marketplacePath).hooks({
        around: {
            all: []
        },
        before: {
            all: [],
            find: [findById],
            get: [justPtc, getPlan, slcspGet, ai_chat_get],
            create: [],
            patch: [],
            remove: []
        },
        after: {
            all: [],
            find: [searchPlans]
        },
        error: {
            all: []
        }
    })
}

// Add this service to the service type index
declare module '../../declarations.js' {
    interface ServiceTypes {
        [marketplacePath]: MarketplaceService
    }
}
