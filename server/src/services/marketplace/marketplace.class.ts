// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#custom-services
import type { Id, NullableId, Params, ServiceInterface } from '@feathersjs/feathers'

import type { Application } from '../../declarations.js'

type Marketplace = any
type MarketplaceData = any
type MarketplacePatch = any
type MarketplaceQuery = any

export type { Marketplace, MarketplaceData, MarketplacePatch, MarketplaceQuery }

export interface MarketplaceServiceOptions {
  app: Application
}

export interface MarketplaceParams extends Params<MarketplaceQuery> {}

// This is a skeleton for a custom service class. Remove or add the methods you need here
export class MarketplaceService<ServiceParams extends MarketplaceParams = MarketplaceParams>
  implements ServiceInterface<Marketplace, MarketplaceData, ServiceParams, MarketplacePatch>
{
  constructor(public options: MarketplaceServiceOptions) {}

  async find(_params?: ServiceParams): Promise<Marketplace[]> {
    return []
  }

  async get(id: Id, _params?: ServiceParams): Promise<Marketplace> {
    return {
      id: 0,
      text: `A new message with ID: ${id}!`
    }
  }

  async create(data: MarketplaceData, params?: ServiceParams): Promise<Marketplace>
  async create(data: MarketplaceData[], params?: ServiceParams): Promise<Marketplace[]>
  async create(
    data: MarketplaceData | MarketplaceData[],
    params?: ServiceParams
  ): Promise<Marketplace | Marketplace[]> {
    if (Array.isArray(data)) {
      return Promise.all(data.map((current) => this.create(current, params)))
    }

    return {
      id: 0,
      ...data
    }
  }

  // This method has to be added to the 'methods' option to make it available to clients
  async update(id: NullableId, data: MarketplaceData, _params?: ServiceParams): Promise<Marketplace> {
    return {
      id: 0,
      ...data
    }
  }

  async patch(id: NullableId, data: MarketplacePatch, _params?: ServiceParams): Promise<Marketplace> {
    return {
      id: 0,
      text: `Fallback for ${id}`,
      ...data
    }
  }

  async remove(id: NullableId, _params?: ServiceParams): Promise<Marketplace> {
    return {
      id: 0,
      text: 'removed'
    }
  }
}

export const getOptions = (app: Application) => {
  return { app }
}
