import type {HookContext} from '../../../declarations.js';
import {getCmsPerson, getCmsSlcsp, nonMarketplaceStates} from '../cms/index.js';
import {find_ptc_with_income_and_slcsp} from './aca.js';
import {stateExchangeSlcsp} from '../state-exchange/index.js';
// import {getIdeonPerson, ideonPolicySearch} from '../ideon/index.js';

export const getSlcsp = ({household, place, fuzzy, silver_plans}: any) => {
    return async (context: HookContext) => {
        let silvers: any = silver_plans || [];
        // const bronzes: any = [];
        if (!silvers?.length) {
            if (nonMarketplaceStates.includes(place.state)) {
                const silver = await stateExchangeSlcsp({household, place, business_year: context.params.query?.business_year})(context)
                silvers = [ silver, silver ]
            }
            household.people = household.people.map(a => getCmsPerson(a))
            const cms_slcsp = await getCmsSlcsp({household, place})(context)
                .catch(err => console.log(`Error getting slcsp: ${err.message}`))
            if (cms_slcsp?.data) {
                silvers.push({})
                silvers.push(cms_slcsp.data)
            }
            // } else {
            //     context.params.query.$limit = 50;
            //     household.people = household.people.map(a => getIdeonPerson(a))
            //     const plans2 = await ideonPolicySearch({household, place})(context)
            //         .catch(err => {
            //             console.log(`Error retrying for slcsp due to medicaid subsidy: ${err.message}`)
            //             return {data: {}}
            //         })
            //     const silvers: any = [];
            //     const sorted = (plans2.data.plans || []).sort((a, b) => a.premium - b.premium);
            //     for (let i = 0; i < sorted.length; i++) {
            //         const plan = sorted[i];
            //         if (plan.level.includes('bronze')) bronzes.push(plan);
            //         if (plan.level.includes('silver')) silvers.push(plan);
            //     }
            // }
        }
        const getPayload = (val: any) => {
            if (!val) return undefined;
            return {name: val.name, premium: val.premium, metal_level: val.metal_level}
        }
        if (silvers[1]) return getPayload(silvers[1]);
        else return undefined
        // else if (silvers[0] && fuzzy) return getPayload(silvers[0]);
        // return fuzzy ? getPayload(bronzes[bronzes.length - 1]) : undefined
    }
}
export const getAptc = ({household, place, household_size, silver_plans}: any) => {
    return async (context: HookContext) => {
        // let silver;
        let silver = (silver_plans || [])[1];
        let ptc = 0;
        if (!silver) {
            silver = await getSlcsp({household, place})(context)
                .catch(err => {
                    console.log(`Error getting slcsp: ${err.message}`)
                    return {};
                })
            // if (!nonMarketplaceStates.includes(place.state)) {
            //     household.people = household.people.map(a => getCmsPerson(a))
            //     const est = await getAptcEstimate({household, place})(context)
            //         .catch(err => {
            //             console.log('Error getting aptc estiamte for marketplace quote', err.message);
            //             return {data: {}}
            //         })
            //     if (est.data.estimates?.length) {
            //         ptc = est.data.estimates[0].aptc
            //     }
            // } else silver = await getSlcsp({household, place, fuzzy: true})(context)
        }
        if (silver) ptc = find_ptc_with_income_and_slcsp({
            income: household.income,
            members: household_size || household.people.length,
            slcsp: silver.premium * 12
        }).ptc
        return ptc
    }
}
