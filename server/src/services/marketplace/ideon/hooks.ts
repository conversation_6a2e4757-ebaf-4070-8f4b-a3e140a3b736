import {HookContext} from '../../../declarations.js';
import axios from 'axios';
import {getAptc} from '../utils/hooks.js';
import {find_slcsp_w_ptc_and_income} from '../utils/aca.js';
import {normalizeIdeonPolicy} from './index.js';


export const getAge = (dob:string) => {
    const today = new Date(); // Get today's date
    const birthDate = new Date(dob); // Convert input to a Date object

    let age = today.getFullYear() - birthDate.getFullYear(); // Calculate the year difference

    // Adjust age if the birthday hasn't occurred yet this year
    const hasHadBirthday =
        today.getMonth() > birthDate.getMonth() ||
        (today.getMonth() === birthDate.getMonth() && today.getDate() >= birthDate.getDate());

    if (!hasHadBirthday) {
        age--;
    }

    return age;
}

export const getIdeonPerson = (p:any) => {
    const age = p.age || getAge(p.dob)
    return {
        age,
        child: p.child || p.child === false ? false : age < 18,
        smoker: p.smoker || !!p.uses_tobacco
    }
}

export const ideonPolicyGet = (id:string, options:{throw?:boolean}) => {
    return async (context: HookContext) => {
        const key = '44263404d0b0be4fcc1314498f2502a0'
        const url = `https://api.ideonapi.com/plans/medical/${id}`
        const headers = {
            'Content-Type': 'application/json',
            'Vericred-Api-Key': key
        }
        const data = await axios.get(url, { headers })
            .catch(err => {
                console.log(`Error getting ideon policy`, err.message);
                if(options.throw) throw new Error(err.message);
                return { data: {} }
            })
        return normalizeIdeonPolicy(data.data?.policy)
    }

}

type Options = { household:any, place:any, noThrow?:boolean }
export const ideonPolicySearch = ({ household, place, noThrow }:Options) => {
    return async (context:HookContext) => {
        const {$skip, $limit} = context.params.query;
        const page = Math.max(1, Math.ceil($skip || 0 / ($limit || 1)));
        const key = '44263404d0b0be4fcc1314498f2502a0'
        const url = `https://api.ideonapi.com/plans/medical/search?page=${page}&per_page=${$limit}`

        const year = new Date().getFullYear();
        const month = new Date().getMonth();
        // const ee = enrollment.enrolled;

        const data = {
            fips_code: place.countyfips,
            zip_code: place.zipcode,
            market: 'individual',
            per_page: $limit || 150,
            enrollment_date: `${(month < 11 ? year : year + 1).toString().padStart(2, '0')}-${(month < 11 ? month + 2 : 1).toString().padStart(2, '0')}-01`,
            applicants: household.people.map(a => {
                return {
                    age: a.age || getAge(a.dob),
                    child: a.child || ['child', 'grandson', 'granddaughter', 'niece', 'nephew', 'stepson', 'stepdaughter'].includes(a.relation || 'none'),
                    smoker: a.smoker || a.monthsSinceSmoked ? a.monthsSinceSmoked < 12 : false
                }
            }),
            sort: "premium:asc",
            household_income: household.income,
            household_size: household.people.length
        }
        const headers = {
            'Content-Type': 'application/json',
            'Vericred-Api-Key': key
        }
        return await axios.post(url, data, { headers })
            .catch(err => {
                console.log(`Error with ideon call: ${err.message}`)
                if(!noThrow) throw new Error(`Error retrieving plans: ${err.message}`)
            }) as any

    }
}


export const ideonMarketplaceSearch = async (context:HookContext) => {
    const {household, household_size, drugs, providers, place} = context.params.query || {};
    household.people = household.people.map(a => getIdeonPerson(a))
    const data = await ideonPolicySearch({ household, place })(context)
    context.result = {
        data: (data.data?.plans || []).sort((a, b) => a.premium - b.premium),
        total: data.data.meta.total
    };
    const res = {
        bronze: {data: [], total: 0},
        silver: {data: [], total: 0},
        gold: {data: [], total: 0},
        platinum: {data: [], total: 0}
    }
    const metals = ['bronze', 'silver', 'gold', 'platinum']

    for (let i = 0; i < (context.result.data || []).length; i++) {
        const plan = context.result.data[i];
        if (plan) {
            for (let l = 0; l < metals.length; l++) {
                if (plan.level?.includes(metals[l])) {
                    res[metals[l]].data.push(normalizeIdeonPolicy(plan))
                    res[metals[l]].total++
                    break;
                }
            }
        }
    }
    const {premium_tax_credit, state_subsidy, eligible_for_chip_medicaid} = data.data.meta;
    let ptc;
    if (eligible_for_chip_medicaid) {
        const ptc = await getAptc({household, place, household_size, silver_plans: res.silver})(context)
    }
    const aptc = ptc || premium_tax_credit;
    const slcsp = find_slcsp_w_ptc_and_income({
        ptc: aptc,
        income: household.income,
        members: household_size || household.people.length
    })
    context.result = {...context.result, ...res, state_subsidy, aptc, ...slcsp}
    //TODO: reenable provider and drug filters - check ./cms/index file for old filters

    return context;
}
