import {HookContext} from '../../../declarations.js';
import { pdfToPng } from 'pdf-to-png-converter'

import OpenAi from 'openai';
import {CoreCall} from 'feathers-ucan';

const convertPdfToPng = async (pdfBuffer: ArrayBufferLike) => {
    return await pdfToPng(pdfBuffer, { viewportScale: 2 });
}

const fileToBuffer = async (file:any) => {
    if (file.buffer) {

        if (file.mimetype.includes('pdf')) {

            const pngPages = await convertPdfToPng(file.buffer)
                .catch(err => {
                    console.log(`Error parsing pdf to png`, err.message);
                });
            for (let idx = 0; idx < (pngPages || []).length; idx++) {
                if (pngPages[idx]) {
                    const nodeBuff = Buffer.from(pngPages[idx].content);
                    const base64 = nodeBuff.toString('base64');
                    return `data:image/png;base64,${base64}`
                }
            }

        } else {
            const nodeBuff = Buffer.from(file.buffer);
            const base64 = nodeBuff.toString('base64');
            return `data:${file.mimetype};base64,${base64}`;
        }
    }
}

export const autoBillErase = async (context: HookContext) => {
    const {bill_eraser} = context.params.runJoin || {}
    if (bill_eraser) {
        const {planId, ref} = bill_eraser;
        const {files = []} = context.params;
        let prices = {data: []};
        let analysis: any = {};
        let session: any;

        if (files.length) {

            const uploads: any = [];

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
               const buf = fileToBuffer(file);
               if(buf) uploads.push(buf)
            }

            const getPages = (url: string) => {
                return {
                    type: 'image_url',
                    image_url: {url}
                }
            }

            // const {key} = context.app.get('x') as any;
            const {key, org} = context.app.get('openai');
            const openai = new OpenAi({apiKey: key, organization: org})
            // const openai = new OpenAi({
            //     baseURL: "https://api.x.ai/v1",
            //     apiKey: key
            // })
            const result = await openai.chat.completions.create({
                // model: 'grok-2-latest',
                model: 'gpt-4o',
                messages: [
                    {
                        role: 'system',
                        content: 'You are an assistant helping to take images of medical bills and parse them so they are valid JSON data'
                    },
                    {
                        role: 'user',
                        content: [
                            {
                                type: 'text',
                                text: 'Please analyze the bill from these images and compile the information into a single JSON object with the following structure: {"provider": The name of the medical provider if available across all pages (such as a hospital or doctor\'s office), otherwise "n/a",  "zip_code": The provider zip code if available, otherwise "n/a", "state": The 2-digit state code for the provider if available, otherwise "n/a", "codes": [ {"procedureCode": CPT code from the bill (if there is one),"billing_code": Other billing code listed, like rev code, "description": line description,  "price": Unit cost of each line item, "qty": Quantity of the item, default to 1 if not specified,  "total": Total cost for this line item } // Repeat for each line item across all pages except for summary lines or payment lines showing payments made], "insurance": If an insurance company is being billed, the name of the company, if self-pay - "self-pay", otherwise just "n/a", "patient_responsibility": Amount labeled as you pay or patient share if specified, otherwise "n/a",  "bill_total": Total amount of all line items.} Be sure to skip "total" and "payment" lines that summarize charges or show past payments. We only want to show actual procedure charges. For each URL, extract the relevant data ensuring all line items from each page are included in the "codes" array, maintaining their original order as they appear in the bill. If data is missing or ambiguous, use "n/a" for those fields.Please ensure that the data extracted is directly from the documents at these URLs, without any fabrication or inference. Return the object as a JSON object. No commentary and no markdown - just JSON'
                            },
                            ...uploads.map(a => getPages(a))
                        ]
                    }],
            })
                .catch(err => {
                    console.log(`Error searching ai procedures: ${err.message}`)
                    throw new Error(`Error processing image: ${err.message}`)
                })

            // console.log('result', result);
            analysis = result ? JSON.parse((result.choices || [])[0]?.message?.content?.replace('```json', '').replace('```', '')?.trim().replace(/[']/g, '') || "{}") : {};
            const codes: any = [];
            const getNumber = (val: string | number) => {
                if (val === 'n/a') return 0;
                if (typeof val === 'string') return Number(val.replace(/[^\d.]/g, ''))
                else return val || 0;
            }
            const lines = analysis.codes || [];
            const {provider, zip_code, state, carrier} = analysis;

            const notes = `provider:${provider}|state:${state}|zip:${zip_code}`
            const nona = (val: any) => {
                if (val && val !== 'n/a') return val
                else return undefined
            }
            const sesh = {
                person: context.params.login?.owner,
                plan: planId || undefined,
                ref: ref || undefined,
                carrier: carrier || undefined,
                state: nona(state),
                zip: nona(zip_code),
                providerName: nona(provider)
            }
            for (const k in sesh) {
                if (!sesh[k]) delete sesh[k]
            }
            session = await new CoreCall('bill-erasers', context).create(sesh)
                .catch(err => {
                    console.log(`Error adding new bill-eraser after reading bill: ${err.message}`)
                    throw new Error(`Error adding bill eraser session: ${err.message}`)
                })
            for (let i = 0; i < lines.length; i++) {
                const {price, qty, total, procedureCode, billing_code, description, rxcui} = lines[i] || {}
                if (procedureCode || billing_code) {
                    analysis.codes[i].code = procedureCode

                    const adjTotal = Math.max(getNumber(price), getNumber(total)) * 100;
                    const unitPrice = adjTotal / getNumber(qty || 1);
                    lines[i].price = unitPrice;
                    lines[i].total = adjTotal;
                    if (unitPrice) {
                        const pCode = procedureCode === 'n/a' ? undefined : procedureCode;
                        const newPrice: any = {
                            price: unitPrice,
                            eraser: session._id,
                            source: 'vision',
                            description,
                            carrier,
                            rxcui,
                            billing_code,
                            code: pCode,
                            notes: `${notes || ''}|qty:${qty}`
                        }
                        if (pCode) {
                            codes.push(procedureCode);
                        }
                        if (!billing_code || billing_code === 'n/a') newPrice.billing_code = pCode
                        await new CoreCall('prices', context).create(newPrice, {admin_pass: true, add_price: true})
                            .catch(err => {
                                console.log(`Could not create price: ${err.message}`)
                            })
                    }
                }
            }
            if (codes.length) {
                prices = await new CoreCall('prices', context).find({
                    code: {$in: codes},
                    runJoin: {price_check: {codes}}
                })
                    .catch(err => {
                        console.log(`Error getting price check: ${err.message}`)
                    })
            }
            context.result = {_id: new Date().getTime().toString(), session, prices, analysis}
            return context;
        }
        context.result = {_id: new Date().getTime().toString(), session, prices, analysis}
        return context;
    }
    return context;
}
