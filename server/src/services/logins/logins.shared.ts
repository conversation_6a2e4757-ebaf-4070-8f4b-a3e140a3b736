// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type { Logins, LoginsData, LoginsPatch, LoginsQuery, LoginsService } from './logins.class.js'

export type { Logins, LoginsData, LoginsPatch, LoginsQuery }

export type LoginsClientService = Pick<LoginsService<Params<LoginsQuery>>, (typeof loginsMethods)[number]>

export const loginsPath = 'logins'

export const loginsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const loginsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(loginsPath, connection.service(loginsPath), {
    methods: loginsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [loginsPath]: LoginsClientService
  }
}
