// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  Bundles,
  BundlesData,
  BundlesPatch,
  BundlesQuery,
  BundlesService
} from './bundles.class.js'

export type { Bundles, BundlesData, BundlesPatch, BundlesQuery }

export type BundlesClientService = Pick<
  BundlesService<Params<BundlesQuery>>,
  (typeof bundlesMethods)[number]
>

export const bundlesPath = 'bundles'

export const bundlesMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const bundlesClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(bundlesPath, connection.service(bundlesPath), {
    methods: bundlesMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [bundlesPath]: BundlesClientService
  }
}
