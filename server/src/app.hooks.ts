// Application hooks that run for every service
import {HookContext} from "./declarations.js";

import {softDelete, paramsFromClient, iff} from 'feathers-hooks-common';
import {removeFastjoin} from './utils/remove-fastjoin.js';
import {setUrlConfigParams} from './utils/core/domain-parse.js';
import {idConvert} from './utils/index.js';
import {allUcanAuth, anyAuth, noThrow, UcanAllArgs} from 'feathers-ucan';
import { oAuthStrategies } from './authentication/index.js';

const defRestAuthConfig:UcanAllArgs = {
    get: anyAuth,
    find: anyAuth,
    patch: anyAuth,
    update: anyAuth,
    create: anyAuth,
    remove: anyAuth
}
/** you only need to add in auth hooks that aren't there in the main authentication config for the service */
const serviceRestAuth = {
    'bill-eraser': () => {
        return {find: anyAuth }
    },
    'gps': () => {
        return { find: anyAuth, get: anyAuth }
    },
    'uploads': (context:HookContext) => {
        if(['parse', 'print'].includes(context.params.query?.storage)) return { find: anyAuth, get: anyAuth }
        else if(context.params.runJoin?.add_files) return { find: anyAuth, get: anyAuth, create: noThrow, patch: noThrow }
        else return defRestAuthConfig;
    },
    'plan-docs': () => {
        return { find: anyAuth, get:anyAuth }
    }
}
const alwaysAuth = async (context: HookContext) => {
    if (context.params.provider !== 'socketio' && !context.path.includes('oauth/:provider') && context.path !== 'authentication') {
        if(context.params.query?.core) {
            context.params.core = context.params.query.core || {}
            delete context.params.query.core;
        }
        if(context.params.query?.runJoin) {
            context.params.runJoin = context.params.query.runJoin || {}
            delete context.params.query.runJoin
        }

        let config = defRestAuthConfig;
        if(serviceRestAuth[context.path]) config = serviceRestAuth[context.path](context);
        if((context.params.authStrategies || []).some(a => oAuthStrategies.includes(a))){
            delete config.get;
            delete config.find;
            delete config.create;
            delete config.patch;
        }
        return await allUcanAuth<HookContext>(config)(context)
    }
    return context;
}

const addHost = (context: HookContext) => {
    const { host, ref } = context.params.core || {};
    if(host && !context.data.host) context.data.host = host
    if(ref && !context.data.ref) context.data.ref = ref
    return context;
}
export default {
    before: {
        all: [
            alwaysAuth,
            paramsFromClient(
                'disableSoftDelete',
                '$globalAggregate',
                'paginate',
                'relate_hook',
                'core',
                'banking',
                'runJoin',
                'hackId',
                'encrypt',
                'loginOptions',
                'voting',
                'loginId',
                'objectIdPaths',
                'crypto',
                'addMembers',
                '_attribute',
                'special_change',
                '_search',
                '_geo',
                'adj',
                'caps'
            ),
            iff(
                (context: HookContext) => ![
                    'authentication',
                    'sms',
                    'mailer',
                    'authManagement',
                    'geocode',
                    'places',
                    'places-auto-complete',
                    'file-uploader',
                    'uploads',
                    'accounts',
                    'rates'
                    // ...Object.values(context.app.get('uploads').enums.UPLOAD_SERVICES),
                ].includes(context.path),
                setUrlConfigParams(),
                softDelete({
                    // eslint-disable-next-line no-unused-vars
                    deletedQuery: async (context: HookContext) => {
                        return {deleted: {$ne: true}};
                    },
                    // eslint-disable-next-line no-unused-vars
                    removeData: async (context: HookContext) => {
                        return {deleted: true, deletedAt: new Date()};
                    },
                }),
            ),
            removeFastjoin(),
            (context: HookContext) => {
                if (context.params?.core?.login) context.params.login = context.params.core.login;
                return context;
            }
        ],
        find: [idConvert('_id')],
        create: [addHost],
        update: [],
        patch: [addHost],
        remove: []
    },
    error: {
        all: [
            ctx => {
                console.log(`Uncaught error on service: ${ctx.path} for method ${ctx.method} at stage ${ctx.type}: ${ctx.error?.message}`)
            }
        ],
        find: [],
        get: [],
        create: [],
        update: [],
        patch: [],
        remove: [],
    }
};
