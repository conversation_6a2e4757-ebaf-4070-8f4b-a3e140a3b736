// For more information about this file see https://dove.feathersjs.com/guides/cli/application.html
import { feathers } from '@feathersjs/feathers'
import { _get } from './utils/index.js';
import express, {
    rest,
    json,
    urlencoded,
    cors,
    serveStatic,
    notFound,
    errorHandler
} from '@feathersjs/express'
import configuration from '@feathersjs/configuration'
import socketio from '@feathersjs/socketio'

import type { Application } from './declarations.js'
import { configurationValidator } from './configuration.js'
import { logger } from './logger.js'
import { logError } from './hooks/log-error.js'
import { mongodb } from './mongodb.js'
import { services } from './services/index.js'
import { channels } from './channels.js';
import authentication from './authentication/index.js';
import appHooks from './app.hooks.js';

const app: Application = express(feathers())

// Load app configuration
app.configure(configuration(configurationValidator))
app.use(cors())
app.use(json({
    verify: (req:any, res, buf) => {
        const rawEndpoints: string[] = ["/banking"];

        if (req.url && rawEndpoints.includes(req.url)) {
            req.rawBody = buf;
        }
    }
}))
app.use(urlencoded({ extended: true }))
// Host the public folder
app.use('/', serveStatic(app.get('public')))

// Configure services and real-time functionality
app.configure(rest())
app.configure(
    socketio({
        cors: {
            origin: app.get('origins'),
        }
    }, (io) => {
        io.use(function (socket, next) {
            const ip = _get(socket, 'handshake.headers.x-forwarded-for', _get(socket, ['request', 'connection', 'remoteAddress']));
            (socket as { [key: string]: any }).feathers.ip = ip;
            (socket as { [key: string]: any }).feathers.headers.ip = ip;
            next()
        })
    })
);
app.configure(mongodb)
app.configure(services)
app.configure(authentication)
app.configure(channels)

// Configure a middleware for 404s and the error handler
app.use(notFound())
app.use(errorHandler({ logger }))

// Register hooks that run on all service methods
app.hooks({
        ...{
            around: {
                all: [],
            }
        },
        ...appHooks
    }
);
// Register application setup and teardown hooks here
app.hooks({
    setup: [],
    teardown: [],
});

export {app}
