import {computed, ComputedRef, Ref} from 'vue';
import {AnyObj} from 'src/utils/types';
import {Org} from 'components/orgs/utils/control-groups.js';
import {useRoute} from 'vue-router';

type OrgObj = Org & AnyObj
export const providerNav = (org: Ref<OrgObj> | ComputedRef<OrgObj>) => {
    const route = useRoute()

    const links = computed(() => {
            const rMeta = route.meta || {}

            return [
                {
                    icon: 'mdi-heart',
                    label: 'Care',
                    on: rMeta.category === 'care',
                    link: {route: {name: 'provider-care'}},
                    items: {
                        'Care': {
                            'Events': {
                                label: 'Events',
                                link: {
                                    route: {name: 'provider-care'}
                                },
                                on: rMeta.category === 'care' && rMeta.name === 'events'
                            },
                            'Visits': {
                                label: 'Visits',
                                link: {route: {name: 'provider-visits'}},
                                on: rMeta.name === 'visits'
                            },
                            'Claims': {
                                label: 'Claims',
                                link: {route: {name: 'claims-page'}},
                                on: rMeta.category === 'care' && rMeta.name === 'claims',
                                subs: [
                                    {
                                        label: 'Claims',
                                        on: rMeta.category === 'care' && rMeta.sub === 'claims',
                                        link: {
                                            route: {name: 'claims-page'}
                                        }
                                    },
                                    {
                                        label: 'Payments',
                                        on: rMeta.category === 'care' && rMeta.sub === 'payments',
                                        link: {route: {name: 'claim-payments'}}
                                    }
                                ]
                            }
                        }
                    }
                },
                {
                    icon: 'mdi-domain',
                    label: 'Account',
                    on: rMeta.category === 'provider',
                    link: {route: {name: 'provider-profile'}},
                    items: {
                        'Display': {
                            'Profile': {
                                on: rMeta.name === 'profile',
                                link: {route: {name: 'provider-profile'}}
                            },
                            'Videos': {
                                on: rMeta.name === 'video',
                                link: {route: {name: 'provider-video'}}
                            }
                        }
                    }
                },
                {
                    icon: 'mdi-cash',
                    label: 'Bundle',
                    on: rMeta.category === 'bundle',
                    link: {route: {name: 'provider-bundle'}},
                    items: {}
                },
                {
                    id: 'direct-care',
                    icon: 'mdi-stethoscope',
                    label: 'Direct Care',
                    on: rMeta.category === 'direct-care',
                    link: { route: { name: 'direct-care-home' }},
                    items: {
                        'Panels': {
                            'Panels': {
                                label: 'Panels',
                                link: {route: {name: 'dc-coverages'}},
                                on: rMeta.name === 'coverages'
                            }
                        }
                    }
                },
                {
                    icon: 'mdi-bank',
                    label: 'Payments',
                    on: rMeta.category === 'finance',
                    link: {route: {name: 'care-account-dash'}},
                    items: {
                        'Payments': {
                            'Care Account': {
                                on: rMeta.name === 'care-account',
                                link: {route: {name: 'care-account-dash'}},
                                subs: [
                                    {
                                        on: rMeta.sub === 'dash',
                                        link: {route: {name: 'care-account-dash'}},
                                        label: 'Account'
                                    },
                                    {
                                        on: rMeta.sub === 'settings',
                                        link: {route: {name: 'care-account-settings'}},
                                        label: 'Settings'
                                    }
                                ]
                            },
                            'Payments': {
                                on: rMeta.name === 'payments',
                                link: {route: {name: 'provider-payments'}},
                            }
                        }
                    }
                },
                {
                    icon: 'mdi-cog',
                    label: 'Settings',
                    on: rMeta.category === 'settings',
                    link: {
                        route: {name: 'org-settings'}
                    },
                    items: {
                        [org.value?.name || 'Company']: {
                            'Ownership': {
                                on: rMeta.name === 'org-ownership',
                                link: {route: {name: 'org-ownership'}}
                            },
                            'Control': {
                                on: rMeta.name === 'org-control',
                                link: {route: {name: 'org-control'}}
                            },
                            'Info': {
                                on: rMeta.name === 'org-info',
                                link: {route: {name: 'org-info'}}
                            },
                            'Settings': {
                                on: rMeta.name === 'finance-settings',
                                link: {route: {name: 'org-banking'}},
                                subs: [
                                    {
                                        label: 'Bank Accounts',
                                        link: {route: {name: 'org-banking'}},
                                        on: rMeta.sub === 'bank-accounts'
                                    },
                                    {
                                        label: 'Profile',
                                        link: {route: {name: 'banking-profile'}},
                                        on: rMeta.sub === 'banking-profile'
                                    }

                                ]
                            },
                        }
                    }
                }
            ]
        }
    )

    return {
        links
    }

}

