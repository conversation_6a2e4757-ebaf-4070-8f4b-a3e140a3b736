<template>
  <div :class="`__main_foot ${dark ? '__dark_footer' : ''} q-py-xl q-px-md`">
    <div class="row justify-center">
      <div class="_cent">

        <div class="row">
          <div class="col-12">
            <div class="row">
              <div
                  class="col-4 col-md-3 q-pa-xs cursor-pointer"
                  v-for="(link, i) in links.filter(a => !!a.items)" :key="`link-${i}`"
                  @click="$emit('link', link.link)"
              >
                <div :class="`text-p${dark ? '2' : '9'} font-1r text-weight-bolder q-pb-sm`">{{ link.label }}</div>
                <div v-for="(sub, idx) in getSubs(link)" :key="`sub-${i}-${idx}`"
                     @click.stop="$emit('link', sub.link)"
                >
                  <div class="font-1r q-py-xs">{{ sub.key }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-12 col-md-3 q-py-lg">
            <div class="row q-py-md">
              <q-img fit="contain" :src="icon" style="width: 50px; height: 50px; max-width: 100%"></q-img>
            </div>
            <div class="font-1-1-4r text-weight-bold">Superb care should be common</div>
            <div>&copy;CommonCare 2023</div>
          </div>
        </div>

        <policy-strip :dark="dark"></policy-strip>

        <div class="q-py-lg">
          <div class="font-7-8r text-italic">
            Nothing on this website is written as specific tax or legal advice. CommonCare is a technology platform and does not engage in the business of giving legal or tax advice unless under specific agreement to do so. CommonCare is not a healthcare provider, insurance company, or third party administrator. If you notice any issues with this site, HIPAA concerns, or other <NAME_EMAIL>.
          </div>
          <div class="q-pt-sm font-7-8r">
            Issues or complaints? <span class="tw-six cursor-pointer text-primary" @click="reportDialog = true">Report here.</span>
          </div>
        </div>
      </div>
    </div>

    <common-dialog v-model="reportDialog">
      <div class="q-pa-md bg-white">
        <report-issue @update:modelValue="reportDialog = false" type="complaint" service="issues"></report-issue>
      </div>
    </common-dialog>

  </div>
</template>

<script setup>
  import icon from 'src/assets/commoncare_icon.svg';
  import PolicyStrip from 'layouts/utils/PolicyStrip.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import ReportIssue from 'components/issues/forms/ReportIssue.vue';

  import {links} from './utils/main-nav';
  import {_flatten} from 'symbol-syntax-utils';
  import { ref } from 'vue';

  const props = defineProps({
    dark: Boolean
  })

  const reportDialog = ref(false);
  const getSubs = (link) => {
    return link.items ? _flatten(Object.keys(link.items).map(a => Object.keys(link.items[a]).map(b => {
      return {
        key: b,
        ...link.items[a][b]
      }
    }))) : [];
  };
</script>

<style lang="scss" scoped>
  .__main_foot {
    width: 100%;
    background: linear-gradient(192deg, var(--q-ir-grey-1), white);
    color: var(--q-p12);
  }

  .__col {
    width: 25%;
    min-width: 100px;
  }

  .__dark_footer {
    background: linear-gradient(192deg, var(--q-ir-grey-10), black);
    color: var(--q-ir-grey-2);
  }
</style>
