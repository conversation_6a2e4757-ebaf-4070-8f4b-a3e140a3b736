<template>
  <div class="__admin_foot q-py-xl q-px-md" :style="{ background: `var(--q-${bg || 'ir-grey-1'})`}">
    <div class="row justify-center">
      <div class="_cent">

        <div class="row">

          <div class="col-12 col-md-3 q-py-lg">
            <div class="row q-py-md">
              <q-img fit="contain" :src="iconIn || icon" style="width: 50px; height: 50px; max-width: 100%"></q-img>
            </div>
            <div :class="`font-1-1-4r text-weight-bold text-${text}`">Great care should be common</div>
            <div>&copy;CommonCare 2023</div>
          </div>
        </div>

        <policy-strip></policy-strip>

        <div class="q-py-lg">
          <div class="font-7-8r text-italic">
            Nothing on this website is written as specific tax or legal advice. CommonCare is a technology platform and does not engage in the business of giving legal or tax advice unless under specific agreement to do so. CommonCare is not a healthcare provider, insurance company, or third party administrator. If you notice any issues with this site, HIPAA concerns, or other <NAME_EMAIL>.
          </div>
          <div class="q-pt-sm font-7-8r">
            Issues or complaints? <span class="tw-six cursor-pointer text-primary" @click="reportDialog = true">Report here.</span>
          </div>
        </div>
      </div>
    </div>

    <common-dialog v-model="reportDialog">
      <div class="q-pa-md bg-white">
        <report-issue @update:modelValue="reportDialog = false" type="complaint" service="issues"></report-issue>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import ReportIssue from 'components/issues/forms/ReportIssue.vue';

  import icon from 'src/assets/commoncare_icon.svg';
  import { ref } from 'vue';
  import PolicyStrip from 'layouts/utils/PolicyStrip.vue';

  const props = defineProps({
    bg: String,
    text: { default: 'p10' },
    iconIn: { required: false }
  })

  const reportDialog = ref(false);

</script>

<style lang="scss" scoped>
  .__admin_foot {
    width: 100%;
    background: var(--q-ir-grey-1);
    color: black;
  }

  .__col {
    width: 25%;
    min-width: 100px;
  }
</style>
