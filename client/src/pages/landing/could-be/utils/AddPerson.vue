<template>
  <div class="_fw w300 mw100 row items-center justify-around">
    <gender-picker :size="size" :model-value="mv[1]" @update:model-value="setGender"></gender-picker>

    <div class="__age">
      <div class="font-3-4r text-ir-mid _l0-8">Age</div>
      <age-pick :start-age="0" :model-value="mv[0]" @update:model-value="setAge"></age-pick>
    </div>

    <div class="__age" v-if="!noRelation">
      <q-chip class="font-3-4r tw-six" :color="relation === 'spouse' ? 'p1' : 'transparent'" clickable @click="emit('update:relation', 'spouse')" dense square label="Spouse" :text-color="relation === 'spouse' ? 'primary' : 'ir-mid'"></q-chip>
      <div class="__mid"></div>
      <q-chip class="font-3-4r tw-six" :color="relation === 'dep' ? 'p1' : 'transparent'" :text-color="relation === 'dep' ? 'primary' : 'ir-mid'" clickable @click="emit('update:relation', 'dep')" dense square label="Child"></q-chip>
    </div>
  </div>
</template>

<script setup>

  import {computed} from 'vue';
  import GenderPicker from 'components/market/household/GenderPicker.vue';
  import AgePick from 'pages/landing/could-be/utils/AgePick.vue';

  const emit = defineEmits(['update:model-value', 'update:relation'])
  const props = defineProps({
    modelValue: { required: false },
    size: { default: '24px' },
    relation: { enum: ['spouse', 'dep']},
    noRelation: Boolean
  })
  const mv = computed(() => {
    if(Array.isArray(props.modelValue)) return [props.modelValue[0] || 40, props.modelValue[1] || 'male']
    return [40, 'female']
  })

  const setAge = (v) => {
    console.log('set age', v, mv.value)
    emit('update:model-value', [v, mv.value[1]])
  }

  const setGender = (v) => {
    emit('update:model-value', [mv.value[0], v])
  }
</script>

<style lang="scss" scoped>
  .__age {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .__mid {
    width: 30px;
    height: 1px;
    background: var(--ir-light);
    margin: 2px 0;
  }
</style>
