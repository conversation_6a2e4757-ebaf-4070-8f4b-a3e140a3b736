<template>

  <div class="row items-start">
    <div class="col-4 col-md-3">
      <div class="__block">
        <div class="__title">Age</div>
        <div>
          <age-pick :model-value="age" @update:model-value="setAge"/>
        </div>
      </div>
    </div>
    <div class="col-4 col-md-3">
      <div class="__block">
        <div class="__title">Gender</div>
        <div class="q-pt-xs">
          <gender-picker :size="size" :model-value="gender" @update:model-value="setGender"/>
        </div>
      </div>
    </div>
    <div class="col-4 col-md-3">

      <div class="__block">
        <div class="__title">Zip</div>
        <div>
          <zip-picker @zip-data="setZipData" dense square class="tw-six font-1-1-8r" color="transparent" :model-value="place?.zipcode || zip" @update:model-value="zip = $event"/>
        </div>
      </div>
    </div>
    <div class="col-12 col-md-3 __fam">

      <div class="__block">
        <div class="__title">Family</div>
        <div class="flex items-center">
          <family-member-icon class="_hov cursor-pointer" v-if="spouse" :def-size="size"
                              :model-value="{ age: spouse[0], gender: spouse[1] }">
            <q-menu>
              <div class="w200 q-pa-sm bg-white">
                <div class="row justify-end">
                  <remove-proxy size="sm" dense flat icon="mdi-close" remove-label="Remove Person" :label="undefined"
                                @remove="setSpouse(undefined)"></remove-proxy>
                </div>
                <add-person no-relation relation="spouse" :model-value="spouse"
                            @update:model-value="setSpouse"></add-person>
              </div>
            </q-menu>
          </family-member-icon>
          <family-member-icon class="_hov cursor-pointer" v-for="(dep, i) in deps || []" :key="`dep-${i}`"
                              :def-size="size" :model-value="{ age: dep[0], gender: dep[1] }">
            <q-menu>
              <div class="w200 q-pa-sm bg-white">
                <div class="row justify-end">
                  <remove-proxy size="sm" dense flat icon="mdi-close" remove-label="Remove Person" :label="undefined"
                                @remove="removeDep(i)"></remove-proxy>
                </div>
                <add-person no-relation relation="dep" :model-value="dep"
                            @update:model-value="editDep(i, $event)"></add-person>
              </div>
            </q-menu>
          </family-member-icon>
          <q-btn dense flat size="xs" color="ir-mid" icon="mdi-plus-thick">
            <q-menu @update:model-value="toggleAdding">
              <div class="w250 q-pa-sm bg-white">
                <add-person v-model="newPerson" @update:model-value="added = true" :relation="useRelation"
                            @update:relation="setAdding"></add-person>
              </div>
            </q-menu>
          </q-btn>
        </div>
      </div>
    </div>
  </div>


  <!--  <div class="__azf">-->
  <!--      <div class="__title">Age</div>-->
  <!--      <div class="__title">Gender</div>-->
  <!--      <div class="__title">Zip</div>-->
  <!--      <div class="__title">Family</div>-->

  <!--      <age-pick :model-value="age" @update:model-value="setAge"></age-pick>-->
  <!--      <gender-picker :size="size" :model-value="gender" @update:model-value="envStore.setGender"></gender-picker>-->
  <!--      <zip-picker @zip-data="setZipData" dense square :icon-right="undefined" class="tw-six font-1-1-8r" color="transparent" v-model="zip"></zip-picker>-->
  <!--      <div class="flex items-center">-->
  <!--        -->
  <!--      </div>-->

  <!--  </div>-->
</template>

<script setup>
  import ZipPicker from 'components/common/geo/pickers/ZipPicker.vue';
  import AgePick from 'pages/landing/could-be/utils/AgePick.vue';
  import GenderPicker from 'components/market/household/GenderPicker.vue';
  import AddPerson from 'pages/landing/could-be/utils/AddPerson.vue';
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';
  import FamilyMemberIcon from 'components/households/utils/FamilyMemberIcon.vue';

  import {computed, ref, watch} from 'vue';
  import {useEnvStore} from 'stores/env';
  import {sessionFamily} from 'components/households/utils/session-family';
  import {getStateCode} from 'src/components/common/geo/data/states';

  const envStore = useEnvStore();
  const emit = defineEmits(['zip-data', 'change', 'age'])
  const props = defineProps({
    size: { default: '26px' },
    place: Object
  })

  const { age, deps, spouse, address, gender } = sessionFamily(envStore)

  const zip = ref('27283')

  const setAge = (v) => {
    envStore.setAge(v);
    emit('change')
  }

  const postal = computed(() => address.value?.postal)
  watch(postal, (nv) => {
    if (nv) zip.value = nv
  }, { immediate: true })

  const newPerson = ref([40, 'female'])
  const adding = ref();
  const added = ref(false);
  const useRelation = computed(() => {
    if (adding.value) return adding.value;
    else if (spouse.value) return 'spouse'
    return 'dep'
  })
  const setAdding = (val) => {
    adding.value = val;
    added.value = true
  }

  const add = () => {
    if (adding.value === 'spouse') envStore.setSpouse(newPerson.value);
    else envStore.setDeps([...deps.value, newPerson.value])
    adding.value = undefined
    added.value = false;
    emit('change')
  }

  const setGender = (val) => {
    envStore.setGender(val);
    emit('change')
  }

  const toggleAdding = (val) => {
    if (!val && added.value) add()
  }

  const removeDep = (i) => {
    const arr = [...deps.value];
    arr.splice(i, 1);
    envStore.setDeps(arr);
    emit('change')

  }

  const editDep = (i, val) => {
    const arr = [...deps.value];
    arr.splice(i, 1, val);
    envStore.setDeps(arr);
    emit('change')

  }
  const setSpouse = (val) => {
    envStore.setSpouse(val);
    emit('change')

  }

  const setZipData = (val, passive) => {
    emit('zip-data', val, passive);
  }

  watch(() => props.place, (nv) => {
    if (nv?.zipcode) {
      zip.value = nv.zipcode;
    }
  }, { immediate: true })
  watch(address, (nv) => {
    if (!props.place?.countyfips && nv && nv.postal && nv.fips) {
      zip.value = nv.postal;
      emit('place', {
        zipcode: nv.postal,
        countyfips: nv.fips,
        state: getStateCode(nv.region)
      })
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

  //.__azf {
  //  display: grid;
  //  flex-di;
  //  gap: 12px;
  //  width: 100%;
  //}
  //
  //.__block {
  //  display: grid;
  //  grid-template-columns: 120px 1fr; // Label + component
  //  align-items: center;
  //  gap: 12px;
  //
  //  .__title {
  //    font-size: 0.8rem;
  //    font-weight: 600;
  //    color: var(--ir-mid);
  //  }
  //

  //}


  .__block {
    display: grid;
    grid-template-columns: 1fr;
    row-gap: 8px;
    width: 100%;
    grid-template-rows: auto auto;
    justify-content: center;
    align-items: start;
    justify-items: center;

    div {
      color: var(--q-primary);
      font-size: 1.12rem;
    }

    .__title {
      font-size: .8rem;
      font-weight: 600;
      color: var(--ir-mid);
      text-align: center;
    }
  }

  @media (max-width: 600px) {

    .__title {
      text-align: left;
    }
    .__fam {
      padding-top: 10px;
    }
  }


</style>
