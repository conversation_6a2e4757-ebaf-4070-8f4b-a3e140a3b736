<template>
  <div class="_fw row justify-center">
    <div class="_cent pd15 pw2">
      <div class="text-center text-lg tw-six">Oh, we integrate physician care into your group health plan too</div>

      <div class="row q-pt-xl relative-position">
        <div class="__blb"></div>
        <div class="col-12 col-md-4 q-pa-sm relative-position z5" v-for="(thing, i) in things" :key="`thing-${i}`">
          <div class="__c z5">
            <div class="text-xs tw-six">{{thing.label}}</div>
            <q-img v-if="thing.url" class="_fw h200" fit="cover" :src="thing.url"></q-img>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

  import {computed} from 'vue';

  const things = computed(() => {
    return [
      {
        label: 'Plan Docs',
        url: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fdocs.gif?alt=media&token=3fb04265-66b4-49b9-94b7-b07630a0c481'
      },
      {
        label: 'ACA Compliance',
        url: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fcompliance.gif?alt=media&token=0ae10a32-bc10-4360-b055-2079011f2640'
      },
      {
        label: 'Sleek Enrollment',
        url: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fenroll.gif?alt=media&token=71a18abc-c2ad-459c-8987-4faf2cba874f'
      },
      {
        label: 'Fund Management',
        url: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Ffunds.gif?alt=media&token=eda453e1-c36b-47ab-a37c-2495123f5ba9'
      },
      {
        label: 'Care Adjudication',
        url: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fadj.gif?alt=media&token=b1595da2-13bb-4ddb-9a10-e1d0076e9a78'
      },
      {
        label: 'Custom Networks',
        url: 'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fnetwork.gif?alt=media&token=5e3de4b6-7f91-4596-9467-c136e7fd9268'
      }
    ]
  })
</script>

<style lang="scss" scoped>

  .__c {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 2px 9px -3px #999;
    padding: 20px 20px;
    background: white;
    position: relative;
  }
  .__blb {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: radial-gradient(var(--q-a3) -50%, transparent 50%);
    z-index: 0;
    width: max(150vw, 1500px);
    height: max(150%, 100vw);
  }
</style>
