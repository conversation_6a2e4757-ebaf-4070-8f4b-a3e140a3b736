<template>
  <div style="padding-bottom: 80px" class="_fw row justify-center q-px-sm">
    <div class="_cent br30 relative-position _oh __team">
      <div class="__b"></div>
      <div class="__b1"></div>
      <div class="row z5">
        <div :class="`col-12 col-md-6 q-py-sm pw3`">
          <div class="text-xl tw-six">Do I <span class="tw-eight text-s4">need</span> a broker?</div>
          <div class="text-sm tw-five">You can use CommonCare to implement and manage a plan all on your own - but most businesses want the right help. We have certified partners in your market. <br><br>Trade in "broker" for a new team.</div>
        </div>
        <div class="col-12 col-md-6 z5">
          <div class="__c">
            <div class="tw-six text-md text-s4">Plan Doctor <span class="tw-six text-xs text-a1 q-ml-sm">Typical cost: $10-110 PEPM</span></div>
            <div class="text-xs tw-five">Your health plan starts with healthcare - and should be physician(s) lead, ranging from simple care navigation to providing 80%+ of your care directly.</div>
          </div>
          <div class="__c">
            <div class="tw-six text-md text-s4">Plan Advisor <span class="tw-six text-xs text-a1 q-ml-sm">Typical cost: $10 PEPM</span></div>
            <div class="text-xs tw-five">This is the soft touch for you and your employees. Ensuring your plan is setup correctly, reported on, and manages individual experience and ongoing support.</div>
          </div>
          <div class="__c">
            <div class="tw-six text-md text-s4">Plan Attorney <span class="tw-six text-xs text-a1 q-ml-sm">Typical cost: $1,000-2,000/year</span></div>
            <div class="text-xs tw-five">Compliance sign-off helps you sleep well - but way more interesting is having help when a 9-page hospital bill hits. You need some muscle in your corner.</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

</script>

<style lang="scss" scoped>

  .__team {
    background: linear-gradient(82deg, var(--q-s7), var(--q-accent));
    position: relative;
    color: white;
    box-shadow: 0 4px 14px -4px #999;
    padding: max(40px, 8vh) max(20px, 2vw);
  }
  .__win {
    color: var(--q-accent);
    background: -webkit-linear-gradient(180deg, var(--q-accent), var(--q-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .__c {
    padding: 10px 3vw;
  }
  .__b {
    position: absolute;
    top: -50%;
    height: 200%;
    width: 200%;
    left: 0%;
    background: radial-gradient(var(--q-a7) -50%, transparent 50%);
    z-index: 0;
  }
  .__b1 {
    position: absolute;
    top: -38%;
    left: -30%;
    height: 200%;
    width: 200%;
    background: radial-gradient(var(--q-s7) -50%, transparent 50%);
    z-index: 1;
  }
</style>
