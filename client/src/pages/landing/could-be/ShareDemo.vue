<template>
  <div class="_fw">
    <div class="q-pa-sm">
      <div class="q-pb-sm text-center text-xxs tw-six text-ir-mid">Medical Bills</div>
      <div class="__chart">
        <div v-for="(yr, i) in years.all" :key="`yr-${i}`"
             :style="`width: 3%; height: ${years.heights[i]}%; margin-right: 1%; background: var(--q-${getColor(i)})`">
          <div v-show="(i+1) % 5 === 0">{{ i + 1 }}</div>
          <q-tooltip class="tw-six text-xxs">{{ dollarString(yr.value, '$', 0) }}</q-tooltip>
        </div>
      </div>
    </div>
    <div class="q-pa-sm q-mt-sm">
      <div class="__t">Best Insurance</div>

      <div class="__opt">
        <div>
          <q-img v-if="silverLogo" class="h20 w20" :src="silverLogo.url"></q-img>
          <div>{{silver?.carrierName}}</div>
        </div>
        <div>{{silver?.name}}</div>
        <div class="alt-font">{{dollarString(silver?.premium, '$', 0)}}<span>/mo</span></div>
      </div>
      <div class="__t">Best Alternative</div>

      <div class="__opt">
        <div>
          <q-img v-if="altLogo" class="h20 w20" :src="altLogo.url"></q-img>
          <div>{{alt?.carrierName}}</div>
        </div>
        <div>{{alt?.name}}</div>
        <div class="alt-font">{{dollarString(alt?.premium, '$', 0)}}<span>/mo</span></div>
      </div>
    </div>
  </div>
</template>

<script setup>

  import {dollarString} from 'symbol-syntax-utils';
  import {onMounted, ref, watch} from 'vue';
  import {useUploads} from 'stores/uploads';

  const uploadStore = useUploads();

  const props = defineProps({
    alt: { required: true },
    silver: { required: true }
  })

  const bills = [2574,1430,1487,3167,1021,3150,1703,1410,738,1116,1742,2588,32841,0,1187,1237,455,1459,0,2121,683,1277,4311,9154,1984,3097,1766,0,631,482,2863,15155,8053,11554,2355,2602,9096,518,0,6648,1667,1560,1275,1175,462,1684,17917,1703,9926,1462].slice(0, 25)

  const years = ref({
    all: [],
    length: 0,
    heights: [],
    byIdx: {},
    total: 0,
  })
  const floor = (v) => {
    return Math.floor(v * 100)
  }

  const setYears = () => {
    const arr = [];
    let total = 0;
    for (let i = 0; i < 25; i++) {
      const v = bills[i] || 0
      total += v;
      arr.push({ value: v, idx: i })
    }
    const sorted = arr.slice(0).sort((a, b) => b.value - a.value)
    const byIdx = {}
    for (let i = 0; i < sorted.length; i++) byIdx[sorted[i].idx] = i;
    years.value = {
      total,
      byIdx,
      all: arr,
      length: arr.length,
      heights: arr.slice(0).map((a) => floor(a.value / sorted[0].value)),
      barWidth: (100 / arr.length) * .75 + '%',
      gapWidth: (100 / arr.length) * .25 + '%'
    }
  }
  const getColor = (idx) => {
    const pct = years.value.byIdx[idx] || 0;
    if (pct < 2.5) return 'secondary'
    if (pct < 12.5) return 'accent'
    return 'primary'
  }

  onMounted(() => setYears())


  const altLogo = ref('')
  watch(() => props.alt, async (nv) => {

    if(nv?.carrierLogo?.uploadId){
      const logo = uploadStore.getFromStore(nv.carrierLogo.uploadId)
      if(logo?.value?.url) altLogo.value = logo.value.url;
      else altLogo.value = await uploadStore.get(nv.carrierLogo.uploadId)
          .catch(err => {
            console.error(`Could not load alt logo: ${err.message}`)
            return ''
          })
    }
  }, { immediate: true })
  const silverLogo = ref('')
  watch(() => props.silver, async (nv) => {
    if(nv?.carrierLogo?.uploadId){
      const logo = uploadStore.getFromStore(nv.carrierLogo.uploadId)
      if(logo?.value) silverLogo.value = logo.value.url;
      else silverLogo.value = await uploadStore.get(nv.carrierLogo.uploadId)
          .catch(err => {
            console.error(`Could not load silver logo: ${err.message}`)
            return ''
          })
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>
  .__chart {
    width: 100%;
    height: 150px;
    max-height: 50vw;
    display: flex;
    align-items: flex-end;
    border-bottom: solid 2px var(--ir-mid);

    > div {
      background: blue;
      border-radius: 3px 3px 0 0;
      position: relative;

      > div {
        position: absolute;
        bottom: 0%;
        left: 50%;
        transform: translate(-50%, 100%);
        font-size: .7rem;
        color: var(--ir-mid);
        font-family: var(--alt-font);
      }
    }
  }

  .__t {
    font-size: 1rem;
    font-weight: 600;
    color: var(--ir-deep);
    padding: 5px;
  }
  .__opt {
    width: 100%;
    border-radius: 8px;
    box-shadow: 2px 2px 8px var(--ir-light);
    padding: 6px 10px;
    background: white;

    > div {

      &:first-child {
        display: flex;
        align-items: center;
        font-size: .9rem;
        font-weight: 600;
        color: var(--ir-text);

        > div {
          &:nth-child(2) {
            padding: 0 8px;
          }
        }
      }

      &:nth-child(2) {
        font-size: .8rem;
        font-weight: 600;
        color: var(--ir-deep);
      }
      &:nth-child(3) {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--q-primary);
        font-family: var(--alt-font);

        > span {
          font-size: .8rem;
          font-weight: 500;
          color: var(--ir-deep);
        }
      }
    }
  }

</style>
