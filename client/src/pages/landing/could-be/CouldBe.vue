<template>
  <div class="_fw text-p12">
    <div class="row justify-center">
      <div class="_cent __tp">
        <div class="text-center text-ir-text">
          <div class="text-center tw-five __sub">
            A new kind of care experience <span class="font-1r">(and cost)</span>
<!--            People <span class="font-1r">(your doctor)</span> care for people <span class="font-1r">(you)</span>-->
          </div>
          <div class="__title">Healthcare direct - no middle people</div>
        </div>
        <div class="row justify-center">
          <div class="w800 mw100 q-py-sm font-1-1-2r text-center text-ir-deep">Working directly with doctors will
            drastically improve your healthcare cost and experience - insurance can move to "just in case"
          </div>
        </div>

      </div>
    </div>

    <old-new v-model:silver="silver" v-model:alt="alt"></old-new>

    <why-are-we></why-are-we>

    <div class="row justify-center pd10 pw2">
      <div class="_cent">
        <auto-insurance></auto-insurance>
      </div>
    </div>

    <div class="row justify-center">
      <div class="_cent pd3 pw2">
        <div class="row __ww">
          <div class="col-12 col-md-6 q-pa-sm relative-position _fh">
            <div class="__wb"></div>

            <div class="row justify-center">
              <div class="__wc">
                <div class="tw-five text-sm">What you want from <span
                    class="tw-six text-sm text-secondary">insurance</span></div>
                <table class="__items text-xs">
                  <tbody>
                  <tr>
                    <td>
                      <q-icon name="mdi-triangle" class="__flip text-xs"></q-icon>
                    </td>
                    <td>Low premiums</td>
                  </tr>
                  <tr>
                    <td>
                      <q-icon name="mdi-umbrella" class="text-xs"></q-icon>
                    </td>
                    <td>Protection from enormous bills</td>
                  </tr>
                  <tr>
                    <td>
                      <q-icon name="mdi-check-circle" class="text-xs"></q-icon>
                    </td>
                    <td>Simple transparent options</td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6 q-pa-sm relative-position _fh">
            <div class="__wb __r"></div>

            <div class="row justify-center">
              <div class="__wc">
                <div class="tw-five text-sm">What you want from <span
                    class="tw-six text-sm text-primary">healthcare</span></div>
                <table class="__items text-xs">
                  <tbody>
                  <tr>
                    <td>
                      <q-icon name="mdi-stethoscope" class="text-xs"></q-icon>
                    </td>
                    <td>Access to great doctors</td>
                  </tr>
                  <tr>
                    <td>
                      <div class="flex items-center no-wrap">
                        <q-icon name="mdi-stethoscope" class="text-xs"></q-icon>
                        <div class="tw-seven alt-font">!</div>
                      </div>
                    </td>
                    <td>Immediate access to great doctors</td>
                  </tr>
                  <tr>
                    <td>
                      <div class="flex items-center no-wrap">
                        <q-icon name="mdi-pill" class="text-xs"></q-icon>
                      </div>

                    </td>
                    <td>Low cost labs & meds</td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row justify-center">
      <div class="_cent pd10 pw1 relative-position">
        <div class="text-xxl tw-five text-center _l1-3 alt-font __title">How to make it happen</div>
        <div class="row justify-center q-py-sm">
          <div class="w700 mw100 q-px-md">
            <div class="text-sm text-center">This is how you have a top-quality healthcare experience for the best cost.</div>
            <div class="text-xs q-pt-md tw-five text-ir-mid text-center">With approx. pricing for this household.
            </div>
            <div class="row justify-center">
              <div class="__azf">
                <age-zip-family></age-zip-family>
              </div>
            </div>

          </div>
        </div>

        <q-img class="__img" fit="contain" :src="icon"></q-img>

        <div class="h50"></div>
        <div class="row justify-center" v-for="(step, i) in steps" :key="`step-${i}`">
          <div v-if="$q.screen.gt.sm" class="col-5 q-pa-md q-py-lg">
            <div :class="`font-1-1-2r tw-five text-${step.color}`">{{ step.title }}&nbsp;&nbsp;<span
                class="font-1r tw-five text-grey-8 tw-five">{{ step.caption }}</span></div>
            <div class="font-1r">{{ step.text }}
            </div>
          </div>
          <div class="col-1 _fh">
            <div class="text-center _l1 __step">
              <div class="tw-six text-grey-7 text-xs alt-font">STEP</div>
              <div class="row justify-center">
                <span class="text-xxl tw-six text-grey-7 alt-font">0{{ i + 1 }}</span>
              </div>
            </div>
            <div v-if="i < 2" class="row justify-center _fh">
              <div class="__dots"></div>
            </div>
          </div>
          <div class="col-10 col-md-5 pw2 q-pt-lg relative-position">
            <div v-if="$q.screen.lt.md" class="q-pb-lg q-pl-md">
              <div :class="`font-1-1-2r tw-five text-${step.color}`">{{ step.title }}&nbsp;&nbsp;<span
                  class="font-1r tw-five text-grey-8">{{ step.caption }}</span></div>
              <div class="font-1r">{{ step.text }}
              </div>
            </div>
            <div class="relative-position _fw">
              <div class="text-xxs tw-six q-px-sm">Our Solution:</div>
              <div class="row">
                <div :class="`text-sm __sol __bg${i}`">{{ step.solution }} <span
                    class="alt-font text-xs">: {{ step.price }}</span></div>
              </div>
              <div :class="`__tb __tb${i}`">
                <component v-bind="step.attrs" :is="step.component"></component>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <div class="__green relative-position">
      <div class="row justify-center">
        <div class="_cent pd15 pw2">
          <div class="row justify-center">
            <div class="_xsent text-center">
              <div class="text-md tw-five q-pa-sm"><span>Let's Recap - </span>instead of using a <span
                  class="text-secondary tw-six">health insurance</span> carrier to manage your <span
                  class="tw-six text-primary">healthcare</span> - use a doctor instead. That's who actually provides
                healthcare. Insurance is for covering risks, keep it in the proper lane. <span
                    class="text-primary tw-six">That's much better.</span></div>
            </div>
          </div>
        </div>
      </div>

    </div>
    <make-it-easy></make-it-easy>
    <the-team></the-team>

  </div>
</template>

<script setup>
  import ShareDemo from './ShareDemo.vue';
  import DpcDemo from './DpcDemo.vue';
  import CostDemo from './CostDemo.vue';
  import MakeItEasy from './MakeItEasy.vue';
  import WhyAreWe from './WhyAreWe.vue';
  import TheTeam from './TheTeam.vue';
  import OldNew from 'pages/landing/could-be/OldNew.vue';
  import AgeZipFamily from 'pages/landing/could-be/utils/AgeZipFamily.vue';
  import AutoInsurance from 'pages/landing/could-be/AutoInsurance.vue';

  import icon from 'src/assets/commoncare_icon.svg'
  import {computed, ref} from 'vue';
  import {dollarString} from 'src/utils/global-methods';
  import {sessionFamily} from 'components/households/utils/session-family';
  import {useEnvStore} from 'stores/env';
  const envStore = useEnvStore();

  const alt = ref({})
  const silver = ref({})
  const { household } = sessionFamily(envStore)
  const mult = ref(12);

  const dpcCost = computed(() => {
    const costs = {
      1: 95,
      2: 150,
      3: 210,
      4: 275,
      5: 300,
      6: 300,
      7: 300,
      8: 300,
      9: 300,
      10: 300,
      11: 300,
      12: 300
    }
    return (costs[household.value.people.length] || 300) * mult.value
  })
  const steps = computed(() => [
    {
      title: 'Use a free doctor',
      caption: '(The right kind of free)',
      text: 'Doctors all have business models. You want your doctor\'s primary incentive to be your healing. Allowing insurance to dictate care is doomed to fail you. If a doctor can\'t discuss incentives openly, you have the wrong doctor.',
      solution: 'DIRECT CARE',
      price: `${dollarString((dpcCost.value || 1190)/12, '$', 0)}/mo`,
      component: DpcDemo,
      color: 'primary'
    },
    {
      title: 'Get the right coverage',
      price: dollarString(alt.value.premium, '$', 0) + '/mo',
      text: 'You don\'t want insurance to control your care - but you do want to be protected from big bills. There are so many options, contracts, networks, deductibles, copays, the list goes on. AI is built for this. We can stress-test millions of scenarios in seconds - and show you the outcomes. It\'s super-human decision making.',
      solution: 'AI MATCHED PLAN',
      color: 'secondary',
      component: ShareDemo,
      attrs: {
        silver: silver.value,
        alt: alt.value
      }
    },
    {
      title: 'Stay in control of your care',
      text: 'Price transparency is the key to improving your care. "Is it covered?" should not be the question that dictates your care. When you can see prices, you can make choices. When you get care, we can help you make sure the cost is fair - like half of what insurance networks pay.',
      price: '$0',
      solution: 'OPEN PRICING',
      color: 'accent',
      component: CostDemo
    }
  ])
</script>

<style lang="scss" scoped>

  $sidepd: max(2vw, 15px);

  .__azf {
    padding: 5px 8px;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 8px var(--ir-light);
    max-width: 550px;
    width: 100%;
    margin: 10px;
  }

  .__tp {
    padding: max(12vh, 60px) $sidepd 30px $sidepd
  }

  .__title {
    font-size: var(--text-xl);
    font-family: var(--alt-font);
    font-weight: 500;
    text-align: center;
    color: var(--q-primary);

  }

  .__sub {
    font-size: 1.75rem;
    color: var(--q-secondary);
    //background: -webkit-linear-gradient(0deg, var(--q-secondary) 40%, var(--q-primary) 70%);
    //-webkit-background-clip: text;
    //-webkit-text-fill-color: transparent;
  }

  .__wc {
    border-radius: 10px;
    position: relative;
    //padding: 15px 20px;
    margin: 5px 0;
    overflow: hidden;
  }

  @media screen and (max-width: 1024px) {
    .__wc {
      width: 100%;
    }
  }

  .__items {
    width: 100%;
    padding-top: 15px;

    tr {
      td {
        padding: 1px 10px;
      }

      td:last-child {
        width: 99%;
      }
    }
  }

  .__ww {
    color: black;
    font-weight: 500;
    padding: 8vh min(45px, 5vw);
    border-radius: 15px;
    background: linear-gradient(95deg, var(--q-p0), var(--q-p1));
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px -8px #999;
  }

  .__wb {
    position: absolute;
    top: 0;
    left: 0;
    background: radial-gradient(var(--q-p2) -50%, transparent 50%);
    transform: translate(-50%, -40%);
    height: 400%;
    width: 200%;
  }

  .__r {
    border-radius: 50%;
    top: 100%;
    left: 50%;
    transform: translate(-30%, -40%);
    background: linear-gradient(115deg, white, var(--q-p1), var(--q-p2), white);

  }

  .__step {
    padding: 10px 4px;
    background: white;
  }

  .__dots {
    margin: 15px 0;
    height: 95%;
    min-height: 200px;
    border-right: dotted 3px var(--q-ir-grey-7);
  }

  .__img {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 5px;
    width: 600px;
    max-width: 80%;
    opacity: .1;
  }

  .__sol {
    color: white;
    margin: 0 5px;
    padding: 5px 15px;
    font-weight: 600;
    border-radius: 4px;
    background-size: 400% 400%;
    animation: gradient 15s infinite;
  }

  .__bg0 {
    background-image: linear-gradient(-45deg, var(--q-p7), var(--q-primary), var(--q-primary), var(--q-p7))
  }

  .__bg1 {
    background-image: linear-gradient(-45deg, var(--q-s7), var(--q-secondary), var(--q-secondary), var(--q-s7))
  }

  .__bg2 {
    background-image: linear-gradient(-45deg, var(--q-a7), var(--q-accent), var(--q-accent), var(--q-a7))
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .__tb {
    margin: 20px 0;
    padding: 30px 20px;
    border-radius: 10px;
    background: rgba(255, 255, 255, .4);
  }

  .__tb0 {
    box-shadow: 10px -10px 40px -10px var(--q-p3);
    transition: all .3s;
    transform: none;
    border: solid .5px var(--q-primary);

    &:hover {
      background: white;
      box-shadow: 10px -10px 60px -10px var(--q-p3);
      transform: translate(0, 5px);
    }
  }

  .__tb1 {
    box-shadow: 10px -10px 40px -10px var(--q-s3);
    transition: all .3s;
    transform: none;
    border: solid .5px var(--q-secondary);

    &:hover {
      background: white;
      box-shadow: 10px -10px 60px -10px var(--q-s3);
      transform: translate(0, 5px);
    }
  }

  .__tb2 {
    box-shadow: 10px -10px 40px -10px var(--q-a3);
    transition: all .3s;
    transform: none;
    border: solid .5px var(--q-accent);

    &:hover {
      background: white;
      box-shadow: 10px -10px 60px -10px var(--q-a3);
      transform: translate(0, 5px);
    }
  }

  .__flip {
    transform: rotateX(180deg);
  }

  .__green {
    overflow: hidden;
    width: 100%;
    background: linear-gradient(115deg, white 10%, var(--q-p0), var(--q-p1), white 90%);
  }


</style>
