<template>
  <q-page>
    <div class="row justify-center __top">
      <div class="_cent pw2 relative-position">
        <div class="__review flex no-wrap items-center">
          <q-img class="h30 w30 q-mr-md" fit="contain" :src="stamp"></q-img>
          <div class="text-primary"> Plan Transparency</div>
        </div>
        <div class="row items-center">
          <div class="col-12 col-md-6 q-py-md pw2">

            <div class="row q-py-md">
              <q-chip v-if="plan._id" class="bg-a9 text-white">
                <q-avatar v-if="org.avatar" class-="q-mr-sm">
                  <img style="object-fit: cover" :src="org.avatar.url">
                </q-avatar>
                <span class="tw-six text-xs">{{plan.name}}</span>
              </q-chip>
            </div>
            <div class="text-xxl tw-six text-a0 _l1-2">Are my benefits better than <span class="text-primary alt-font">cash</span>?</div>
            <q-separator dark class="q-my-md"></q-separator>

            <div class="text-sm tw-five text-a0">Let's check how well your employer health plan spends your money - or should you rather have the cash?</div>
          </div>
          <div class="col-12 col-md-6 q-py-md pw2">
            <div class="__slide">
              <q-tab-panels class="_panel" v-model="q" animated>
                <q-tab-panel class="_panel" name="1">
                  <div class="__test">
                    <div>First decide:</div>
                    <div>Would you spend your wages on this if you were offered the cash?</div>
                    <div class="__answers">
                      <q-radio dark v-model="q1" val="Y" @update:model-value="val => val === 'Y' ? q = '2' : ''"
                               label="Yes"></q-radio>
                      <q-radio dark v-model="q1" val="N" label="No"></q-radio>
                      <div class="q-pt-sm q-px-sm">
                        <q-badge color="primary" v-if="q1 === 'Y'" class="cursor-pointer tw-six text-xs"
                                 @click="q = '2'">Go on to Question 2
                        </q-badge>
                        <q-badge class="tw-six text-xs" color="secondary" v-if="q1 === 'N'">Keep your money</q-badge>
                      </div>
                    </div>
                  </div>
                </q-tab-panel>
                <q-tab-panel class="_panel" name="2">
                  <div class="__test">
                    <div class="q-pt-sm">Now check:</div>
                    <div>Can you buy it for less by utilizing this group plan?</div>
                    <div class="__answers">
                      <q-radio dark v-model="q2" val="Y" label="Yes"></q-radio>
                      <q-radio dark v-model="q2" val="N" label="No"></q-radio>
                      <div class="q-pt-sm q-px-sm">
                        <q-badge class="tw-six text-xs" v-if="q2 === 'Y'" color="primary">You have a bargain</q-badge>
                        <q-badge class="tw-six text-xs" v-if="q2 === 'N'" color="secondary">Give me the cash</q-badge>
                      </div>
                    </div>

                  </div>
                </q-tab-panel>
              </q-tab-panels>
              <div v-if="q === '2'" class="t-l q-pa-sm">
                <q-btn dense flat size="sm" icon="mdi-chevron-left" @click="q = '1'"></q-btn>
              </div>
            </div>
          </div>
        </div>

        <div class="__tabs">
          <q-tabs align="left" no-caps :model-value="tab" @update:model-value="setTab">
            <q-tab v-for="(k, i) in Object.keys(pages)" :key="`tab-${i}`" :name="k">
              <span class="tw-six">{{ pages[k].label }}</span>
            </q-tab>
          </q-tabs>
        </div>

      </div>
    </div>

    <div class="row justify-center">
      <div class="_cent pw2">
        <component v-bind="pages[tab].attrs" :is="pages[tab].component"></component>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import ScoreCriteria from 'components/plans/scores/criteria/ScoreCriteria.vue';
  import PlanScore from 'components/plans/scores/summary/PlanScore.vue';
  import PlanCoverages from 'components/plans/scores/coverages/PlanCoverages.vue';

  import stamp from 'src/assets/commoncare_icon.svg'
  import {computed, onMounted, ref} from 'vue';
  import {useRoute, useRouter} from 'vue-router';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {useOrgs} from 'stores/orgs';

  const planStore = usePlans();
  const orgStore = useOrgs();

  const route = useRoute();
  const router = useRouter();

  const q = ref('1')
  const q1 = ref('')
  const q2 = ref('')

  const tab = ref('criteria')

  const { item:plan } = idGet({
    store: planStore,
    routeParamsPath: 'planId'
  })
  const { item:org } = idGet({
    store: orgStore,
    value: computed(() => plan.value.org)
  })

  const setTab = (v) => {
    tab.value = v;
    const { href } = router.resolve({ ...route, query: { ...route.query, tab: v } })
    window.history.pushState({}, '', href)
  }

  const pages = computed(() => {
    return {
      'criteria': {
        label: 'Criteria',
        component: ScoreCriteria
      },
      'plan': {
        label: 'Your Plan',
        component: PlanScore,
        attrs: { plaN: plan.value }
      },
      'coverage': {
        label: 'Coverage',
        component: PlanCoverages,
        attrs: { plaN: plan.value }
      },
    }
  })

  onMounted(() => {
    const { tab: t } = route.query;
    if (t && pages.value[t]) tab.value = t
  })

</script>

<style lang="scss" scoped>

  .__top {
    background: linear-gradient(180deg, black, var(--q-a12));
    color: white;

    > div {
      &:first-child {
        padding: min(18vh, 18vw) 2vw;
        padding-bottom: max(min(18vh, 18vw), 80px);
      }
    }

    .__review {
      position: absolute;
      top: min(8vh, 8vw);
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: var(--text-sm);
      font-weight: 600;
    }
  }

  .__two {
    background: linear-gradient(180deg, var(--q-a12), var(--q-a10));
    padding-bottom: 50px;
    padding-top: 20px;
  }



  .__tabs {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    background: linear-gradient(90deg, var(--q-a10), transparent);
    margin: 0 2vw;


  }

  .__slide {
    margin: 20px 0;
    padding: 30px;
    border-radius: 12px;
    background: linear-gradient(180deg, var(--q-a12), var(--q-a10));
    box-shadow: 0 0 54px var(--q-a9);
    border: solid 1px var(--q-primary);
    position: relative;

    .__test {
      font-size: var(--text-sm);
      //border: solid 2px var(--q-a3);

      > div {
        &:first-child {
          font-weight: 600;
          font-size: var(--text-xs);
          color: var(--q-p5);
        }

        &:nth-child(2) {
          color: var(--q-a0);
          font-size: var(--text-sm);
          font-weight: 600;
        }

      }

      .__answers {
        font-family: var(--alt-font);
        padding-top: 15px;

        .__yes {
          color: var(--q-primary);
          font-weight: 600;
        }

        .__no {
          color: var(--q-secondary);
          font-weight: 600;
        }
      }
    }
  }


</style>
