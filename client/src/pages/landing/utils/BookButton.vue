<template>
  <q-btn v-bind="{...$attrs}" @click="on = !on">
    <slot name="default">

    </slot>

    <common-dialog v-model="on" setting="smmd">
      <div class="_fw q-pa-md __dl">
        <div class="row q-mb-sm">
          <q-tabs no-caps align="left" v-model="tab" indicator-color="primary">
<!--            <q-tab name="meet">-->
<!--              <span class="tw-six">Meet</span>-->
<!--            </q-tab>-->
            <q-tab name="wecall">
              <span class="tw-six">We'll Call You</span>
            </q-tab>
            <q-tab name="callus">
              <span class="tw-six">No, You Call Us</span>
            </q-tab>

          </q-tabs>
        </div>
        <q-tab-panels class="_panel" v-model="tab" animated>
          <q-tab-panel class="_panel" name="meet">
            <div class="q-pa-sm">
              <div class="text-md tw-six text-primary alt-font">Can't wait to meet you</div>
              <div class="tw-five text-xs"><i>(in an un-creepy tone)</i></div>
            </div>
            <hubspot-calendar></hubspot-calendar>

          </q-tab-panel>
          <q-tab-panel class="_panel" name="callus">

            <div class="q-pa-sm">
              <div class="text-md tw-six text-primary alt-font">Reach out, let's talk</div>
            </div>
            <q-list separator>
              <q-item clickable @click="openEmail">
                <q-item-section avatar>
                  <q-icon name="mdi-email" color="accent"></q-icon>
                </q-item-section>
                <q-item-section>
                  <q-item-label class="font-1r tw-six">{{ email }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item clickable @click="openTel">
                <q-item-section avatar>
                  <q-icon name="mdi-phone" color="primary"></q-icon>
                </q-item-section>
                <q-item-section>
                  <q-item-label class="font-1r tw-six">{{ national }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item clickable @click="openSms">
                <q-item-section avatar>
                  <q-icon name="mdi-message" color="secondary"></q-icon>
                </q-item-section>
                <q-item-section>
                  <q-item-label class="font-1r tw-six">{{ national }}</q-item-label>
                </q-item-section>
              </q-item>


            </q-list>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="wecall">
<!--            <div class="q-pa-sm">-->
<!--              <div class="text-md tw-six text-primary alt-font">We'll reach out asap</div>-->
<!--            </div>-->
            <div class="_form_grid">
              <div class="_form_label">Your Name</div>
              <div class="q-pa-sm">
                <q-input filled dense v-model="form.name" placeholder="Donald K. Trump"></q-input>
              </div>

              <div class="_form_label">Company</div>
              <div class="q-pa-sm">
                <q-input filled dense v-model="form.company" placeholder="The Mar-a-Lago Club"></q-input>

              </div>

              <div class="_form_label">Email</div>
              <div class="q-pa-sm">
                <email-field filled dense hide-bottom-space v-model="form.email"></email-field>

              </div>

              <div class="_form_label">Phone</div>
              <div class="q-pa-sm">
                <phone-input :input-attrs="{ dense: true, filled: true }" v-model="form.phone"></phone-input>

              </div>

              <div class="_form_label">Note</div>
              <div class="q-pa-sm">
                <q-input dense filled v-model="form.message" placeholder="Any last words..." autogrow></q-input>
              </div>
            </div>
            <div class="q-pt-md row justify-end">
              <q-btn :disable="!form.email" class="_l_btn tw-six" push no-caps @click="save" label="Send"
                     icon-right="mdi-send"></q-btn>
            </div>
          </q-tab-panel>

        </q-tab-panels>
      </div>
    </common-dialog>
  </q-btn>
</template>

<script setup>

  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import HubspotCalendar from 'components/utils/appointments/HubspotCalendar.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import PhoneInput from 'components/common/phone/PhoneInput.vue';

  import {ref} from 'vue';
  import {HForm} from 'src/utils/hForm';
  import {useReqs} from 'stores/reqs';

  const emit = defineEmits(['saved'])

  const reqStore = useReqs();

  const on = ref(false);
  const tab = ref('wecall');

  const number = '+19842380013';
  const national = '(*************';
  const email = '<EMAIL>';
  const openSms = () => {
    window.open(`sms:${number}`)
  }
  const openTel = () => {
    window.open(`tel:${number}`);
  }
  const openEmail = () => {
    window.open(`mailto:${email}`);
  }

  const { form, save } = HForm({
    store: reqStore,
    vOpts: ref({ email: { name: 'Email', v: ['email'] } }),
    validate: true,
    afterFn: (val) => {
      form.value = formFn()
      emit('saved', val)
      on.value = false;
    }
  })
</script>

<style lang="scss" scoped>
  .__dl {
    background: linear-gradient(180deg, var(--q-ir-grey-2), white);
  }
</style>
