<template>
  <q-page class="_fw">
    <div class="row justify-center">
      <div class="_cent pd15 pw2">
        <div class="row justify-center">
          <div class="_sent pw2">
            <!--            <div class="text-sm tw-six text-center">With rising healthcare costs creating pressure</div>-->
            <div class="text-center text-sm tw-six text-accent">With group health plan costs rising</div>
            <div class="text-xxl tw-six text-center alt-font _l1-2">Every plan needs <span class="text-accent tw-eight"><br
                v-if="$q.screen.lt.sm">Bill <PERSON></span></div>
            <div class="text-md text-center tw-five q-pt-sm">To <span v-if="$q.screen.gt.xs">eloquently</span>
              re-capture money lost to complexity
            </div>

            <div class="row justify-center q-pt-lg">
              <book-button no-caps class="q-mx-md __rb" size="lg">
                <span class="tw-six q-mr-sm text-xs text-white">Sign Up</span>
                <q-icon name="mdi-arrow-right-thick" color="white"></q-icon>
              </book-button>
            </div>

          </div>
        </div>

        <!--        Now here's how it's done-->
        <!--        1. Use your tax savings-->
        <!--        2. Get help before you start-->
        <!--        2. Get rid of easy errors-->
        <!--        3. Find deeper price mismatches-->
        <!--        4. Negotiate shrewdly-->

      </div>
    </div>
    <bill-example></bill-example>
    <dont-pay></dont-pay>

    <div class="row justify-center _oh relative-position">
      <div class="__blob"></div>
      <div class="__blob1"></div>
      <div class="_sent pd12 pw2 relative-position z4">
        <div class="text-center tw-six text-sm text-accent">Only pay when you save</div>
        <div class="text-center tw-six text-xl alt-font _l1-2">
          Pricing with aligned incentives
        </div>
        <div class="text-center tw-six text-sm pw2">
          The lesser of: <span class="text-lg text-accent">25%</span> of savings or <span
            class="text-lg text-accent">10%</span> of the original bill
        </div>

        <div class="q-py-lg"></div>
        <pricing-table></pricing-table>

        <div class="row justify-center q-pt-xl q-mt-xl">
          <book-button size="lg" push class="_a_btn" no-caps>
            <div class="text-sm tw-six">See how much we could save you</div>
            <div class="text-md q-ml-sm">👀</div>
          </book-button>
        </div>


      </div>
    </div>

    <div class="row justify-center __bg">
      <div class="_sent pd12 pw2">
        <div class="text-center text-md tw-five">While you're saving - we're mapping your data into a <span class="cursor-pointer text-a1 tw-eight" @click="$router.push({ name: 'claim-map'})">ClaimMap</span> - which is a real-time actuarial map of the cash price equivalent of your plan claims.</div>
        <div class="row justify-center q-pt-xl">
          <q-btn @click="$router.push({ name: 'claim-map'})" push size="lg" color="a12" no-caps>
            <span class="text-sm tw-six text-a1">Learn More</span>
            <q-icon class="q-ml-sm text-sm text-a1" name="mdi-arrow-right-thick"></q-icon>
          </q-btn>
        </div>
      </div>
    </div>


    <pocket-preview></pocket-preview>

  </q-page>
</template>

<script setup>
  import DontPay from './DontPay.vue';
  import BillExample from './BillExample.vue';
  import PricingTable from './PricingTable.vue';
  import PocketPreview from './PocketPreview.vue';
  import BookButton from 'pages/landing/utils/BookButton.vue';


</script>

<style scoped lang="scss">
  .__rb {
    background: linear-gradient(12deg, var(--q-a3), var(--q-accent));
  }

  .__hid {
    background: linear-gradient(12deg, var(--q-p0), var(--q-p2));
  }


  .__bump {
    transform: translate(-10px, -6px);
  }

  .__bg {
    background: linear-gradient(135deg, var(--q-a12), var(--q-a8));
    color: white;
  }

  .__blob {
    position: absolute;
    z-index: 0;
    width: 130vw;
    height: 150vw;
    border-radius: 50%;
    opacity: 1;
    animation: roam_l 20s infinite;
    background: radial-gradient(var(--q-a2) -50%, transparent 50%);
    transform: translate(5%, 0);
    top: -50vw;
    left: -50vw;
  }
  .__blob1 {
    position: absolute;
    z-index: 0;
    width: 130vw;
    height: 150vw;
    border-radius: 50%;
    opacity: 1;
    animation: roam_l 20s infinite;
    background: radial-gradient(var(--q-s2) -50%, transparent 50%);
    transform: translate(5%, 0);
    right: -50vw;
    bottom: -50vw;
  }
</style>
