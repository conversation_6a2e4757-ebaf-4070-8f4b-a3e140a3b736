<template>
  <div class="row justify-center __pp">
    <div class="_cent pd12 q-px-md">
      <div class="row items-center">
        <div class="col-12 col-md-6 q-pa-md q-pb-lg">
          <div class="text-sm tw-six alt-font">
            <div class="flex items-center">
              <div>Optionally - opt in to</div>
              <q-img class="h20 mw200" fit="contain" :src="logo"></q-img>
            </div>
          </div>
          <div class="text-xl tw-eight alt-font">Erase out-of-pocket cost, lower premiums, create tax-free claims</div>
          <q-separator class="q-my-sm"></q-separator>
          <div class="tw-five text-xs">
            <div>PocketGuard is a self-funded cost sharing pool that gives employees up-front access to their full
              deductible and coinsurance amounts.
            </div>
            <div class="q-pt-sm">Our <span class="tw-six text-primary">CareWallet</span> automates setup and
              administration. When the plan has excess funds you can give the option for a return of premium (taxable as
              wages) or to carry it forward to next plan year.
            </div>
          </div>
        </div>
        <div class="col-12 col-md-6 q-pa-md">
          <div class="__table">
            <table class="text-sm">
              <tbody>
              <tr class="text-grey-8 text-xs">
                <th></th>
                <th>Single</th>
                <th>Family</th>
              </tr>
              <tr>
                <td>Deductible</td>
                <td>$3000</td>
                <td>$7500</td>
              </tr>
              <tr>
                <td>Coinsurance</td>
                <td>20%</td>
                <td>20%</td>
              </tr>
              <tr>
                <td>MOOP</td>
                <td>$9200</td>
                <td>$18,900</td>
              </tr>
              <tr>
                <td>Deductible Guard</td>
                <td>$125</td>
                <td>$325</td>
              </tr>
              <tr>
                <td>MOOP Guard</td>
                <td>$180</td>
                <td>$360</td>
              </tr>
              </tbody>
            </table>
          </div>
          <div class="q-px-lg q-pt-lg">
            <div class="text-xxs tw-five">Fully transparent pooling of funds provides a smoother cost outlay, tax-free
              out of pocket costs, and unused premiums can either be refunded at year-end or applied to next plan year.
            </div>
          </div>
        </div>
      </div>

      <div class="row q-pt-xl">
        <div class="col-6 col-md-3 pw2 q-py-md" v-for="(item, i) in items" :key="`item-${i}`">
          <div class="__c tw-five">
            <div class="text-sm tw-six row items-center">
              <q-icon color="accent" class="q-mr-sm" :name="item.icon" size="25px"></q-icon>
              <div>{{ item.label }}</div>
            </div>
            <div class="text-xxs">{{ item.text }}</div>
            <!--            <div class="font-1-1-8r q-ml-md row justify-center items-center no-wrap" v-if="on !== i">-->
            <!--              <div>{{$limitStr(item.text, 30, '')}}</div>-->
            <!--              <q-chip square dense class="__shift" color="transparent" clickable @click="on = i">-->
            <!--                <span class="font-1-1-2r tw-six text-accent">...</span>-->
            <!--              </q-chip>-->
            <!--            </div>-->
            <!--            <div :class="`__full ${on === i ? '__on' : ''}`">{{item.text}}</div>-->
          </div>
        </div>
      </div>
      <div class="row justify-center q-pt-xl q-mt-lg q-mb-md">
        <q-btn size="lg" class="_a_btn" @click="$router.push({ name: 'pocket-guard'})" no-caps>
          <span class="text-xs tw-six">Learn More</span>
        </q-btn>
      </div>
    </div>
  </div>
</template>

<script setup>
  import logo from 'src/assets/pocketguard_logo.svg';

  const items = [
    {
      icon: 'mdi-flash',
      label: 'Instant Access',
      text: 'To your full deductible/moop. This significantly reduces the stress of unexpected medical expenses.'
    },
    {
      icon: 'mdi-abacus',
      label: 'Tax Advantaged',
      text: 'As a self-funded supplemental health plan, premiums are tax-free - claim payments also tax-free.'
    },
    {
      icon: 'mdi-percent',
      label: 'Discounted',
      text: 'Most people don\'t meet their deductible, so participation costs less than your individual max risk.'
    },
    {
      icon: 'mdi-license',
      label: 'Transparent',
      text: 'If there are excess funds at plan year end, issue a return of premium, or roll it toward next year\'s fund.'
    }
  ]
</script>

<style lang="scss" scoped>

  .__pp {
    color: var(--q-a12);
    background: linear-gradient(135deg, var(--q-a1), var(--q-a0));
  }

  table {
    width: 100%;
    border-collapse: collapse;

    tr {
      th {
        text-align: right;
        padding: 3px 10px;
      }

      td {
        padding: 5px 10px;
        text-align: right;
        font-weight: 600;

        &:first-child {
          text-align: left;
          font-weight: 400;
        }
      }

      &:nth-child(5) {
        td {
          border-bottom: solid 2px white;
          background: var(--q-a0);
          color: var(--q-accent);
          font-weight: 600;
        }
      }

      &:last-child {
        td {
          border-bottom: solid 2px white;
          background: var(--q-a1);
          color: var(--q-a6);
          font-weight: 600;
        }
      }
    }

  }

  .__full {
    max-height: 0;
    overflow: hidden;
    width: 100%;
    transition: all .4s;
    font-size: 1.12rem;
    text-align: center;
  }

  .__on {
    padding-top: 10px;
    max-height: 800px;
  }

  .__shift {
    transform: translate(-5px, 0);
  }

  .__table {
    border: solid 3px var(--q-accent);
    padding: 20px;
    border-radius: 20px;
    background: white;
    color: var(--q-a12);
    //box-shadow: 25px 25px 50px -10px black;
  }
</style>
