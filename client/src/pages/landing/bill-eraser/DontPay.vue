<template>
  <div class="row justify-center q-pt-xl">
    <div class="_cent pd8 q-px-md">
      <div class="row">
        <div class="col-12 col-md-6 q-px-md">
          <div>
            <div class="text-sm tw-six text-accent">Always remember</div>
            <div class="text-xl tw-six _l1-2 alt-font">Never pay the first bill</div>
            <div class="text-sm tw-five q-pt-sm">Upload it and let us go to work</div>
          </div>
          <div class="__item" v-for="(item, i) in items" :key="`item-${i}`" @click="on === i ? on = -1 : on = i">
            <div class="row items-center no-wrap">
              <q-icon name="mdi-triangle" :class="`__t q-mr-md ${on !== i ? '__right' : '__down'}`" size="20px"
                      color="accent"></q-icon>
              <div class="_fw tw-six font-1-1-2r">{{ item.label }}</div>
            </div>
            <div :class="`__full font-1-1-8r ${on === i ? '__on' : ''}`">
              {{ item.text }}
            </div>
          </div>
        </div>
        <div class="col-12 col-md-6 q-py-lg pw2">
          <div class="row justify-center">
            <div class="w800 mw100 q-pa-md">
              <div class="__c">
                <q-img
                    src="https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2FScreen-Recording-2024-10-16-at-10.11.45%E2%80%AFAM_2.gif?alt=media&token=32d54d9e-6520-4b08-9fa5-d2671f45ce4e"
                    fit="contain" class="_fw"></q-img>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row q-pt-md pw2">
        <book-button size="lg" push color="black" no-caps>
          <div class="text-sm tw-six">Send us a test bill</div>
          <q-icon class="text-sm q-ml-sm" name="mdi-arrow-right-thick"></q-icon>
        </book-button>
      </div>

    </div>
  </div>
</template>

<script setup>

  import {ref} from 'vue';
  import BookButton from 'pages/landing/utils/BookButton.vue';

  const on = ref(-1);
  const items = [
    {
      label: 'Finding billing errors',
      text: 'It is common for 15-30% of your medical bills to be fraud, waste, and abuse. The bigger the bill - the truer this becomes as complexity and momentum make it easy to have little extras sprinkled in. We identify these items through automated flags and manual expertise.'
    },
    {
      label: 'Digging through contracts',
      text: 'Most commonly, people are charged different rates than those published in price transparency files or network pricing agreements. There are other contractual issues as well - coordination of benefits, medicare secondary payer, preventive service regs, the complexity is everywhere.'
    },
    {
      label: 'Shrewdly negotiating',
      text: 'Less than 25% of what you pay actually makes it to the doctors and clinicians who provide your care. Doctors are the friend - admin is the bloat. We make life easier for doctors and we can negotiate direct-pay rates that are a win-win. If you\'re self-funded our power here is 10x.'
    }
  ]
</script>

<style lang="scss" scoped>
  .__c {
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 30px 30px 55px -10px var(--q-a2);
  }

  .__item {
    padding: 15px;
    margin: 10px 0;
    background: transparent;
    transition: all .3s;
    transform: none;
    border-radius: 20px;
    cursor: pointer;

    &:hover {
      background: var(--q-a1);
      transform: translate(0, -2px);
    }
  }

  .__full {
    max-height: 0;
    overflow: hidden;
    width: 100%;
    transition: all .4s;
  }

  .__on {
    padding-top: 10px;
    max-height: 800px;
  }

  .__t {
    transform: none;
    transition: all .3s;
  }

  .__right {
    transform: rotate(90deg);
  }

  .__down {
    transform: rotate(180deg);
  }
</style>
