<template>
  <q-page class="text-a10">
    <div class="row justify-center relative-position _oh __pt">
      <div class="_cent">
        <div class="row">
          <div class="col-12 col-md-6 pw2 q-pb-xl">
            <div class="row justify-center">
              <div class="_fw pw2">
                <div class="text-xxxl tw-eight text-primary _l1 alt-font __pg flex items-center">
                  <q-img class="__logo" :src="logo" fit="contain"></q-img>
                </div>
                <div class="text-lg alt-font tw-six q-pt-sm row items-center">
                  goodbye&nbsp;
                  <span :class="`__wrd text-secondary ${moving ? '__in' : ''}`">{{ byes[on] }}</span>
                </div>
                <div class="text-sm tw-five q-py-md">Lower insurance premiums and remove deductibles with a self-funded
                  tax-advantaged cost share
                </div>

                <div class="row q-pt-lg">
                  <book-button class="_s_btn" no-caps push>
                    <span class="tw-six text-xs">Get A Demo</span>
                  </book-button>
                </div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6 pw2">
            <div class="__hello">
              <bill-example></bill-example>
            </div>
          </div>
        </div>

      </div>
    </div>
    <gap-spread></gap-spread>

    <the-catch></the-catch>
    <cc-role></cc-role>
    <how-it-works></how-it-works>
  </q-page>
</template>

<script setup>
  import logo from 'src/assets/pocketguard_logo.svg'
  import BillExample from './BillExample.vue';
  import GapSpread from 'pages/landing/pocket-guard/GapSpread.vue';
  import BookButton from 'pages/landing/utils/BookButton.vue';
  import CcRole from 'pages/landing/pocket-guard/CcRole.vue';
  import HowItWorks from 'pages/landing/pocket-guard/HowItWorks.vue';
  import TheCatch from 'pages/landing/pocket-guard/TheCatch.vue';

  import {onMounted, ref} from 'vue';

  const byes = ['deductible', 'coinsurance', 'ghastly premiums', 'payroll tax', 'unexpected bill'];

  const on = ref(0);
  const moving = ref(false);

  const fullTo = ref();
  const partTo = ref();
  const run = () => {
    fullTo.value = setTimeout(() => {
      moving.value = true;
      partTo.value = setTimeout(() => {
        if (on.value < byes.length - 1) on.value++;
        else on.value = 0;
        moving.value = false;
        run();
      }, 650)
    }, 4000);
  }

  onMounted(() => run())
</script>

<style lang="scss" scoped>
  .__pt {
    background: linear-gradient(-45deg, var(--q-a1), white);
    padding: max(60px, 15vh) 10px 15vh 10px;
  }

  .__pg {
    text-shadow: 2px 2px 2px var(--q-a2);
  }

  .__logo {
    width: 90%;
    height: 60px;
  }

  .__hello {
    width: 100%;
    padding: 40px min(4vw, 40px);
    border-radius: 10px;
    background: white;
    box-shadow: 20px 20px 40px -10px var(--q-a2);
    overflow: hidden;
    //border: solid 5px var(--q-primary);
  }

  .__wrd {
    position: relative;
    animation: swing-back .4s ease-in 1;
    //border-radius: 5px;
  }


  .__in {
    background: transparent;
    animation: swing .4s ease-in 1;
    background: -webkit-linear-gradient(0deg, transparent, transparent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .__live {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -59%);
    text-wrap: nowrap;
  }

  @keyframes swing {
    0% {
      background: -webkit-linear-gradient(270deg, var(--q-secondary), var(--q-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    50% {
      background: -webkit-linear-gradient(270deg, transparent, var(--q-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    100% {
      background: -webkit-linear-gradient(270deg, transparent, transparent);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  @keyframes swing-back {
    0% {
      background: -webkit-linear-gradient(90deg, transparent, transparent);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

    }
    50% {
      background: -webkit-linear-gradient(90deg, transparent, var(--q-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    100% {
      background: -webkit-linear-gradient(90deg, var(--q-secondary), var(--q-secondary));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

</style>
