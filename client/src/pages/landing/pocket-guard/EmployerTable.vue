<template>
  <div class="_fw" v-if="ov?.totals && nv?.totals">
    <div class="_fw q-py-md tw-six text-xs">
      <q-chip color="grey-2" class="tw-six text-xs" square>Employer Controls:</q-chip>
      <div class="row q-py-xs">
        <div class="col-12 col-md-grow q-pa-sm">
          <div class="q-pb-md">How much out of pocket to pass to employees
            <q-btn dense icon="mdi-information" flat color="accent" class="_i_i">
              <q-popup-proxy :breakpoint="20000">
                <div class="w500 mw100 bg-white q-pa-lg">
                  <div class="text-xs tw-five">
                    When deciding how much to charge in
                    <q-img fit="contain" class="h20 w20 q-mx-xs" :src="icon"></q-img>
                    premiums, you need to assume what the risk will be. If you absorb more of it, your employees have
                    lower premiums (like all insurance).<br><br>By default we've used national averages to project the
                    parity cost - which are quite expensive compared to what a private plan can do with our help. We
                    expect you can smash the default rates.<br><br>Still, you can pass all out of pocket costs through
                    to employees - which is what your existing insurance was already doing - and you'd still save 5-10%
                    because we cut out insurance profit & overhead by self-funding. Plus, if claims turn out to be low,
                    you have the option to refund employee premiums with the excess funds.
                  </div>
                </div>
              </q-popup-proxy>
            </q-btn>
          </div>
        </div>
        <div class="col-12 col-md-6 q-pa-sm">
          <q-slider
              label-color="a9"
              color="accent"
              v-model="premSlide"
              :min="0"
              :max="10"
              :markers="5"
              :step="1"
              :label-always="true"
              :label-value="`Employer Risk: ${getPremLabel(premSlide)}`"
          ></q-slider>
        </div>

      </div>

      <div class="row q-pt-lg">
        <div class="col-12 col-md-grow q-pa-sm">
          <div class="q-pb-md">How efficiently care costs are managed
            <q-btn dense icon="mdi-information" flat color="secondary" class="_i_i">
              <q-popup-proxy :breakpoint="20000">
                <div class="w500 mw100 bg-white q-pa-lg">
                  <div class="text-xs tw-five">
                    Now that you have more variable plan costs, you profit by managing them wisely. The lower your
                    costs, the more profitable your plan is.<br><br>By default we've used national averages to project
                    the parity cost - which are quite expensive compared to what a private plan can do with our help. We
                    expect you can smash the default rates.<br><br>Remember, a doctor is doing what they were born to do
                    when you let them manage the health and cost of care for your population.
                  </div>
                </div>
              </q-popup-proxy>
            </q-btn>
          </div>
        </div>
        <div class="col-12 col-md-6 q-pa-sm">
          <q-slider
              label-color="s10"
              color="secondary"
              v-model="costSlide"
              :min="0"
              :max="10"
              :markers="5"
              :step="1"
              :label-always="true"
              :label-value="`Cost Management: ${getCostLabel(costSlide)}`"
          ></q-slider>
        </div>

      </div>
    </div>
    <div class="row items-center">
      <q-space></q-space>
      <div class="q-px-md tw-five text-secondary flex items-center">
        <div class="text-lg">&#x25cf;</div>
        <div class="q-ml-sm text-xs tw-six">current plan</div>
      </div>
    </div>
    <div class="__main">
      <table>
        <thead>
        <tr class="text-xxs">
          <th>Deductible</th>
          <th>Participants</th>
          <th>
            <q-img fit="contain" class="h20 w20 _i_i" :src="white_icon"></q-img>
            /mo
          </th>
          <th>Avg Premium</th>
          <!--          <th>Ann Ins Premium</th>-->
          <!--          <th>-->
          <!--            <q-img fit="contain" class="h20 w20 _i_i" :src="white_icon"></q-img>-->
          <!--            Premium-->
          <!--          </th>-->
          <th>
            <q-img fit="contain" class="h20 w20 _i_i" :src="white_icon"></q-img>
            Cost
          </th>
          <th>Windfall</th>
        </tr>
        </thead>
        <tbody class="text-xs tw-five">
        <tr>
          <td>{{ dollarString(ov.stats.ded, '$', 0) }}</td>
          <td>{{ dollarString(enrolled, '', 0) }}</td>
          <td> -</td>
          <td>{{ dollarString(oldPremium / 12, '$', 0) }}</td>
          <!--          <td>{{ dollarString(oldPremium * enrolled, '$', 0) }}</td>-->
          <!--          <td> - </td>-->
          <td> -</td>
          <td> -</td>

        </tr>
        <tr v-for="(k, i) in keys" :key="`k-${i}`">
          <td>{{ dollarString(vals[k]?.stats?.ded, '$', 0) }}</td>
          <td class="cursor-pointer" @click="openMap(true)">
            {{ dollarString(p[k], '', 0) }}
          </td>
          <td>{{ dollarString(pgs[k].prem / 12, '$', 0) }}</td>
          <td>{{ dollarString((pgs[k].prem + premium) / 12, '$', 0) }}</td>
          <!--          <td>{{ dollarString(premium * enrolled, '$', 0) }}</td>-->
          <!--          <td>{{ dollarString((pgs[k].prem) * p[k], '$', 0) }}</td>-->
          <td>{{ dollarString(pgs[k].cost * p[k], '$', 0) }}</td>
          <td>{{ dollarString((pgs[k].prem * p[k]) - (pgs[k].cost * p[k]), '$', 0) }}</td>
        </tr>
        <tr class="__totals">
          <td> -</td>
          <td>{{ dollarString(enrolled, '', 0) }}</td>
          <td> -</td>
          <td> -</td>
          <!--          <td>{{ dollarString(totes.premium + premium * enrolled, '$', 0) }}</td>-->
          <!--          <td>{{ dollarString(totes.premium, '$', 0) }}</td>-->
          <td>{{ dollarString(totes.cost, '$', 0) }}</td>
          <td>{{ dollarString(totes.windfall, '$', 0) }}</td>
        </tr>
        </tbody>
      </table>
    </div>

    <div class="_fw q-py-lg">
      <div class="text-sm tw-six">Summary:</div>
      <div class="_fw row">
        <div class="col-12 col-md-6 q-py-md pw3">
          <table class="__summary">
            <thead>
            <tr>
              <th></th>
              <th>Old Plan</th>
              <th>New Plan</th>
            </tr>
            </thead>
            <tbody>
            <tr>
              <td>Fixed Premiums</td>
              <td>{{ dollarString(oldPremium * enrolled, '$', 0) }}</td>
              <td>{{ dollarString(premium * enrolled, '$', 0) }}</td>
            </tr>
            <tr>
              <td>
                <q-img class="h20 w20 _i_i" :src="icon" fit="contain"></q-img>
                Premium
              </td>
              <td>-</td>
              <td>
                {{ dollarString(totes.premium, '$', 0) }}
              </td>
            </tr>
            <tr>
              <td>Total Premium</td>
              <td>{{ dollarString(oldPremium * enrolled, '$', 0) }}</td>
              <td>{{ dollarString((premium * enrolled) + totes.premium, '$', 0) }}</td>
            </tr>
            <tr>
              <td>Covered Costs</td>
              <td>{{ dollarString(ov.totals.insured, '$', 0) }}</td>
              <td>{{ dollarString(nv.totals.insured + totes.cost, '$', 0) }}</td>
            </tr>
            <tr>
              <td>Out-Of-Pocket Costs</td>
              <td>{{ dollarString((ov.totals.deductible + ov.totals.coins) * costShift, '$', 0) }}</td>
              <td>{{ dollarString(eeOop, '$', 0) }}</td>
            </tr>
            <tr>
              <td>Employer O-O-P</td>
              <td> -</td>
              <td>{{ dollarString(totes.cost - totes.premium, '$', 0) }}</td>

            </tr>
            <tr>
              <td>Total Plan Cost</td>
              <td>{{ dollarString(oTotal, '$', 0) }}</td>
              <td>{{ dollarString(totes.planCost, '$', 0) }}</td>
            </tr>
            <tr>
              <td>Actuarial Value</td>
              <td>{{ dollarString((ov.totals.insured / (oTotal - ov.totals.insProfit)) * 100, '', 0) }}%</td>
              <td>
                {{
                  dollarString((nv.totals.insured + totes.cost) / (totes.planCost - nv.totals.insProfit) * 100, '', 0)
                }}%
              </td>
            </tr>
            <tr>
              <td>FICA Tax Saved</td>
              <td>{{ dollarString((oldPremium * enrolled) * .153, '$', 0) }}</td>
              <td>{{ dollarString((maxIt(totes.premium, totes.cost) + (premium * enrolled)) * .153, '$', 0) }}</td>
            </tr>

            </tbody>
          </table>

        </div>
        <div class="col-12 col-md-6 q-py-sm pw3">
          <div class="text-xs tw-five">
            <div class="_fw" v-for="(item, i) in riskItems" :key="`r-${i}`">
              <div class="text-xs tw-six q-pt-md">
                {{ item[0] }}
                <q-icon v-if="item[2] === 'up'" class="_i_i q-ml-sm text-xxs" color="green" name="mdi-triangle"></q-icon>
                <q-icon v-else class="_i_i q-ml-sm text-xxs _flip" color="red" name="mdi-triangle"></q-icon>
              </div>
              <div class="text-xxs tw-five">{{ item[1] }}</div>
            </div>
          </div>
        </div>

      </div>
    </div>
    <common-dialog setting="small" v-model="mapP" @update:model-value="openMap">
      <div class="_fw q-pa-md bg-white">
        <div class="_fw row items-center q-pb-md">
          <div class="text-xxs tw-six">Edit Enrollment</div>
          <q-space></q-space>
          <q-chip :color="entered === enrolled ? 'black' : 'red-9'" class="tw-six">
            <span class="text-xxs text-white">{{ dollarString(entered, '', 0) }} of {{ dollarString(enrolled, '', 0) }} <span
                v-if="entered !== enrolled">&nbsp;({{ dollarString(entered - enrolled, '', 0) }})</span></span>
          </q-chip>
        </div>
        <table class="__ft">
          <thead>
          <tr>
            <th>Deductible</th>
            <th>Participants</th>
          </tr>
          </thead>
          <tbody>
          <template v-for="(k, i) in keys" :key="`inp-${k}`">
            <tr>
              <td>{{ dollarString(vals[k].stats.ded, '$', 0) }}</td>
              <td>
                <money-input @focus="hasFocused = i" :id="`row-${i}:col-1`" filled dense v-model="pmap[k]"
                             input-class="text-right tw-six font-1r" prefix="" class="text-right"></money-input>
              </td>
            </tr>
          </template>
          </tbody>
        </table>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import icon from 'src/assets/pg_icon.svg';
  import white_icon from 'src/assets/pg_icon_white.svg';
  import {dollarString} from 'src/utils/global-methods';
  import {computed, onMounted, ref, watch} from 'vue';
  import {moop} from '../claim-map/calcs'

  const emit = defineEmits(['prem-shift'])
  const props = defineProps({
    keys: Array,
    ov: Object,
    nv: Object,
    three: Object,
    one: Object,
    five: Object,
    zero: Object,
    premium: Number,
    oldPremium: Number,
    enrolled: Number
  })

  const maxIt = (a, b) => Math.max(a, b)

  const premSlide = ref(5);
  const costSlide = ref(5);

  const premShift = computed(() => (premSlide.value) / 5);
  const costShift = computed(() => (costSlide.value) / 5);

  watch(premShift, (nv) => {
    if (nv || nv === 0) emit('prem-shift', nv)
  }, { immediate: true });

  const getPremLabel = (val) => {
    if (val < 1) return 'Fully at risk';
    if (val < 3) return 'Majority';
    if (val < 4.5) return 'Above parity';
    if (val < 5.5) return 'Parity';
    if (val < 7) return 'Low';
    if (val < 9) return 'Very Low';
    else return 'Virtually none'
  }

  const getCostLabel = (val) => {
    if (val < 2) return 'Unrealistically low';
    if (val < 3) return 'Very Efficient';
    if (val < 4) return 'Efficient';
    if (val < 4.5) return 'Better than average';
    if (val < 5.5) return 'Average';
    if (val < 7) return 'Poor';
    if (val < 9) return 'Very Poor';
    else return 'Everyone is dying'
  }

  const p = ref({
    zero: 0,
    one: 0,
    three: 0,
    five: 0,
    nv: 0
  })

  const pct = ref({
    zero: .35,
    one: .1,
    three: .25,
    five: .1,
    nv: .2
  })

  const pmap = ref({})
  const mapP = ref(false)
  const entered = computed(() => Object.keys(pmap.value).reduce((a, v) => a + pmap.value[v], 0));
  const openMap = (val) => {
    if (val) {
      mapP.value = true;
      pmap.value = { ...p.value };
    } else if (entered.value === props.enrolled) {
      for (const k in pmap.value) {
        p.value[k] = pmap.value[k];
      }
    }
  }

  const setPs = () => {
    let t = 0;
    for (let i = 0; i < props.keys.length - 1; i++) {
      const v = Math.round((props.enrolled * pct.value[props.keys[i]]));
      p.value[props.keys[i]] = v
      t += v
    }
    const lastKey = props.keys[props.keys.length - 1]
    p.value[lastKey] = props.enrolled - t
  }

  watch(() => props.enrolled, (nv, ov) => {
    if (nv && nv !== ov) {
      setPs()
    }
  }, { immediate: true })

  const vals = computed(() => {
    const obj = {};
    for (let i = 0; i < props.keys.length; i++) {
      obj[props.keys[i]] = props[props.keys[i]]
    }
    return obj;
  })

  const pgs = computed(() => {
    const obj = {};
    const nv = props.nv || { totals: {} };
    const maxPossible = moop * props.enrolled;
    for (const k in vals.value) {
      const { totals } = vals.value[k] || { totals: {} }
      obj[k] = { prem: 0, cost: 0 }
      const max = (nv.totals.deductible + nv.totals.coins);
      const parity = (totals.deductible + totals.coins);
      const base = Math.max(0, max - parity);
      const prem = Math.min(base * premShift.value, maxPossible);
      const cost = Math.min(base * costShift.value, maxPossible);
      obj[k].prem = prem / totals.enrolled
      obj[k].cost = cost / totals.enrolled
    }
    return obj;
  })

  const totes = computed(() => {
    const { totals } = props.nv || { totals: {} }
    const obj = {
      premium: 0,
      cost: 0,
      windfall: 0,
      planCost: ((totals.deductible + totals.coins) * costShift.value) + totals.insured + totals.insProfit
    };
    for (const k in vals.value) {
      const prem = (pgs.value[k].prem) * p.value[k]
      const cost = (pgs.value[k].cost) * p.value[k]
      obj.premium += prem;
      obj.cost += cost;
      obj.windfall += (prem - cost);
    }
    return obj;
  })

  const oTotal = computed(() => {
    const { totals } = props.ov || { totals: {} }
    return totals.insured + totals.insProfit + ((totals.deductible + totals.coins) * costShift.value)
  })
  const eeOop = computed(() => {
    const { totals } = props.nv || { totals: {} }
    return Math.max(0, ((totals.deductible + totals.coins) * costShift.value) - totes.value.premium)
  })

  const hasFocused = ref(-1);

  const riskItems = computed(() => {
    const { totals: ot } = props.ov || { totals: {} }
    const { totals: nt } = props.nv || { totals: {} }
    return [
      ['Costs', `In all scenarios your costs are down. Even if you want to keep all other things the same - your fixed insurance profit & overhead reduces your costs by ${dollarString(((ot.insProfit - nt.insProfit) / ((ot.insured + (ot.deductible + ot.coins)) * costShift.value)) * 100, '', 1)}%`, 'down'
      ],
      [
        'Employee Experience', 'More choices, lower premiums, less taxes, more predictability - but most importantly, a care navigation team that can provide better care at lower cost.', 'up'
      ],
      [
        'Control', 'Besides reducing sunk costs and giving you a budget you can keep if you manage costs - you also get a map of your plan costs and actuarial value that mean better rates next year.', 'up'
      ],
      [
        'Risk', 'You can be aggressive - or take on no risk at all. In all cases, the risk is contained to the o-o-p max. Everything over $9,200 PEPY is still 100% insurance carrier risk.', 'down'
      ],
      [
        'Taxes', 'You save marginally more payroll tax, but the hidden savings is that employees with more expenses keep saving on o-o-p costs - instead of just on premiums or having to guess on HSA contributions.', 'down'
      ]
    ]
  })

  let tabListener;
  const senseTab = () => {
    tabListener = document.addEventListener('keydown', (e) => {
      if (hasFocused.value > -1 && e.keyCode === 9) {
        const nextId = `row-${hasFocused.value + 1}:col-1`;
        const el = document.getElementById(nextId);
        if (el) el.focus();
      }
    });
  };

  onMounted(() => senseTab())

</script>

<style lang="scss" scoped>

  .__main {
    width: 100%;
    overflow-x: scroll;

    table {
      width: 100%;
      border-collapse: collapse;
      text-align: right;

      tr {
        th {
          background: linear-gradient(180deg, var(--q-accent), var(--q-a7));
          color: white;

          &:first-child {
            padding: 10px 20px;
            border-radius: 8px 0 0 0;
          }

          &:last-child {
            padding: 10px 20px;
            border-radius: 0 8px 0 0;
          }
        }

        td, th {
          padding: 10px;
        }

      }

      tbody {
        tr {
          &:nth-child(odd) {
            td {
              background: var(--q-a0);
            }
          }

          &:first-child {
            td {
              color: white;
              background: var(--q-secondary) !important;
              font-weight: 600;
            }
          }
        }

        .__totals {
          td {
            background: linear-gradient(0deg, var(--q-primary), var(--q-p6)) !important;
            color: white;
            font-weight: 600;

            &:last-child {
              border-radius: 0 0 8px 0;
            }

            &:first-child {
              border-radius: 0 0 0 8px;
            }
          }
        }
      }


    }
  }

  .__ft {
    width: 100%;
    border-collapse: collapse;
    text-align: right;

    tr {
      th {
        padding: 3px 8px;
      }

      td {
        padding: 4px 8px;
      }

      &:nth-child(odd) {
        td {
          background: var(--q-a1);
        }
      }
    }
  }

  .__summary {
    width: 100%;
    border-collapse: collapse;

    tr {
      th {
        padding: 4px 8px;
        text-align: right;
        font-size: var(--text-xxs);
      }

      td {
        font-size: var(--text-xs);
        text-align: right;
        padding: 8px;
        font-weight:  500;

        &:first-child {
          text-align: left;
          font-weight: 600;
        }
        &:nth-child(2) {
          color: var(--q-s9);
        }
        &:nth-child(3) {
          color: var(--q-a7);
        }
      }
      &:nth-child(odd) {
        td {
          background: var(--q-a0);
        }
      }
    }
  }
</style>
