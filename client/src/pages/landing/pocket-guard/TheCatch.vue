<template>
  <div class="_fw __ctch">
    <div class="row justify-center relative-position _oh">
      <div class="_cent pd12 pw2">
        <div class="__blob"></div>
        <div class="__blob1"></div>
        <div class="row relative-position z1">
          <div class="col-12 col-md-6 q-pa-md relative-position">
            <div :class="`_fw ${$q.screen.lt.md ? 'text-center' : ''}`">
              <div class="text-xl alt-font tw-six">If you're looking for the catch
              </div>
            <div class="text-sm tw-six text-accent alt-font"><span class="text-a12"> You won't find it.</span> Let's do
              the math.
            </div>
            </div>
            <div class="_fw text-xs tw-five">
              <div class="q-pt-md q-pb-md">
                <div class="tw-six text-xs text-a12 row justify-center items-center">
                  <div class="col-5 col-sm-3 q-pa-sm">
                    <div class="row justify-center">
                      <div class="__txt">Cost of care
                        <div>(Say - $80)</div>
                      </div>
                    </div>
                  </div>
                  <div class="col-2 col-sm-1">
                    <div class="row justify-center">
                      <div class="q-mx-md text-lg text-accent">+</div>
                    </div>
                  </div>
                  <div class="col-5 col-sm-3 q-pa-sm">
                    <div class="row justify-center">
                      <div class="__txt">25%
                        <div>($20)</div>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-1">
                    <div class="row justify-center">
                      <div v-if="$q.screen.gt.sm" class="q-mx-md text-accent text-lg">=</div>
                      <div v-else class="_fw q-mx-md bg-accent" style="height: 2px"></div>
                    </div>
                  </div>
                  <div class="col-12 col-sm-3 q-pa-sm">
                    <div class="row justify-center">
                      <div class="__txt">Premium
                        <div>($100)</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="text-xs">Under the ACA, your insurance carrier has to pay out 80% of premiums on care costs.
                So this is how premiums are calculated ($80 out of $100 premium).
              </div>
              <div class="q-pt-md">
                We moved a chunk of costs away from your insurance carrier - and introduced a fully self funded
                supplemental
                plan to cover them. That plan has no overhead costs, and any unused premiums stay with the plan.
              </div>
            </div>

          </div>
          <div class="col-12 col-md-6 q-py-md pw2 relative-position">
            <div class="__c">
              <div class="text-xs tw-six q-py-lg text-center">Come ask all your questions - you'll love what you
                find
              </div>

              <div class="_form_grid _f_g_r">
                <div class="_form_label">Your Name</div>
                <div class="q-pa-sm">
                  <q-input placeholder="Tony Fauci" dense filled v-model="form.name"></q-input>
                </div>
                <div class="_form_label">Email</div>
                <div class="q-pa-sm">
                  <email-field hide-bottom-space dense filled v-model="form.email"></email-field>
                </div>
                <div class="_form_label">Phone</div>
                <div class="q-pa-sm">
                  <phone-input :input-attrs="{ dense: true, filled: true }" v-model="form.phone"></phone-input>
                </div>
                <div class="_form_label">Note</div>
                <div class="q-pa-sm">
                  <q-input type="textarea" filled v-model="form.message"
                           placeholder="Say something - anything"></q-input>
                </div>
              </div>
              <div class="q-py-md row justify-end">
                <q-btn color="primary" @click="save()" no-caps push dark>
                  <span class="text-xxs tw-six">Send It</span>
                  <q-icon class="q-ml-sm" name="mdi-send"></q-icon>
                </q-btn>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import EmailField from 'components/common/input/EmailField.vue';
  import PhoneInput from 'components/common/phone/PhoneInput.vue';

  import {ref} from 'vue';
  import {useReqs} from 'stores/reqs';
  import {HForm} from 'src/utils/hForm';

  const reqStore = useReqs();

  const formFn = (defs) => {
    return {
      email: '',
      ...defs
    }
  }
  const { form, save } = HForm({
    store: reqStore,
    formFn,
    validate: true,
    notify: true,
    successMessage: 'We\'ll be in touch',
    vOpts: ref({ email: { name: 'Email', v: ['email'] } }),
    afterFn: () => form.value = formFn()
  })
</script>

<style lang="scss" scoped>
  .__plus {
    line-height: 1rem;
  }

  .__txt {
    background: white;
    position: relative;
    text-align: center;
    padding: 15px;
    width: 100%;
    max-width: 180px;
    border-radius: 8px;
    //box-shadow: 0 4px 12px -4px var(--q-a2);

    div {
      width: 100%;
      //position: absolute;
      //bottom: 0;
      //left: 50%;
      //transform: translate(-50%, 100%);
      text-align: center;
      font-size: var(--text-xxs);
      color: var(--q-accent);
    }

  }

  .__c {
    padding: 35px 20px;
    border-radius: 20px;
    background: white;
    box-shadow: 30px 30px 60px -24px var(--q-p2);
  }

  .__blob {
    position: absolute;
    z-index: 0;
    width: 1900px;
    max-width: 180vw;
    height: 1650px;
    max-height: 120vw;
    border-radius: 50%;
    opacity: .6;
    background: radial-gradient(var(--q-a2) -30%, transparent 50%);
    animation: roam 20s infinite;
    bottom: -20%;
    left: -10%;
    transform: translate(5%, 0);
  }

  .__blob1 {
    position: absolute;
    z-index: 0;
    width: 200vw;
    height: 150vw;
    border-radius: 50%;
    opacity: .6;
    //background: radial-gradient(var(--q-p3) -30%, transparent 50%);
    animation: roam 20s infinite;
    bottom: -50vw;
    right: -120vw;
    transform: translate(5%, 0);
  }
</style>
