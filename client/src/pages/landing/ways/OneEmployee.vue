<template>
  <div class="_fw">
    <div class="text-center text-sm tw-six q-pb-sm">Now break it down to a single employee</div>
    <div class="row justify-center">
      <div class="__t text-lg">
        Let's say
        <q-chip clickable color="grey-2" class="tw-six text-lg main-font">
          <span class="q-mr-xs text-lg">{{ person[1] }}&nbsp;&nbsp;{{ person[0] }}</span>
          <q-icon size="25px" name="mdi-menu-down"></q-icon>
          <q-menu>
            <q-list separator>
              <q-item clickable v-for="(p, i) in ppl" :key="`ppl-${i}`" @click="setPerson(p)">
                <q-item-section>
                  <q-item-label class="text-p12 font-1-1-4r">{{ p[0] }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <span class="font-1-1-2r">{{ p[1] }}</span>
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-chip>
        works for you.
      </div>
    </div>

    <div class="row justify-center text-lg">
      <div class="__c __w">
        <table class="__w">
          <tr class="__r">
            <td>Budget for {{ person[0] }}'s job</td>
            <td class="__s">
              {{ dollarString(pay, '$', 0) }}
              <q-icon class="__i" name="mdi-menu-down"></q-icon>
              <q-menu>
                <q-list separator>
                  <q-item clickable v-for="(p, i) in pays" :key="`fpr-${i}`" @click="setPay(p)">
                    <q-item-section>
                      <q-item-label class="tw-six text-p12">{{ dollarString(p, '$', 0) }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </td>
          </tr>
          <tr class="__lb">
            <td>Health benefit cost</td>
            <td class="__s">
              {{ dollarString(planCost, '$', 0) }}
              <q-icon class="__i" name="mdi-menu-down"></q-icon>
              <q-menu>
                <q-list separator>
                  <q-item clickable v-for="(p, i) in hbCosts" :key="`hbc-${i}`" @click="setCost(p)">
                    <q-item-section>
                      <q-item-label class="tw-six text-p12">{{ dollarString(p, '$', 0) }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-menu>
            </td>
          </tr>
        </table>
        <div class="relative-position _fw __g text-sm tw-six">
          <div class="_fw __slide">
            <div class="_fw relative-position h40">

              <div class="__per" :style="{ right: (100 - (erCost * 100))/2 + '%'}">
                <div class="__nr">{{ dollarString(100 - erCost * 100, '', 0) }}% {{ person[0] }}</div>
              </div>
              <q-btn class="__l" v-if="erCost > 0" flat color="primary" @click="setEr(erCost - .1)"
                     icon="mdi-minus-thick"></q-btn>
              <q-btn class="__rg" v-if="erCost < 1" flat color="accent" icon="mdi-plus-thick"
                     @click="setEr(erCost + .1)"></q-btn>
              <div id="graph">
                <div class="__er" :style="{width: erCost * 100 + '%'}">
                  <div class="row justify-center _fa">
                    <div class="relative-position _fh">
                      <div class="__erp">
                        <div class="__nr text-p4">{{ dollarString(erCost * 100, '', 0) }}% Employer</div>
                      </div>
                    </div>
                  </div>
                  <div class="__handle">
                    <div class="flex items-center no-wrap _fh">
                      <q-btn :class="erCost > .1 ? '' : '__ghost'" @click="setEr(erCost - .1)" dense flat
                             color="primary" icon="mdi-menu-left"></q-btn>
                      <div class="w5 _fh bg-white"></div>
                      <q-btn :class="erCost < .9 ? '' : '__ghost'" color="accent" icon="mdi-menu-right" dense flat
                             @click="setEr(erCost + .1)"></q-btn>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <table class="__w">

          <tr class="__spc">
            <td></td>
            <td></td>
          </tr>
          <tr class="__spc">
            <td></td>
            <td></td>
          </tr>
          <tr class="__sub text-sm">
            <td>Employer pays</td>
            <td>{{ dollarString(erCost * planCost, '$', 0) }}</td>
          </tr>
          <tr class="__sub text-sm">
            <td>{{ person[0] }} pays</td>
            <td>{{ dollarString(planCost - (erCost * planCost), '$', 0) }}</td>
          </tr>

          <tr class="__a">
            <td>{{ person[0] }} gross pay</td>
            <td>{{ dollarString(gross, '$', 0) }}</td>
          </tr>
          <tr class="__suba text-sm">
            <td>Payroll Tax</td>
            <td>-{{ dollarString(gross * .0765, '$', 0) }}</td>
          </tr>
          <tr class="__suba text-sm">
            <td>Income Tax (estimated)</td>
            <td>-{{ dollarString(estTax, '$', 0) }}</td>
          </tr>
          <tr class="__a">
            <td>{{ person[0] }} take-home pay</td>
            <td>{{ dollarString((gross * .9235) - estTax, '$', 0) }}</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="row justify-center q-px-sm">
      <div class="__c __w">
        <div class="text-md flex items-center">
          If you change the percent of premium you pay (try it), {{ person[0] }}'s take-home pay is completely
          unchanged. You gave {{ person[2] }} nothing and took {{ person[3] }} ability to choose where to spend that
          money.
          <br><br><b>So give {{ person[0] }} the money - let {{ person[2] }} choose how much to put toward
          healthcare.</b>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

  import {dollarString} from 'src/utils/global-methods';
  import {computed} from 'vue';

  const emit = defineEmits(['update:person', 'update:pay', 'update:plan-cost', 'update:er-cost']);
  const props = defineProps({
    person: { required: false },
    pay: { required: false },
    planCost: { required: false },
    erCost: { required: false }
  })

  const ppl = [['Frank', '👨🏻', 'him', 'his'], ['Alice', '👩🏾', 'her', 'hers'], ['Jim', '👨🏼', 'him', 'his'], ['Sarah', '👩🏻', 'her', 'hers'], ['Juan', '👨🏽', 'him', 'his'], ['May', '👩🏿', 'her', 'hers'], ['Neil', '👨🏾', 'him', 'his'], ['Sharon', '👩🏽', 'her', 'hers'], ['Jerry', '👨🏿', 'him', 'his'], ['Halle', '👩🏼', 'her', 'hers']]
  const pays = [20000, 30000, 40000, 50000, 60000, 70000, 80000, 90000, 100000, 120000, 150000, 180000, 200000, 300000, 400000];
  const hbCosts = [2500, 5000, 10000, 15000, 20000, 30000, 40000];
  const ers = [.1, .2, .3, .4, .5, .6, .7, .8, .9, 1];

  const gross = computed(() => props.pay - props.planCost);

  const setPerson = (val) => {
    emit('update:person', val);
  }
  const setPay = (val) => {
    emit('update:pay', val);
  }
  const setCost = (val) => {
    emit('update:plan-cost', val);
  }
  const setEr = (val) => {
    emit('update:er-cost', Math.max(0, Math.min(val, 1)))
  }


  const highest = 500000000000000000;
  const brackets = {
    0: 15700,
    1: 59850,
    2: 95350,
    3: 182100,
    4: 231250,
    5: 578100,
    6: highest
  }
  const taxRates = {
    0: .1,
    1: .12,
    2: .22,
    3: .24,
    4: .32,
    5: .35,
    6: .37
  }
  const estTax = computed(() => {
    let tax = 0;
    let last = 0;
    for (const k in Object.keys(brackets)) {
      const bracket = brackets[k];
      if (gross.value >= bracket) {
        tax += ((bracket - last) * taxRates[k])
        last = bracket;
      } else {
        tax += ((gross.value - last) * taxRates[k]);
        break;
      }
    }
    return Math.max(0, tax);
  })
</script>

<style lang="scss" scoped>
  .__c {
    padding: max(15px, 2vw) 0;
    margin: 1vw;
    transform: none;
    transition: all .2s;
  }

  .__t {
    padding: 20px 35px;
    border-radius: 45px;
    border: solid 2px black;
    background: white;
    text-align: center;
  }

  .__d {
    padding: 4px 8px;
    border-radius: 5px;
    text-align: center;
    margin: 0 5px;
    cursor: pointer;
  }

  .__i {
    transform: translate(0, -12%);
  }

  .__w {
    width: 100%;
    max-width: 1000px;
  }

  table {

    border-collapse: collapse;

    .__s {
      cursor: pointer;
      transform: none;
      box-shadow: none;
      transition: all .2s;
      text-wrap: nowrap;

      &:hover {
        transform: translate(0, -2px);
      }
    }

    td {
      font-weight: 600;
      padding: 10px 10px;
      //border-bottom: solid 3px #f0f0f0;
    }

    tr {
      td:last-child {
        text-align: right;
      }
    }

    .__r {
      color: var(--q-accent);
    }

    .__lb {
      color: var(--q-primary);
    }

    .__sub {
      color: var(--q-p4);

      td:first-child {
        padding-left: 35px;
      }
    }

    .__suba {
      td {
        color: #999;
      }

      td:first-child {
        padding-left: 35px;
      }
    }

    .__a {
      td {
        color: black;
      }
    }
  }

  .__slide {
    position: absolute;
    top: 5px;
    left: 5px;
    width: 100%;
  }

  .__ghost {
    opacity: 0;
    z-index: -2;
    pointer-events: none;
  }

  .__l, .__rg {
    position: absolute;
    top: 50%;
    z-index: 10;
    transform: translate(0, -50%);
  }

  .__l {
    left: 0;
  }

  .__rg {
    right: 0;
  }

  .__g {
    position: relative;
    transform: none;
    transition: all .2s;

    &:hover {
      transform: translate(0, -3px);
    }
  }

  #graph {
    border-radius: 30px;
    width: 100%;
    height: 40px;
    background: var(--q-a2);
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
  }

  .__er {
    border-radius: 30px 0 0 30px;
    background: var(--q-p1);
    height: 100%;
    position: relative;

    .__handle {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 50%;
      height: 100%;
      transform: translate(50%, -50%);
    }
  }

  .__per {
    position: absolute;
    top: 40px;
    right: 0;
    transform: translate(50%, 0px);
    overflow: visible;
    color: var(--q-a4);
  }

  .__erp {
    position: absolute;
    top: 100%;
    left: 0;
    transform: translate(-50%, 0px);
    overflow: visible;
  }

  .__nr {
    text-wrap: nowrap;
  }

  .__spc {
    td {
      padding: 20px;
    }
  }
</style>
