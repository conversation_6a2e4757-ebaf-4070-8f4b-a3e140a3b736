<template>
  <q-page class="_fw relative-position">
    <div class="row justify-center __dt">
      <div class="_cent pw2">

        <div class="row q-pt-md">
          <div class="col-12 col-md-6 pw2">
            <div class="text-lg tw-six __tit">{{ qs.length }} questions that will</div>
            <div class="text-xxl text-a0 tw-six">
              <span class="">Change</span> your idea of a group health
              plan
            </div>

            <q-separator color="a9" class="q-my-md"></q-separator>

            <div class="row items-center q-pb-xl">
              <template v-for="(q, i) in qs" :key="`q-${i}`">
                <q-btn @click="active = i" flat color="s7">
                  <span :class="`text-md tw-six text-${active === i ? 'a3' : 'a7'}`">{{ i + 1 }}</span>
                </q-btn>
                <div class="text-md text-a7" v-if="i < qs.length - 1">|</div>
              </template>

            </div>

          </div>
          <div class="col-12 col-md-6 pw2 text-md text-p4">
            <div class="__c">

              <q-tab-panels transition-duration="500" v-model="active" class="_panel" animated
                            transition-prev="slide-down" transition-next="slide-up">
                <q-tab-panel class="_panel" v-for="(q, i) in qs" :key="`qp-${i}`" :name="i">
                  <div class="text-md alt-font q-pb-md text-s0">
                    {{ q }}
                  </div>
                  <div class="row q-py-sm">
                    <q-checkbox dark color="primary" size="lg" :model-value="answer" class="text-md tw-six text-grey-4"
                                label="Doctor"></q-checkbox>
                  </div>
                  <div class="row q-py-sm">
                    <q-checkbox dark size="lg" :model-value="false" class="text-grey-4 tw-six"
                                label="Insurance Salesman"></q-checkbox>
                  </div>
                </q-tab-panel>
              </q-tab-panels>
            </div>

          </div>
        </div>
        <div class="q-py-lg pw2">
          <book-button class="_a_btn" no-caps size="lg" push>
            <span class="tw-six text-sm">Meet with a plan doctor</span>
            <q-icon class="q-ml-sm" name="mdi-calendar"></q-icon>
          </book-button>
        </div>

      </div>
    </div>


    <div class="row justify-center __d">
      <div class="_cent pd5 pw4">
        <div class="row">
          <div>
            <div class="text-md tw-six _l1-3 relative-position">Time for a health plan run by <span
                class="text-primary">doctors</span>
              <div class="__box"></div>
            </div>
            <div class="text-xl tw-six">Let's explore how that looks</div>
            <div class="text-sm">{{ $q.screen.lt.md ? 'Tap' : 'Click' }} any element to read more</div>
            <q-separator class="q-my-lg"></q-separator>
          </div>
        </div>
        <new-way></new-way>
        <div class="_fw __nt">

          <div class="text-xl tw-six">What you need to make it happen</div>
          <div class="text-sm text-p12 q-pb-md">CommonCare open infrastructure means these roles can be filled by any
            qualified party
          </div>
          <new-team></new-team>
        </div>

      </div>
    </div>

    <you-pay></you-pay>


    <div class="_fw __ot">
      <div class="row justify-center">
        <div class="_cent pd12 pw4">
          <div class="text-xl text-center tw-six text-white">Your plan has some <span
              class="text-s7">malignant</span> parts
          </div>
          <!--          <div class="text-xl tw-six text-p2 text-center">Like every part that isn't patient and doctor</div>-->
          <div class="text-center text-md tw-six text-s7 alt-font">Ready to cut them out?</div>
          <!--            <new-team></new-team>-->
          <old-way></old-way>
        </div>
      </div>

    </div>


    <div class="row justify-center __steer">
      <div class="_cent q-pb-xl">
        <div class="_fw pd12 q-px-sm">
          <div class="row">
            <div class="col-12 col-md-6 q-pa-md">
              <div class="text-sm alt-font tw-six">How healthcare looks when you</div>
              <div class="text-xxl tw-six text-a12">
                Get the right doctor steering
              </div>
              <div class="row q-py-md">
                <q-chip class="q-mr-sm q-my-sm" color="ir-grey-2" v-for="(ch, i) in changes" :key="`ch-${i}`">
                  <q-icon v-bind="{name: 'mdi-triangle', ...ch[1]}"></q-icon>
                  <span class="q-ml-sm tw-six font-1r">{{ ch[0] }}</span>
                </q-chip>
              </div>
            </div>
            <div class="col-12 col-md-6 q-pa-md">
              <div class="row justify-center">
                <div class="col-12 q-py-md pw3">
                  <div class="text-sm tw-five text-secondary"><b>Instead of:</b> <span
                      class="text-xs text-s12">Your business <span class="__s">|</span> buys insurance <span
                      class="__s">|</span> sells it back to employees <span class="__s">|</span> Insurance uses administrators <span
                      class="__s">|</span> to adjudicate networks and formularies <span class="__s">|</span> that dictate access + cost of care <span
                      class="__s">|</span> which determines next year's premium</span></div>
                </div>
                <div class="col-12 q-py-md pw3">
                  <div class="text-sm tw-five text-primary"><b>You get:</b> <span class="text-xs text-p12">Your business <span
                      class="__a">|</span> pays employees <span class="__a">|</span> who choose a doctor <span
                      class="__a">|</span> to own their care needs <span class="__a">|</span> insurance is just for big risks </span>
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>

  import {onMounted, ref} from 'vue';
  import OldWay from './OldWay.vue';
  import NewTeam from './NewTeam.vue';
  import NewWay from './NewWay.vue';
  import YouPay from './you-pay/YouPay.vue';
  import BookButton from 'pages/landing/utils/BookButton.vue';

  const active = ref(0);
  const qs = [
    'Who could best help you reduce your healthcare spend?',
    'Who could best help you navigate difficult health conditions?',
    'Who could best help you negotiate with specialist doctors?',
    'Who can directly buy down the cost of prescription meds?',
    'Who can legally tell your employees "don\'t go straight to the ER"?',
    'Who would employees trust most with their healthcare?'
  ];
  const up = { color: 'primary' };
  const down = { color: 'secondary', class: '__flip' };
  const changes = [

    ['Care Quality', up],
    ['Care Cost', down],
    ['Physician Access', up],
    ['Visits', down],
    ['Med Access', up],
    ['Waste/Abuse', down],
    ['Condition Control', up],
    ['Med Cost', down],

  ]

  const answer = ref(false);
  const to = ref();
  const run = () => {
    to.value = setTimeout(() => {
      if (active.value < qs.length - 1) active.value = active.value + 1;
      else active.value = 0;
      answer.value = false;
      setTimeout(() => {
        answer.value = true;
      }, 2500)
      run();
    }, 4000)
  }
  onMounted(() => run())
</script>

<style lang="scss" scoped>
  .__dark_top {

  }

  .__dt {
    padding-top: calc(15vh + 80px);
    padding-bottom: 15vh;
    transform: translate(0, -80px);
    color: white;
    background: linear-gradient(0deg, var(--q-a12), black);
    //background: linear-gradient(0deg, var(--q-s11), black);
    //background: linear-gradient(180deg, var(--q-s12), var(--q-s12));
  }

  .__tit {
    //color: var(--q-primary);
    background: -webkit-linear-gradient(270deg, var(--q-a7), var(--q-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }


  .__c {
    background: var(--q-a12);
    border-radius: 15px;
    padding: 30px 20px;
    border: solid 2px var(--q-a7);
    position: relative;
    box-shadow: 30px -30px 100px -40px var(--q-a7);
  }

  .__s, .__a {
    font-weight: bold;
    margin: 0 3px;
  }

  .__s {
    color: var(--q-secondary);
  }

  .__a {
    color: var(--q-primary);
  }

  .__flip {
    transform: rotate(180deg);
  }

  .__d {
    background: linear-gradient(180deg, white, var(--q-a1));
  }

  .__nt {
    padding: min(150px, 12vh) 0;
  }

  .__ot {
    background: linear-gradient(180deg, var(--q-s11), black);
  }

  .__steer {
    background: linear-gradient(10deg, var(--q-a1), var(--q-a0));
  }
</style>
