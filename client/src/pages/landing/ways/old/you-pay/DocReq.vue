<template>
  <div class="_fw">

    <div class="__form">
      <div class="text-xs tw-six q-pb-md">Talk to one of our doctors near you, learn how they can cut
        costs and
        improve health outcomes
      </div>

      <zip-select
          @five="form.zip = $event"
          v-model="form.zip"
          @focus="hasFocused = true"
          input-class="tw-six"
          :placeholder="form.zip ? '' : 'Your Zip Code'"
          >
        <template v-slot:selected-item>
          <div class="tw-six">{{ form.zip }}</div>
        </template>
        <template v-slot:prepend>
          <q-icon name="mdi-map-marker" color="red"></q-icon>
        </template>
      </zip-select>

      <div class="_fw">
        <q-input v-model="form.orgName"  input-class="tw-six" placeholder="Company">
        </q-input>

        <q-input
            v-bind="{
                    inputClass: 'tw-six',
                    type: 'number',
                    placeholder: 'Employee Count',
                    errorText: 'Cannot Be Empty'
                  }"
            v-model.number="form.eeCount"
        ></q-input>

        <q-input v-model="form.name"  input-class="tw-six" placeholder="Your Name">
        </q-input>

        <email-field :icon="false" @update:valid="maybeSave" hide-bottom-space  input-class="tw-six"
                     placeholder="Email" :label="undefined" v-model="form.email"></email-field>
        <phone-input
            v-model="form.phone"
            @update:valid="maybeSave"
            :input-attrs="{ placeholder: 'Phone', inputClass: 'tw-six' }"></phone-input>


        <q-slide-transition>
          <div v-if="form?._id" class="q-pt-md text-xxs tw-six text-secondary row items-center">
            <div>Got it. We'll reach out!</div>
            <q-space></q-space>
            <q-chip clickable color="transparent" @click="reset">
              <span class="tw-six">Clear Form</span>
              <q-icon color="red" name="mdi-close" classs="q-ml-sm"></q-icon>
            </q-chip>
          </div>
        </q-slide-transition>
      </div>

    </div>
  </div>
</template>

<script setup>
  import PhoneInput from 'components/common/phone/PhoneInput.vue';
  import ZipSelect from 'components/common/geo/pickers/ZipSelect.vue';
  import EmailField from 'components/common/input/EmailField.vue';

  import {HForm} from 'src/utils/hForm';
  import {nextTick, ref} from 'vue';
  import {useReqs} from 'stores/reqs';

  const reqStore = useReqs();
  const hasFocused = ref(false);

  const formFn = (defs) => {
    return {
      ...defs
    }
  }
  const { form, save } = HForm({
    store: reqStore,
    notify: false,
    validate: true,
    beforeFn: (val) => {
      console.log('before save', val);
      return val;
    },
    params: ref({ runJoin: { patchReq: true } }),
    formFn,
    vOpts: ref({
      email: { name: 'Email', v: ['email'] }
    })
  })

  const reset = () => {
    form.value = formFn();
    hasFocused.value = false;
  }

  const maybeSave = (val) => {
    if (val) nextTick(() => save())
  }

</script>

<style lang="scss" scoped>

    .__form {
      //transform: translate(0, -80px);
      width: 100%;
      padding: 40px 30px 60px 30px;
      background: var(--q-ir-grey-1);
      //box-shadow: 12px 12px 30px rgba(0,0,0,.1);
      text-shadow: 1px 1px 0 white;
      border-radius: 7px 7px max(7vw, 60px) 7px;
      box-shadow: 25px 25px 50px var(--q-a1);
    }

</style>
