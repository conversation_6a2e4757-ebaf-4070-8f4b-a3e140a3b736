<template>
  <div class="_fw">

    <q-checkbox v-model="optOut" label="No Insurance"></q-checkbox>
    <div class="__adj">
      <table>
        <tbody>
        <tr>
          <td>Insurance premium (mo)</td>
          <td>
            <money-input :disable="optOut" class="w90" input-class="tw-six num-font text-s10 text-xs" dense borderless prefix="$" @update:model-value="premium = $event" :model-value="usePremium"></money-input>
          </td>
        </tr>
        <tr>
          <td>Out of pocket spend (mo)</td>
          <td>
            <money-input class="w90" input-class="tw-six num-font  text-secondary text-xs" dense borderless
                         prefix="$" v-model="oopSpend"></money-input>
          </td>
        </tr>
        <tr>
          <td>Annual deductible</td>
          <td>
            <money-input class="w90" input-class="tw-six num-font text-s7 text-xs" dense borderless prefix="$"
                        :model-value="ded * 12" @update:model-value="setStat('ded', $event)"></money-input>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div class="row q-py-lg">
      <div class="__total text-md tw-six q-mt-lg q-mt-sm">
        Total Spend: <span class="tw-eight text-s6">{{ dollarString(usePremium + oop, '$', 0) }}</span>/mo
      </div>
    </div>

    <div class="__bar">
      <div class="__ins" :style="{ width: (usePremium/spend) * 100 + '%'}">
        <div></div>
        <div>Insurance Premium<br><b>{{ dollarString(usePremium, '$', 0) }}</b></div>
      </div>
      <div class="__ded" :style="{ width: (ded/spend) * 100 + '%'}">
        <div></div>
        <div>Deductible<br><b>{{ dollarString(ded, '$', 0) }}</b></div>
      </div>
      <div class="__co" :style="{ width: (coins/spend) * 100 + '%'}">
        <div></div>
        <div>Coinsurance<br><b>{{ dollarString(coins, '$', 0) }}</b></div>
      </div>
      <!--            <div class="__other" :style="{ width: (other/spend) * 100 + '%'}">-->
      <!--              <div></div>-->
      <!--              <div>Other<br><b>{{ dollarString(other, '$', 0) }}</b></div>-->
      <!--            </div>-->
    </div>

    <div class="__single text-xs">
      <table>
        <tbody>
        <tr>
          <th></th>
          <th>Amount</th>
          <th>DPC Savings</th>
          <th>New Amount</th>
        </tr>
        <tr v-for="(cost, i) in costs" :key="`ind-${i}`">
          <td>{{ cost.name }}</td>
          <td>{{ dollarString(cost.costT, '$', 0) }}</td>
          <td>-{{ dollarString(cost.savingsT, '$', 0) }}</td>
          <td>{{ dollarString(cost.newCost, '$', 0) }}</td>
        </tr>
        <tr>
          <td>Out of pocket spend</td>
          <td>{{ dollarString(oop, '$', 0) }}</td>
          <td>-{{ dollarString(savings, '$', 0) }}</td>
          <td>{{ dollarString(oop - savings, '$', 0) }}</td>
        </tr>
        <tr>
          <td>DPC Cost</td>
          <td>{{ dollarString(0, '$', 0) }}</td>
          <td>-{{ dollarString(0, '$', 0) }}</td>
          <td>{{ dollarString(dpcCost, '$', 0) }}</td>
        </tr>
        <tr>
          <td>Total spend (ins. included)</td>
          <td>{{ dollarString(oop + usePremium, '$', 0) }}</td>
          <td>-{{ dollarString(savings, '$', 0) }}</td>
          <td>{{ dollarString(oop + usePremium - savings + dpcCost, '$', 0) }}</td>
        </tr>
        </tbody>
      </table>
    </div>

    <div class="_fw q-pt-lg q-pb-sm">
      <div class="row q-py-lg">
        <div class="__total __spend text-md tw-six">
          Net Savings: <span class="tw-eight text-primary">{{ dollarString(savings - dpcCost, '$', 0) }}</span>/mo
        </div>
      </div>
      <div class="tw-five text-xs q-pa-sm">Even continuing to pay for expensive major medical insurance, you save
        by using
        DPC if you are spending <b>{{ dollarString(parity, '$', 0) }}</b>/mo in out of pocket costs.
      </div>


    </div>
  </div>
</template>

<script setup>
  import MoneyInput from 'components/common/input/MoneyInput.vue';

  import {dollarString} from 'src/utils/global-methods';
  import {computed, onMounted, ref, watch} from 'vue';
  import {mapCalcs} from 'pages/landing/claim-map/calcs';

  const props = defineProps({
    prem: { default: 361 }
  })

  const optOut = ref(false);
  const premium = ref(361);
  const oopSpend = ref(435);

  const usePremium = computed(() => optOut.value ? 0 : premium.value);

  const spend = computed(() => usePremium.value + oopSpend.value);

  const { stats, setStat, initMap } = mapCalcs();

  const dpcCost = ref(90);
  const ded = computed(() => Math.max(Math.min(spend.value - usePremium.value, stats.value.ded / 12), 0));
  const coins = computed(() => Math.max(0, spend.value - usePremium.value - ded.value));
  const oop = computed(() => {
    return ded.value + coins.value;
  })

  watch(() => props.prem, (nv) => {
    if(typeof nv === 'number') usePremium.value = nv
  }, { immediate: true });

  const costs = computed(() => [
    {
      name: 'Office Visits',
      cost: .12,
      savings: 1
    },
    {
      name: 'Meds',
      cost: .21,
      savings: .62
    },
    {
      name: 'ER/Urgent Care',
      cost: .31,
      savings: .59
    },
    {
      name: 'Inpatient/Specialty',
      cost: .20,
      savings: .42
    },
    {
      name: 'Outpatient',
      cost: .16,
      savings: .34
    }
  ].map(a => {
    const costT = oop.value * a.cost;
    const savingsT = (oop.value * a.cost) * a.savings;
    return {
      ...a,
      costT,
      savingsT,
      newCost: costT - savingsT
    }
  }))

  const savings = computed(() => costs.value.reduce((acc, v) => acc + v.savingsT, 0))
  const savingsP = computed(() => costs.value.reduce((acc, v) => acc + v.savings, 0) / costs.value.length)

  const parity = computed(() => dpcCost.value / savingsP.value + 2)

  onMounted(() => initMap())
</script>

<style lang="scss" scoped>
  .__bar {
    margin: 60px 0;
    width: 100%;
    border-radius: 5px;
    height: 50px;
    //box-shadow: 0 2px 4px #999;
    display: flex;
    align-items: center;
    position: relative;


    .__ins, .__ded, .__co, .__other {
      position: relative;
      height: 100%;
      text-align: center;
      transition: all .4s;

      div {
        position: absolute;
        left: 50%;
        bottom: -7px;
        transform: translate(-50%, 100%);
        font-size: .9rem;
        font-weight: 500;
        text-wrap: nowrap;

        &:first-child {
          height: 4px;
          width: 2px;
          bottom: -3px;
          background: #999;
        }
      }
    }

    .__ins {
      background: var(--q-s10);
      border-radius: 5px 0 0 5px;
    }

    .__ded {
      background: var(--q-s7);

      div {
        bottom: 100%;
        transform: translate(-50%, -7px);

        &:first-child {
          height: 4px;
          bottom: 100%;
          transform: translate(-50%, -3px);
        }
      }
    }

    .__co {
      background: var(--q-secondary);
      border-radius: 0 5px 5px 0;
    }

    //.__other {
    //  background: var(--q-s3);
    //  border-radius: 0 5px 5px 0;
    //
    //  div {
    //    bottom: 100%;
    //    transform: translate(-50%, -7px);
    //
    //    &:first-child {
    //      height: 4px;
    //      bottom: 100%;
    //      transform: translate(-50%, -3px);
    //    }
    //  }
    //}
  }

  .__total {
    //background: linear-gradient(12deg, var(--q-s10), var(--q-secondary));
    //color: white;
    padding: 2px 10px;
    border-radius: 15px;
  }

  .__spend {
    //background: linear-gradient(12deg, var(--q-p6), var(--q-primary));
  }

  .__adj {

    table {
      width: 100%;
      border-collapse: collapse;

      tr {

        td {
          font-size: 1rem;
          font-weight: bold;
          padding: 2px 8px;
          border-bottom: solid .5px #999;

          &:first-child {
            width: 90%;
          }
        }
      }
    }
  }

  .__single {
    margin-top: 80px;
    width: 100%;
    border-collapse: collapse;

    table {
      width: 100%;
      border-collapse: collapse;

      tr {
        th {
          padding: 5px 8px;
          text-align: right;
          border-bottom: solid .5px #dedede;
        }

        td {
          padding: 8px;
          border-bottom: solid .5px #dedede;
          text-align: right;

          &:first-child {
            text-align: left;
          }

          &:nth-child(2) {
            font-weight: 500;
          }

          &:nth-child(3) {
            font-weight: 500;
            color: var(--q-s6);
          }

          &:last-child {
            font-weight: 500;
            color: var(--q-primary);
          }
        }

        &:nth-child(7) {
          td {
            border-bottom: solid 2px white;
            font-weight: 600;
            background: var(--q-ir-grey-1);
          }
        }

        &:nth-child(8) {
          td {
            border-bottom: solid 2px white;
            font-weight: 600;
            background: var(--q-ir-grey-2);
          }
        }

        &:last-child {
          td {
            font-weight: bold;
            background: var(--q-ir-grey-3);
          }
        }
      }
    }
  }


</style>
