<template>
  <div class="_fw text-sm">
    <div class="text-center text-sm tw-six q-pb-sm">Watch closely where the money comes from</div>
    <div class="row justify-center">
      <div class="__t text-lg text-center">
        <div>
          First, you sell
          <q-chip color="grey-2" class="tw-six text-lg main-font" clickable>
            <span class="q-mr-xs text-lg">{{ abbrMoney(amt, '$', 0) }}</span>
            <q-icon size="25px" name="mdi-menu-down"></q-icon>
            <q-menu>
              <q-list separator class="text-xs">
                <q-item clickable v-for="(a, i) in amounts" :key="`am-${i}`" @click="emit('update:amt', a)">
                  <q-item-section>
                    <q-item-label class="tw-six">{{ abbrMoney(a, '$', 0) }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-chip>
          of
          <q-chip clickable color="grey-2" class="text-lg tw-six __d flex items-center">
            <span class="q-mr-sm">{{ widget }}</span>
            <q-icon size="25px" name="mdi-menu-down"></q-icon>
            <q-menu>
              <q-list separator>
                <q-item clickable v-for="(w, i) in widgets" :key="`widget-${i}`" @click="emit('update:widget', w)">
                  <q-item-section>
                    <q-item-label class="tw-six text-p12 font-2r">{{ w }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-menu>
          </q-chip>
          's
        </div>
      </div>
    </div>
    <div class="row justify-center text-lg">
      <div class="__c __w">
        <table class="__w">
          <tr>
            <td>Labor budget %</td>
            <td class="__s">
              <div>{{ dollarString(pct, '', 0) }}%
                <q-icon name="mdi-menu-down" class="__i"></q-icon>
                <q-menu>
                  <q-list separator>
                    <q-item clickable v-for="(p, i) in pcts" :key="`pct-${i}`" @click="emit('update:pct', p)">
                      <q-item-section>
                        <q-item-label class="tw-six text-p12">{{ dollarString(p, '', 0) }}%</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>
              </div>
            </td>
          </tr>
          <tr>
            <td>Labor budget $</td>
            <td class="__s">
              <div>{{ abbrMoney(labor, '$', 0) }}
                <q-icon name="mdi-menu-down" class="__i"></q-icon>
                <q-menu>
                  <q-list separator>
                    <q-item clickable v-for="(p, i) in pcts" :key="`pct-${i}`" @click="emit('update:pct',p)">
                      <q-item-section>
                        <q-item-label class="tw-six text-p12">{{ abbrMoney((p / 100) * amt, '$', 0) }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>
              </div>
            </td>
          </tr>
          <tr class="__hp">
            <td>Budget for health plan</td>
            <td class="__s">
              <div>
                {{ abbrMoney(benCost, '$', 0) }}
                <q-icon class="__i" name="mdi-menu-down"></q-icon>
                <q-menu>
                  <q-list separator>
                    <q-item-label header>% of payroll</q-item-label>
                    <q-item clickable v-for="(p, i) in benPcts" :key="`bp-${i}`" @click="emit('update:ben', p)">
                      <q-item-section>
                        <q-item-label class="tw-six text-p12">{{ dollarString(p, '', 0) }}%</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>
              </div>
            </td>
          </tr>
          <tr class="__r">
            <td>Money left for payroll</td>
            <td>
              {{ abbrMoney(labor - benCost - .001, '$', 0) }}
            </td>
          </tr>
          <tr class="__sub text-sm">
            <td>Payroll Tax (employer)</td>
            <td>-{{ abbrMoney(prTax / 2, '$', 2) }}</td>
          </tr>
          <tr class="__sub text-sm">
            <td>Payroll Tax (employee)</td>
            <td>-{{ abbrMoney(prTax / 2, '$', 2) }}</td>
          </tr>
          <tr class="__r">
            <td>Money left for employees</td>
            <td>{{ abbrMoney(labor - benCost - .001 - prTax, '$', 2) }}</td>
          </tr>
          <tr class="__r __l">
            <td>Total labor budget <span class="text-xs">per {{widget}}</span></td>
            <td>
              {{ abbrMoney(labor, '$', 0) }}
            </td>
          </tr>
        </table>
      </div>
    </div>
    <div class="row justify-center q-px-sm">
      <div class="__c __w">
        <div class="text-md flex items-center">
          Did you notice something there? <br><br>The dollars for the employee's health plan don't come out of the tears of employers as the last ounce of generosity is squeezed from their greedy soul
          💧 <br><br><span class="tw-seven">They come out of the same bucket as employee take-home pay. More healthcare costs = less take home pay - it's 1:1.</span>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
  import {abbrMoney, dollarString} from 'src/utils/global-methods';
  import {computed} from 'vue';

  const amounts = [1, 100, 1000, 10000, 100000, 1000000, 10000000, 100000000, **********, **********0];
  const widgets = ['🔩', '🩺', '⚾', '🏗️', '🛩️', '🥕', '🐄', '🏎️', '🛒', '💣', '🍁', '🍼', '🪕']
  const pcts = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];
  const benPcts = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50];

  const emit = defineEmits(['update:amt', 'update:widget', 'update:pct', 'update:ben']);
  const props = defineProps({
    amt: Number,
    widget: String,
    pct: Number,
    ben: Number
  })

  const labor = computed(() => props.amt * (props.pct / 100))
  const benCost = computed(() => labor.value * (props.ben / 100))
  const prTax = computed(() => (labor.value - benCost.value) * .153)
</script>

<style lang="scss" scoped>
  .__c {
    padding: max(15px, 2vw) 0px;
    margin: 1vw;
    transform: none;
    transition: all .2s;

  }

  .__t {
    padding: 20px 35px;
    border-radius: 45px;
    border: solid 2px black;
    background: white;
    text-align: center;
  }



  .__i {
    transform: translate(0, -12%);
  }

  .__w {
    width: 100%;
    max-width: 1000px;
  }

  table {

    border-collapse: collapse;

    .__s {
      cursor: pointer;
      transform: none;
      box-shadow: none;
      transition: all .2s;
      text-wrap: nowrap;

      &:hover {
        transform: translate(0, -2px);
        color: var(--q-primary);
      }
    }

    td {
      font-weight: 600;
      padding: 10px 10px;
      //border-bottom: solid 3px #f0f0f0;
    }

    tr {
      td:last-child {
        text-align: right;
      }
    }

    .__sub {
      td {
        color: var(--q-a3);
      }


      td:first-child {
        padding-left: 35px;
      }
    }

    .__hp {
      td {
        color: var(--q-primary);
      }
    }

    .__hp, .__r {
      td:last-child {
        //font-weight: 600;
      }
    }

    .__r {
      td {
        color: var(--q-accent);
      }
    }

    .__l {
      td {
        //color: var(--q-secondary);
      }
    }
  }


</style>
