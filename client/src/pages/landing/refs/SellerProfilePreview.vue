<template>
  <div>
    <div class="__map_wrap">
      <div class="__map">
        <map-box @ready="panMap" :center="[-79.6603,35.9051]" :max-zoom="3" :zoom="3"
                 :markers-in="[[-79.6603,35.9051]]"></map-box>
      </div>

      <div class="__profile">
        <div class="row items-end">
          <div class="__pic">
            <q-img
                src="https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fref-invite%2Fcat.png?alt=media&token=491487e3-07d1-4d1b-85d8-19d8cb44e9bc"
                fit="cover" class="_fa"></q-img>
          </div>
          <div class="font-1-1-4r tw-six q-pa-sm">Catalina</div>
        </div>
        <div class="q-px-sm q-py-md">
          <div class="font-1r tw-five">
            Member of:
            <q-chip color="ir-bg2">
              <q-avatar>
                <img :src="getByCode('NC', 'icon')">
              </q-avatar>
              <span class="tw-six">Piedmont NC Fund</span>
            </q-chip>
          </div>
          <div class="font-1r tw-five">Helped <span class="tw-six text-primary alt-font">67</span> others join</div>
        </div>
        <div class="q-pa-sm __why">
          <div>
            <div class="__vid">
              <q-img class="_fa" fit="cover" src="https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fref-invite%2Fcat_thumbnail.png?alt=media&token=c35150db-9087-4e5e-9bd8-ca068b1afcfa"></q-img>
            </div>
          </div>
          <div class="q-pa-sm">

            <div class="font-7-8r tw-six text-ir-off">Why I joined CommonCare</div>

            <div class="_fw font-3-4r tw-five text-ir-deep q-py-xs">Once I realized we were paying $16,000/year for coverage that only made things worse, we had to make a change. Our last hospital bill was such a nightmare. We...</div>
          </div>


        </div>

        <div class="q-pa-sm">
          <div class="font-7-8r tw-six">About
            <q-chip color="ir-bg2">
              <q-avatar>
                <img :src="getByCode('NC', 'icon')">
              </q-avatar>
              <span class="tw-six _i_i">Piedmont NC Fund</span>
            </q-chip>
          </div>

          <q-list separator>
            <q-item>

              <q-item-section>
                <q-item-label class="font-1r tw-five">
                  <span class="font-3-4r tw-six">Medical Director:</span>&nbsp;Dr Tanner Moore <span class="font-3-4r">MD</span>
                </q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-avatar>
                  <img src="https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Ftanner.png?alt=media&token=7deb0cfe-4932-4527-89a7-76cdb8a52ec3">
                </q-avatar>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <q-item-label class="font-7-8r">Members</q-item-label>
              </q-item-section>
              <q-item-section side>
                <div class="alt-font tw-six text-primary">14,340</div>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <q-item-label class="font-7-8r">Providers</q-item-label>
              </q-item-section>
              <q-item-section side>
                <div class="alt-font tw-six text-secondary">231</div>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <q-item-label class="font-7-8r">Facilities</q-item-label>
              </q-item-section>
              <q-item-section side>
                <div class="alt-font tw-six text-accent">34</div>
              </q-item-section>
            </q-item>
          </q-list>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

  import MapBox from 'components/common/mapbox/map/MapBox.vue';
  import {getByCode} from 'components/common/geo/data/states';

  const panMap = (m) => {
    console.log('pan map');
    m.panBy([0, 50], { duration: 800 })
  }
</script>

<style lang="scss" scoped>
  .__map_wrap {
    position: relative;
    width: 100%;

    .__map {
      max-width: 100%;
      width: 400px;
      height: 200px;
      position: relative;
      overflow: hidden;
      border-radius: 10px;
      box-shadow: 4px 4px 25px var(--q-light);
    }

    .__profile {
      border-radius: 10px;
      position: relative;
      width: 95%;
      transform: translate(2.75%, -90px);
      z-index: 2;
      background: rgba(255, 255, 255, .85);
      padding: 25px 10px;
      box-shadow: 6px -6px 26px rgba(0,0,0,.1);

    }
    .__pic {
      height: 100px;
      width: 100px;
      border-radius: 50%;
      box-shadow: 0 0 0 4px white;
      overflow: hidden;
    }

    .__why {
      width: 100%;
      display: grid;
      grid-template-columns: 150px 1fr;
      align-items: center;
    }

    .__vid {
      width:150px;
      height: calc(150px * .57);
      overflow: hidden;
      border-radius: 5px;
    }
  }

</style>
