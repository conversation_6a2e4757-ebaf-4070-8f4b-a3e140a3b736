import {AnyObj} from 'src/utils/types';
import {getCmsPerson} from 'components/market/household/utils';
import {dollarString} from 'src/utils/global-methods';
import {isEmailRule} from 'stores/validate/validators';
import {isValid} from 'src/utils/date-utils';
import {getAge} from 'components/enrollments/ichra/utils/index';
import {computed, ref} from 'vue';

type CensusFieldIndexes = { [key: number | string]: string }
const censusFieldIndexes: CensusFieldIndexes = {
    0: 'name',
    1: 'firstName',
    2: 'lastName',
    3: 'age',
    4: 'married',
    5: 'deps',
    6: 'zip',
    7: 'wage',
    8: 'email',
    9: 'hours',
    10: 'hourly',
    11: 'role',
    12: 'w9'
}
const idxByKey = ({adders}:{adders?:Array<string>}) => {
    const obj = {};
    for (const k in censusFieldIndexes) {
        obj[censusFieldIndexes[k]] = k;
    }
    if(adders){
        const length = Object.keys(obj).length;
        for(let i = 0; i < adders.length; i++){
            obj[adders[i]] = length + i;
        }

    }
    return obj
}
export const censusIndexByKey = idxByKey({})

const parseDate = (val) => {
    // console.log('parse data', val, isValid(val))
    if (isValid(val)) return getAge(val);
    else return val;
}

export const getReqs = ({adders, exclude, required}:{exclude?: Array<string>,adders?:(args:{errs:any, csvData:any}) => Array<any>, required?:Array<string>}) => {
    const csvData = ref([[]])
    const errs = ref({});
    const oneName = ref(false);
    const reqName = ref(false);
    const defAdders = () => []
    const useAdders = computed(() => (adders || defAdders)({ errs:errs.value, csvData:csvData.value}))
    const indexByKey = computed(() => idxByKey({adders:useAdders.value.map(a => a.key)}))
    const fieldIndexes = computed(() => {
        const obj = {}
        for(const k in indexByKey.value){
            obj[indexByKey.value[k]] = k;
        }
        return obj
    });

    const reqs = computed(() => [
        {
            required: true,
            key: 'age',
            label: 'Age or DOB',
            tooltip: 'Enter age or dob as MM/DD/YYYY',
            ex: 41,
            def: -1,
            format: (val) => {
                return val;
            },
            rev: (val) => {
                // console.log('rev', val)
                let spl = [];
                if (typeof val === 'string') spl = val.split('/');
                if (spl.length > 2) {
                    return parseDate(val)
                } else {
                    const v = Number(val);
                    if (typeof v === 'number') {
                        return v
                    } else return val;
                }
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`
                if (typeof val !== 'number' || val < 0 || val > 120) {
                    errs.value[key] = 'Invalid Age'
                    return false
                }
                else {
                    delete errs.value[key]
                    return true
                }
            }
        },
        {
            required: true,
            key: 'wage',
            label: 'Annual Pay',
            tooltip: 'Annual if salary, hourly if hourly',
            ex: 85000,
            def: 0,
            format: (val) => {
                return dollarString(val, '$', 0, '')
            },
            rev: (val) => {
                if (!val && val !== 0) return undefined;
                if(typeof val === 'number') return val;
                return Number(val.replace(/[^\d.]/g, ''))
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`
                if (typeof val !== 'number') {
                    errs.value[key] = 'Invalid Income';
                    return false
                }
                else {
                    delete errs.value[key]
                    return true
                }
            }
        },
        {
            required: false,
            key: 'hourly',
            label: 'Hourly',
            tooltip: 'Is hourly worker',
            ex: 'Y',
            def: '',
            format: (val) => typeof val === 'string' ? val.charAt(0).toUpperCase() === 'Y' ? 'Y' : 'N' : 'N',
            rev: (val) => val ? 'Y' : 'N',
            check: () => {
                return true
            }
        },
        {
            required: false,
            key: 'hours',
            label: 'Weekly Hours',
            tooltip: 'If hourly - weekly hours',
            ex: 40,
            def: 40,
            format: (val, row) => {
                if(val) return Number(val || 0)
                if((csvData[row] || [])[indexByKey.value['wage']] > 2000) return 40;
                return 0;
            },
            rev: (val) => {
                if (!val && val !== 0) return undefined;
                return Number(val)
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`
                if((csvData.value[row] || [])[indexByKey.value['wage']] > 2000) {
                    delete errs.value[key];
                    return true
                }
                else if(typeof val === 'number') {
                    delete errs.value[key];
                    return true;
                }
                else if((csvData.value[row] || [])[indexByKey.value['hourly']]) {
                    delete errs.value[key];
                    return true;
                }
                else {
                    errs.value[key] = 'Invalid hours'
                    return true
                }
            }
        },
        {
            required: true,
            key: 'married',
            label: 'Married (Y/N)',
            tooltip: 'Just marital status, not whether spouse participates',
            ex: 'Y',
            def: 'N',
            format: (val) => typeof val === 'string' ? val.charAt(0).toUpperCase() === 'Y' ? 'Y' : 'N' : 'N',
            rev: (val) => {
                if (!val) return 'N'
                if (typeof val === 'string') {
                    const v = val.trim().slice(0, 1).toUpperCase()
                    if (['Y', 'N'].includes(v)) return v;
                }
                return ''
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if (typeof val === 'string') {
                    const v = val.trim().slice(0, 1).toUpperCase()
                    if (['Y', 'N'].includes(v)) {
                        delete errs.value[key];
                        return true
                    }
                }
                errs.value[key] = 'Enter "Y" or "N"'
                return false
            }
        },
        {
            required: true,
            key: 'deps',
            label: '# Dependents',
            tooltip: 'Dependents generally under 26, or disabled',
            ex: 2,
            def: 0,
            format: (val) => val,
            rev: (val) => {
                if (!val) return 0;
                return Number(val)
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if (typeof val !== 'number') {
                    errs.value[key] = 'Must be a number'
                    return true
                } else {
                    delete errs.value[key]
                    return true
                }
            }
        },
        {
            required: true,
            key: 'zip',
            label: 'Zip',
            tooltip: 'Employee home zip code',
            ex: '27283',
            def: '',
            format: (val) => val,
            rev: (val) => {
                const v = val ? String(val) : '';
                return v.replace(/\D/g, '');
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if (/^\d{5}$/.test(val)) {
                    delete errs.value[key];
                    return true
                }
                else {
                    errs.value[key] = 'Invalid Zip Code'
                    return false
                }
            }
        },
        {
            required: reqName.value && oneName.value,
            key: 'name',
            label: 'Name',
            tooltip: 'Employee First Name',
            ex: 'John',
            def: '',
            rev: (v, row) => {
                if(v){
                    const val = v.trim()
                    if(!csvData.value[row]){
                        csvData.value[row] = []
                        for(let i = 0; i < indexByKey.value['name']; i++){
                            csvData.value[row].push('')
                        }
                    }
                    let firstName;
                    let lastName;

                    if(val.includes(',')){
                        const spl = val.split(',').map(a => a.trim());
                        firstName = spl[1];
                        lastName = spl[0];
                    } else {
                        const spl = val.split(' ')
                        const last = spl.length - 1;
                        lastName = spl[last];
                        firstName = spl.slice(0, last).join(' ');
                    }
                    csvData.value[row][indexByKey.value['firstName']] = firstName;
                    csvData.value[row][indexByKey.value['lastName']] = lastName;
                }
                return v || ''
            },
            format: (val, row) => {
                const firstName = csvData.value[row][indexByKey.value['firstName']];
                const lastName = csvData.value[row][indexByKey.value['lastName']];
                if(firstName && lastName) return `${firstName} ${lastName}`;
                return firstName || lastName || '';
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if(typeof val !== 'string' || !val.length) {
                    errs.value[key] = 'Enter first name'
                    return false
                }
                else {
                    delete errs.value[key];
                    return true
                }
            }
        },
        {
            required: reqName.value && !oneName.value,
            key: 'firstName',
            label: 'First Name',
            tooltip: 'Employee First Name',
            ex: 'John',
            def: '',
            format: (val) => typeof val === 'string' ? val.trim() : '',
            rev: (val) => {
                if(!val) return ''
                return val;
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if(typeof val !== 'string' || !val.length) {
                    errs.value[key] = 'Enter first name'
                    return false
                }
                else {
                    delete errs.value[key];
                    return true
                }
            }
        },
        {
            required: reqName.value && !oneName.value,
            key: 'lastName',
            label: 'Last Name',
            tooltip: 'Employee Last Name',
            ex: 'Smith',
            def: '',
            format: (val) => typeof val === 'string' ? val.trim() : '',
            rev: (val) => {
                if(!val) return ''
                return val;
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if(typeof val !== 'string' || !val.length) {
                    errs.value[key] = 'Enter last name'
                    return false
                }
                else {
                    delete errs.value[key];
                    return true
                }
            }
        },
        {
            required: false,
            key: 'email',
            label: 'Email',
            tooltip: 'Email to send enrollment invite (when ready)',
            ex: '<EMAIL>',
            def: '',
            format: (val) => val?.trim().toLowerCase(),
            rev: (val) => {
                if(!val) return ''
                return val;
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if (isEmailRule(val)) {
                    delete errs.value[key];
                    return true
                }
                else {
                    errs.value[key] = 'Invalid Email'
                    return false
                }
            }
        },
        {
            required: false,
            key: 'role',
            label: 'Role',
            tooltip: 'Description of job role ie: "Customer Service"',
            ex: 'A/R Clerk',
            def: '',
            format: (val) => typeof val === 'string' ? val : '',
            rev: (val) => typeof val === 'string' ? val : '',
            check: () => {
                return true
            }
        },
        {
            required: false,
            key: 'w9',
            label: 'Contractor',
            tooltip: 'Is not a W2 employee',
            ex: 'Y',
            def: '',
            format: (val) => typeof val === 'string' ? val.charAt(0).toUpperCase() === 'Y' ? 'Y' : 'N' : 'N',
            rev: (val) => !!val,
            check: () => true
        },
        ...useAdders.value
    ].filter(a => !(exclude || []).includes(a.key)).map(a => (required || []).includes(a.key) ? {...a, required: true} : a).sort(((a, b) => indexByKey.value[a.key] - indexByKey.value[b.key])))

    const useErrs = computed(() => {
        const obj:any = {};
        for(const k in errs){
            const spl = k.split('-');
            if((csvData.value[spl[0]] || [])[spl[1]]) obj[k] = errs.value[k];
        }
        return obj;
    })
    return {
        fieldIndexes,
        oneName,
        errs,
        useErrs,
        reqs,
        csvData,
        reqName,
        indexByKey
    }
}


export const fromCensus = (val: Array<Array<string | number>>, ex: Array<AnyObj>, fieldIndexes:CensusFieldIndexes|undefined = censusFieldIndexes) => {
    if (!val?.length) return ex || []
    const conv: any = [];
    const exArr: any = ex || [];
    const t = new Date().getTime();
    for (let i = 0; i < val.length; i++) {
        const obj: any = exArr[i] || {};
        for (let idx = 0; idx < val[i].length; idx++) {
            obj[fieldIndexes[idx]] = val[i][idx];
        }
        if (!obj.uid) obj.uid = `${i}-${t}`
        obj.updatedAt = new Date();
        obj.hourly = obj.hourly === 'Y'
        if (obj.wage) {
            if (obj.wage > 2000) {
                obj.income = obj.wage;
                obj.hourly = false;
            } else {
                if (!obj.hours) obj.hours = 40;
                obj.income = obj.wage * obj.hours
            }
        } else if(obj.income){
            if(obj.hourly) obj.wage = obj.income / (Number(obj.hours) || 40)
            else obj.wage = obj.income;
        }
        conv.push(obj);
    }
    return conv;
}

export const toCensus = (ex: Array<AnyObj>, reqs:Array<any>, fieldIndexes:CensusFieldIndexes|undefined = censusFieldIndexes) => {
    if (!ex?.length) return [];
    const census: any = [];
    const keys = Object.keys(fieldIndexes).sort((a, b) => Number(a) - Number(b));
    for (let i = 0; i < ex.length; i++) {
        const arr: any = []
        const obj = ex[i] || {};
        for (let idx = 0; idx < keys.length; idx++) {
            let v = obj[fieldIndexes[idx]];
            if (!v && v !== 0) v = ''
            arr.push(reqs[idx].rev(v));
        }
        census.push(arr);
    }
    return census;
}

type EstimateIncomeOptions = {
    income: number,
    married: boolean
}

const twoIncomeOdds = .53
export const estimateHouseholdIncome = ({income, married}: EstimateIncomeOptions) => {
    if (!married) return income;
    const run = Math.random() <= twoIncomeOdds;
    if (!run) return income;
    return income * 2;
}

type GenerateHHOptions = {
    age: number,
    married: boolean,
    deps: number,
    smoker?: boolean
}
export const generateHh = ({age, married, deps, smoker}: GenerateHHOptions) => {
    const people = [getCmsPerson(age, false, !!smoker)]
    if (married) people.push(getCmsPerson(age, false, !!smoker))
    for (let i = 0; i < deps; i++) {
        people.push(getCmsPerson(10, true, false))
    }
    return people;
}
