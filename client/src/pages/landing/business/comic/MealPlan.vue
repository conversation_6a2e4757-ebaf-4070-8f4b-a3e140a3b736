<template>
  <div class="__meal_plan">
    <div id="meal_plan">
      <div class="__scenes">
        <div class="__scene" v-for="(scene, i) in scenes" :key="`scene-${i}`">
          <q-img class="_fa" fit="contain" :src="scene"></q-img>
        </div>
      </div>
    </div>
    <div class="__l">
      <q-btn @click="scroll(-1)" dense flat icon="mdi-chevron-left" color="white"></q-btn>
    </div>
    <div class="__r">
      <q-btn @click="scroll(1)" dense flat icon="mdi-chevron-right" color="white"></q-btn>
    </div>
  </div>
</template>

<script setup>

  const scenes = [
     'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene1.svg?alt=media&token=e17ae457-6841-4072-aa4f-f31853d08ba4',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene2.svg?alt=media&token=1f3a295e-7f68-4755-83be-664ad7ea2844',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene3.svg?alt=media&token=dbd4e3ad-4a41-4c71-bfa5-7cf29a75c410',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene4.png?alt=media&token=eadb4aad-3987-4d6a-bdec-79f4f75a78f0',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene5.svg?alt=media&token=a65fd0c0-1534-4634-a973-a6cf3eeaaab8',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene6.svg?alt=media&token=70c9fee9-be67-4246-bf48-54d1efeaecff',
      'https://firebasestorage.googleapis.com/v0/b/boxfin-1d426.appspot.com/o/commoncare%2Fjan_dan%2Fcompany-meal-plan%2Fscene7.svg?alt=media&token=e42a94aa-6c3b-4e4a-8349-dd3cc1d78c25'
  ]

  const scroll = (v) => {
    const el = document.querySelector('#meal_plan');
    if(el){
      el.scrollTo({
        left: el.scrollLeft + (v * Math.min(window.innerWidth * .94, 600)),
        behavior: 'smooth'
      });
    }
  }
</script>

<style lang="scss" scoped>
  .__meal_plan {
    width: 100%;
    position: relative;
    overflow-x: hidden;
    border-radius: 10px;
    border: solid 10px var(--q-a1);
    background: var(--q-a1);

    > div:first-child {
      position: relative;
      width: 100%;
      overflow-x: scroll;


      .__scenes {
        width: auto;
        display: grid;
        grid-template-rows: min(94vw, 450px);
        grid-template-columns: repeat(7, min(600px, 94vw));
        grid-gap: 10px;

        .__scene {
          width: 100%;
          height: 100%;
          transition: all .4s;
          background: linear-gradient(170deg, var(--q-a1), white);

        }

      }
    }
    .__l, .__r {
      position: absolute;
      top: 50%;
      transform: translate(0, -50%);
      background: rgba(0,0,0,.5);
      display: grid;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      cursor: pointer;
    }

    .__l {
      left: 10px;
    }
    .__r {
      right: 10px;
    }
  }
</style>
