<template>

  <div class="_fw text-a10">
    <div class="__st _hov __legal" @pointerenter="setHov('legal')" @mouseleave="setHov('')">
      <div class="row">
        <div>
          <div>
            <q-icon class="__icon" name="mdi-gavel"/>
            Plan Legal Structure
            <q-icon class="__info" name="mdi-information"></q-icon>
          </div>

        </div>
      </div>
      <div v-bind="getBinding('legal')">
        <div v-html="items.legal.preview" class="q-pt-lg"></div>
      </div>

    </div>
  </div>

  <div class="__structure">
    <div>

      <div class="__st _hov __ermt" @pointerenter="setHov('ermt')" @mouseleave="setHov('')">
        <div>
          <q-icon class="__icon __flip" name="mdi-directions-fork"/>
          Enrollment/Payroll Elections
          <q-icon class="__info" name="mdi-information"></q-icon>
        </div>
        <div v-html="items.ermt.preview" v-bind="getBinding('ermt')"></div>

      </div>

      <div class="__st _hov __wallet" @pointerenter="setHov('wallet')" @mouseleave="setHov('')">
        <div class="row items-center">
          <ai-logo opaque size="30px"></ai-logo>
          <div class="q-ml-sm">Care Wallet
            <q-icon class="__info" name="mdi-information"></q-icon>
          </div>
        </div>
        <div v-html="items.wallet.preview" v-bind="getBinding('wallet')"></div>

      </div>

      <div class="row q-pt-xl">
        <div class="col-12 q-pb-md">
          <div class="__care">
            <div>Healthcare</div>
            <div class="__sub _hov" @pointerenter="setHov('direct_care')" @mouseleave="setHov('')">
              <div>
                <q-icon class="__icon" name="mdi-stethoscope"/>
                Direct Care Contracts
                <q-icon class="__info" name="mdi-information"></q-icon>
              </div>
              <div v-html="items.direct_care.preview" v-bind="getBinding('direct_care')"></div>

            </div>
            <div class="__sub _hov" @pointerenter="setHov('coverage')" @mouseleave="setHov('')">
              <div>
                <q-icon class="__icon" name="mdi-umbrella"/>
                Coverages
                <q-icon class="__info" name="mdi-information"></q-icon>
              </div>
              <div v-html="items.coverage.preview" v-bind="getBinding('coverage')"></div>

            </div>
          </div>
        </div>
        <div class="col-12 q-pt-xl q-pb-md">

          <div class="__funds">
            <div>Cash Flow</div>
            <div class="__sub _hov" @pointerenter="setHov('savings')" @mouseleave="setHov('')">
              <div>
                <q-icon class="__icon" name="mdi-bank"/>
                Tax-Free Savings
                <q-icon class="__info" name="mdi-information"></q-icon>
              </div>
              <div v-html="items.savings.preview" v-bind="getBinding('savings')"></div>
            </div>
            <div class="__sub _hov" @pointerenter="setHov('take_home')" @mouseleave="setHov('')">
              <div>
                <q-icon class="__icon" name="mdi-home"/>
                Take Home
                <q-icon class="__info" name="mdi-information"></q-icon>
              </div>
              <div v-html="items.take_home.preview" v-bind="getBinding('take_home')"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup>
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import {ref} from 'vue';

  const hov = ref('')
  const items = {
    'legal': {
      preview: 'You may not have thought of it - but a big reason you\'re stuck with insurance carriers running your plan is: they provide the administrative platform. We break that off for you - and suddenly insurance is just an option in your plan...'
    },
    'ermt': {
      preview: 'There are a LOT of great healthcare options for your people to elect - but offering a lot of them side-by-side is nearly impossible administratively... unless you have CommonCare and it\'s as easy as point and click. Open architecture has finally arrived for health plans...'
    },
    'wallet': {
      preview: 'Setup an endless number of benefits for employees to select from - and the money moves seamlessly. No more need to trap employer contributions in a coverage, HRA (or ICHRA), or any other option. You provide the funds, we handle moving the money, tax classification, and reporting...'
    },
    'direct_care': {
      preview: 'Direct Care through CommonCare Direct gives your people a company doctor - who manages their health directly. 80% of your actual needs can be met through this arrangement - plus insanely inexpensive meds, labs, and supplies. This is how you push insurance to the background...'
    },
    'coverage': {
      preview: 'Besides having hundreds of choices for individuals to choose major medical coverage, we can also help you setup fully self-funded medical expense plans with little or no risk or cost (because they\'re employee funded), and big tax savings...'
    },
    'savings': {
      preview: 'HSA is must for every business to offer. The payroll tax savings (15.3%) is a big deal, and only available in an employer sponsored plan - plus employers have a superpower to enable people to qualify for an HSA with simple self-funded HDHP arrangements...'
    },
    'take_home': {
      preview: 'If your plan options are fantastic, there is no reason to implement use-it-or-lose-it structure ever - unless of course your advisors commission depends on it. Anyone can take their plan funds home if they are willing to cover the taxes...'
    }
  }

  const getBinding = (key) => {
    return {
      class: `__preview ${hov.value !== key ? '__off' : ''}`,
    }
  }

  const setHov = (val) => {
    if (!val) return hov.value = val;
    setTimeout(() => {
      hov.value = val;
    }, 20)
  }

</script>

<style lang="scss" scoped>
  .__info {
    margin-left: 8px;
    transform: translate(0, -2px);
    font-size: var(--text-xs);
  }

  .__icon {
    margin-right: 8px;
    transform: translate(0, -2px);
    font-size: var(--text-md);
  }

  .__flip {
    transform: rotate(180deg);
  }

  .__st, .__sub {
    font-size: var(--text-md);
    font-weight: 500;
    cursor: pointer;
  }

  .__sub {
    padding: 20px;
  }

  .__legal {
    color: var(--ir-deep);

    > div:first-child {
      height: 100%;

      > div {
        color: white;
        border-radius: 25px;
        background: var(--q-p4);
        height: 100%;
        padding: 0 10px;
        transform: translate(0, 50%);
      }
    }
  }

  .__wallet, .__ermt {
    > div:first-child {
      padding: 20px max(20px, 2vw);
    }

  }

  .__wallet {
    color: var(--q-p10);
    margin: 30px 0;
    border-radius: 10px;
    //background: linear-gradient(115deg, var(--q-p1), white);
    background: repeating-linear-gradient(
            45deg,
            var(--q-p1) 0px,
            var(--q-p1) 2px,
            transparent 2px,
            transparent 8px
    )
  }

  .__care, .__funds {
    padding: 40px 20px;
    position: relative;
    border-radius: 10px;

    > div {
      &:first-child {
        position: absolute;
        top: 0;
        left: 5%;
        transform: translate(0, -30%);
        font-size: var(--text-sm);
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 8px;
      }
    }
  }

  .__care {
    color: var(--q-s10);
    background: linear-gradient(180deg, var(--q-s0) 20%, transparent), repeating-linear-gradient(
            45deg,
            var(--q-s0) 0px,
            var(--q-s0) 2px,
            transparent 2px,
            transparent 9px
    );

    > div {
      &:first-child {
        background: linear-gradient(180deg, var(--q-s1), transparent);
      }
    }
  }

  .__funds {
    color: var(--q-a10);
    background: linear-gradient(0deg, var(--q-a1) 20%, transparent), repeating-linear-gradient(
            -45deg,
            var(--q-a1) 0px,
            var(--q-a1) 2px,
            transparent 2px,
            transparent 9px
    );

    > div {
      &:first-child {
        background: linear-gradient(180deg, var(--q-a1), transparent);
      }
    }
  }


  .__structure {
    color: var(--ir-deep);
    width: 100%;
    border-radius: 0 15px 15px 15px;
    background: linear-gradient(180deg, var(--q-p3), var(--q-s3), var(--q-a3));
    padding: 2px;

    > div {
      width: 100%;
      background: white;
      padding: 40px max(10px, 1vw);
      border-radius: inherit;
    }
  }

  .__preview {
    padding: 15px max(2.1vw, 20px) 20px max(2.1vw, 20px);
    font-size: var(--text-xs);
    font-weight: 400;
    transition: all .35s;
    transition-delay: .1s;
    max-height: 500px;
  }

  .__off {
    height: 0;
    max-height: 0;
    overflow: hidden;
    padding: 0 max(2.1vw, 20px) !important;
    opacity: 0;
  }

</style>
