<template>
  <div class="_fw cursor-pointer" @click="menu = !menu">
    <div class="row justify-around items-center font-7-8r tw-six">
      <div class="col-shrink q-pa-sm" v-for="(item, i) in menuItems.filter(a => a.show)" :key="`mi-${i}`">
        <div class="row justify-center">
          <div>
            <div>{{ item.label }}</div>
            <div class="font-1-1-4r text-sm tw-six">
              {{ Object.keys(item.attrs || {}).includes('prefix') ? item.attrs.prefix : '$' }}
              {{ dollarString(item.mv, '', 0) }} {{ item.attrs?.suffix || '' }}
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
  <common-dialog v-model="menu" setting="right">
    <div class="_fw bg-white q-pa-sm">
      <div class="q-pa-md tw-six text-xs">Plan Details</div>
      <div class="_form_grid">
        <template v-for="(item, i) in menuItems" :key="`mi-${i}`">
          <div class="_form_label">{{ item.label }}</div>
          <div class="q-pa-sm">
            <money-input
                v-bind="{
                    filled: true,
                    dense: true,
                    modelValue: item.mv,
                   ...item.attrs,
                      }"
                @update:model-value="item.handler"
            ></money-input>
          </div>
        </template>
      </div>

    </div>
  </common-dialog>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';

  import {dollarString} from 'src/utils/global-methods';
  import {computed, ref} from 'vue';

  const props = defineProps({
    setCount: Function,
    setStat: Function,
    perEeWage: Number,
    familyDed: Number,
    perEe: Number,
    modelValue: {
      default: () => {
        return {}
      }
    }
  })

  const menu = ref(false);

  const menuItems = computed(() => [
    {
      label: 'Employees',
      handler: (val) => props.setCount(val),
      mv: props.modelValue.count,
      show: true,
      attrs: {
        prefix: ''
      }
    },
    {
      label: 'Participants',
      handler: (val) => props.setStat('pCount', val),
      mv: props.modelValue.pCount,
      show: true,
      attrs: {
        prefix: ''
      }
    },
    {
      label: 'Avg Ann Wage',
      handler: (val) => props.setStat('avgWage', val),
      mv: props.perEeWage,
      show: true
    },
    {
      label: 'Avg Ann Spend',
      handler: (val) => props.setStat('avgSpend', val),
      mv: props.perEe,
      show: true
    },
    {
      label: 'Deductible',
      handler: (val) => props.setStat('ded', val),
      mv: props.modelValue.ded,
      show: true
    },
    {
      label: 'Family Deductible',
      handler: (val) => props.setStat('fDed', val),
      mv: props.familyDed
    },
    {
      label: 'Coinsurance',
      handler: (val) => props.setStat('coins', val / 100),
      mv: (props.modelValue.coins || 0) * 100,
      attrs: {
        suffix: '%',
        prefix: ''
      }
    },
    {
      label: 'MOOP',
      handler: (val) => props.setStat('moop', val),
      mv: props.modelValue.moop,
      show: true
    },
    {
      label: 'Family MOOP',
      handler: (val) => props.setStat('fMoop', val),
      mv: props.modelValue.fMoop
    }
  ])
</script>

<style lang="scss" scoped>

</style>
