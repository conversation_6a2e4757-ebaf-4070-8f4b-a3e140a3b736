<template>
  <div class="row justify-center pd6 pw2 __wti _oh">
    <div class="_cent relative-position">



      <div class="_fw relative-position z1 q-py-lg">
        <div class="text-sm tw-six text-center">What <span class="text-accent">we</span> do first:</div>
        <div class="text-center tw-six text-xl _l1-2 alt-font">Identify the obvious leaks</div>

        <div class="row q-py-lg relative-position">
          <div class="__blob"></div>
          <div class="__blob2"></div>
          <div class="col-12 col-md-4 q-pa-sm" v-for="(item, i) in items" :key="`item-${i}`">
            <div class="__c relative-position z2">
              <div class="tw-six font-1-1-2r">{{item.label}}</div>
              <div class="font-1-1-8r q-pt-sm">{{item.text}}</div>
            </div>
          </div>
        </div>
        <div class="row" >
          <div class="col-12 col-md-4 q-pa-sm">
          </div>
          <div class="col-12 col-md-8 q-pa-sm">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

  import {computed} from 'vue';

  const items = computed(() => {
    return [
      {
        label: 'Billing fraud, waste, and abuse',
        text: '15-30% of your billing line items can simply be dropped. We find and correct these - saving you costs and adjusting your actuarial history.'
      },
      {
        label: 'Price transparency files',
        text: 'Providers who post their legally required price files also have to hold to those published prices - many of your bills do not, an easy fix.'
      },
      {
        label: 'Inflated drug costs',
        text: 'Your people know very little about sourcing drugs and will overpay for years - bloating plan spend and out of pocket expenses.'
      },
      {
        label: 'Lab, Test, & Clinic Fees',
        text: 'Like meds - tests, scans, and labs are often loosely controlled by networks and over 1000% of cash prices. Cutting these down is turn-key.'
      },
      {
        label: 'Coordination of Benefits',
        text: 'Double coverage through DPC, spouse plans, Medicare, and more often have major coding miscues. Correcting these can erase significant costs.'
      },
      {
        label: 'Custom Negotiation',
        text: 'Our physician led - legal supported team knows when to leverage and when to just ask nicely. We can negotiate long term favorable rates.'
      }
    ]
  })
</script>

<style lang="scss" scoped>
  .__wti {
    //background: linear-gradient(168deg, var(--q-a1), var(--q-a2));
  }
  .__c {
    padding: 5vh min(4vw, 50px);
    border-radius: 15px;
    background: white;
    position: relative;
    overflow: hidden;
    //box-shadow: 0 4px 9px -4px #999;
  }

  .__blob {
    position: absolute;
    z-index: 0;
    width: 130%;
    height: 130%;
    border-radius: 50%;
    opacity: 1;
    background: radial-gradient(var(--q-a3) -50%, transparent 50%);
    transform: translate(5%, 0);
    animation: roam 20s infinite;
    top: -20%;
    left: -20%;
  }

  .__blob2 {
    position: absolute;
    z-index: 0;
    width: 90%;
    height: 90%;
    border-radius: 50%;
    opacity: 1;
    background: radial-gradient(var(--q-s3) -50%, transparent 50%);
    transform: translate(5%, 0);
    animation: roam 20s infinite;
    bottom: -10%;
    right: -10%;
  }
</style>
