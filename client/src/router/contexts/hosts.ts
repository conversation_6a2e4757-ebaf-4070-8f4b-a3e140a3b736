import {commonRoutes} from '../utils/common-routes';

export const hostRoutes = (orgId: string, hostId?: string) => {
    // const hostNamespace = `hosts:${hostId}`
    return [
        {
            path: '/',
            component: () => import('src/layouts/HostLayout.vue'),
            children: [
                {
                    path: '/contracts',
                    meta: {
                        category: 'contracts',
                        ucan: true
                    },
                    component: () => import('src/components/contracts/pages/ContractsPage.vue'),
                    children: [
                        {
                            path: '',
                            meta: {
                                name: 'contracts'
                            },
                            name: 'contracts',
                            component: () => import('src/components/contracts/pages/HostContracts.vue'),
                        },
                        {
                            path: 'edit/:type/:contractId?',
                            name: 'edit-contract',
                            meta: {
                                name: 'edit-contract',
                                sub: 'edit'
                            },
                            component: () => import('src/components/hosts/pages/HostContractBuilder.vue')
                        },
                        {
                            path: 'templates',
                            name: 'contract-templates',
                            meta: {
                                name: 'contract-templates'
                            },
                            component: () => import('src/components/contracts/pages/AddContract.vue')
                        },
                    ]
                },
                ...[
                    {
                        path: '',
                        name: 'host-home',
                        component: () => import('src/components/hosts/pages/HostAdmin.vue'),
                    },
                    {
                        path: '/enrollments',
                        meta: {category: 'enrollments'},
                        component: () => import('src/components/hosts/pages/HostEnrollments.vue'),
                        children: [
                            {
                                path: '',
                                name: 'host-shops',
                                meta: {name: 'shops'},
                                component: () => import('src/components/hosts/enrollments/shops/pages/HostShops.vue'),
                            },
                            {
                                path: 'shops/:shopId?',
                                name: 'host-shop',
                                meta: {name: 'shops'},
                                component: () => import('src/components/hosts/enrollments/shops/pages/HostShops.vue'),
                            },
                            {
                                path: 'group',
                                meta: {name: 'plan-shops'},
                                component: () => import('src/components/hosts/enrollments/HostPlanEnrollments.vue'),
                                children: [
                                    {
                                        path: '',
                                        name: 'host-enrollments',
                                        meta: {sub: 'enrollments'},
                                        component: () => import('src/components/hosts/enrollments/GroupEnrollments.vue')
                                    },
                                    {
                                        path: 'enrollments/:enrollmentId?',
                                        name: 'host-plan-enrollments',
                                        meta: {sub: 'enrollments'},
                                        component: () => import('src/components/hosts/enrollments/GroupEnrollments.vue')
                                    },
                                    {
                                        path: 'shop/:shopId?',
                                        name: 'host-plan-shops',
                                        meta: {sub: 'shops'},
                                        component: () => import('src/components/hosts/enrollments/shops/pages/HostPlanShops.vue')
                                    }
                                ]
                            },
                        ]
                    },
                    {
                        path: '/chat',
                        component: () => import('src/components/hosts/chat/HostChat.vue'),
                        meta: {category: 'chat'},
                        children: [
                            {
                                path: '',
                                name: 'host-chat',
                                component: () => import('src/components/hosts/chat/HostChats.vue'),
                                meta: {name: 'host-chat'}
                            }
                        ]
                    },
                    {
                        path: '/teams',
                        component: () => import('src/components/hosts/team/HostTeams.vue'),
                        meta: {category: 'teams'},
                        children: [
                            {
                                path: '',
                                name: 'host-teams',
                                component: () => import('src/components/hosts/team/AllTeams.vue'),
                                meta: {name: 'all'}
                            },
                            {
                                path: 'team/:teamId/:tab?',
                                name: 'host-team',
                                component: () => import('src/components/hosts/team/HostTeam.vue'),
                                meta: {name: 'team'}
                            },
                            {
                                path: 'people',
                                name: 'host-people',
                                component: () => import('src/components/hosts/team/refs/HostRefs.vue'),
                                meta: {name: 'host-people'}
                            },
                            {
                                path: 'settings',
                                name: 'teams-settings',
                                component: () => import('src/components/hosts/settings/TeamsSettings.vue'),
                                meta: {name: 'teams-settings'}
                            },
                            {
                                path: 'calendars',
                                name: 'host-calendars',
                                component: () => import('src/components/hosts/settings/HostCalendars.vue'),
                                meta: {name: 'host-calendars'}
                            },
                            {
                                path: 'permissions',
                                name: 'host-permissions',
                                component: () => import('src/components/hosts/settings/HostPermissions.vue'),
                                meta: {name: 'host-permissions'}
                            }
                        ]
                    },
                    {
                        path: '/plans',
                        meta: {category: 'host-plans'},
                        component: () => import('src/components/hosts/plans/pages/HostPlans.vue'),
                        children: [
                            {
                                path: '',
                                name: 'host-plans',
                                meta: {name: 'host-plans'},
                                component: () => import('src/components/hosts/plans/pages/ActivePlans.vue'),
                            },
                            {
                                path: '/offers/:offerId?',
                                name: 'host-offers',
                                meta: {name: 'host-offers'},
                                component: () => import('src/components/hosts/plans/offers/HostOffers.vue'),
                            },
                            {
                                path: '/opportunities',
                                name: 'host-opps',
                                meta: {name: 'host-opps'},
                                component: () => import('src/components/hosts/plans/pages/HostOpps.vue'),
                            }
                        ]
                    },
                    {
                        path: '/settings',
                        meta: {
                            category: 'settings',
                            ucan: {
                                requiredCapabilities: [[`orgs:${orgId}`, ['orgAdmin']], [`orgs:${orgId}`, ['WRITE']], ['orgs', 'WRITE'], [`hosts:${hostId}`, 'hostAdmin']],
                                or: true,
                            }
                        },
                        props: {
                            modelValue: orgId,
                            addedPaths: [
                                {label: 'DBA', name: 'host-dba'},
                                {label: 'Area', name: 'host-service-area'},
                                {label: 'Misc', name: 'misc-settings'}
                            ]
                        },
                        component: () => import('src/components/orgs/pages/OrgDetails.vue'),
                        children: [
                            {
                                path: '',
                                name: 'org-settings',
                                meta: {name: 'org-info'},
                                component: () => import('src/components/orgs/forms/OrgInfo.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/control',
                                name: 'org-control',
                                meta: {name: 'org-control'},

                                component: () => import('src/components/orgs/control/OrgControl.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/ownership',
                                name: 'org-ownership',
                                meta: {name: 'org-ownership'},

                                component: () => import('src/components/orgs/owners/OrgOwnership.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/info',
                                name: 'org-info',
                                meta: {name: 'org-info'},

                                component: () => import('src/components/orgs/forms/OrgInfo.vue'),
                                props: {
                                    modelValue: orgId
                                }
                            },
                            {
                                path: '/dba',
                                name: 'host-dba',
                                meta: {name: 'host-dba'},
                                component: () => import('src/components/hosts/dba/HostDba.vue'),
                            },
                            {
                                path: '/area',
                                name: 'host-service-area',
                                meta: {name: 'host-service-area'},
                                component: () => import('src/components/hosts/forms/HostArea.vue')
                            },
                            {
                                path: '/misc',
                                name: 'misc-settings',
                                meta: {name: 'misc-settings'},
                                component: () => import('src/components/hosts/settings/MiscSettings.vue')
                            }
                        ]
                    }
                ].map((a: any) => {
                    return {
                        ...a,
                        meta: {
                            ucan: !hostId ? true : {
                                requiredCapabilities: [[`hosts:${hostId}`, ['hostAdmin']], [`hostId:${hostId}`, ['WRITE']], ['hosts', 'WRITE']],
                                or: true
                            },
                            ...a.meta,
                        }
                    }
                }),
                ...commonRoutes(),

            ]
        }
    ]
}
