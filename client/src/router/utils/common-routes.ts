
export const formBuilderRoutes = () => [
    {
        path: 'form-hub',
        name: 'form-hub',
        component: () => import('src/components/form-builder/pages/FormHub.vue'),
        meta: { ucan: true }
    },
    {
        path: 'form-builder/:formId?',
        name: 'form-builder',
        component: () => import('src/components/form-builder/pages/FormBuilderWrapper.vue'),
        meta: { ucan: true }
    },
    {
        path: 'form/:formId',
        name: 'form-viewer',
        component: () => import('src/components/form-builder/pages/FormPage.vue')
    },
    {
        path: 'form-results/:formId',
        name: 'form-results',
        component: () => import('src/components/form-builder/cards/Responses.vue'),
        meta: { ucan: true }
    },
]

export const commonRoutes = () => [
    {
        path: '/profile/:tab?',
        name: 'my-profile',
        meta: {
            category: 'profile',
            name: 'profile',
        },
        component: () => import('src/components/common/profile/MultiProfile.vue')
    },
    {
        path: '/login/:action?',
        name: 'login',
        component: () => import('src/components/auth/pages/LoginPage.vue')
    },
    {
        path: '/oauth',
        name: 'oauth',
        component: () => import('src/components/auth/pages/OauthLanding.vue')
    },
    {
        path: '/add-bill',
        name: 'quick-claim',
        meta: {
            category: 'care'
        },
        component: () => import('src/components/care/pages/QuickClaim.vue')
    },
    {
        path: '/price-check',
        component: () => import('src/components/bill-collective/pages/BillCollective.vue')
    },
    {
        path: '/bill-eraser',
        component: () => import('src/components/bill-collective/pages/EraserLanding.vue'),
        children: [
            {
                path: '',
                name: 'bill-eraser',
                component: () => import('src/components/bill-collective/pages/BillEraser.vue'),

            },
            {
                path: ':session',
                name: 'bill-eraser-session',
                component: () => import('src/components/bill-collective/pages/EraserSession.vue')
            }
        ]
    },
    {
        path: '/rx/:sessionId?',
        component: () => import('src/components/bill-collective/rx/pages/RxLanding.vue'),
        children: [
            {
                path: '',
                name: 'rx',
                component: () => import('src/components/bill-collective/rx/pages/CommonRx.vue'),
            }
        ]
    },
    {
        path: '/common-funds/:state?',
        component: () => import('src/components/common-funds/pages/CommonFundsLanding.vue'),
    },
    {
        path: '/connect/:hostId?',
        name: 'add-ref',
        component: () => import('src/pages/landing/refs/RefInvite.vue')
    },
    {
        path: '/join-team/:planId/:role',
        component: () => import('src/components/plans/team/pages/JoinTeam.vue')
    },
    {
        path: '/plan-offer/:offerId',
        name: 'plan-offer',
        component: () => import('src/components/hosts/offers/pages/OfferPage.vue')
    },
    {
        path: '/file-preview/:id?',
        name: 'file-preview',
        component: () => import('src/components/common/uploads/pages/FilePreview.vue')
    },
    {
        path: '/categories',
        name: 'cats',
        meta: {
            name: 'cats',
            category: 'cats'
        },
        component: () => import('src/components/cats/pages/CatsPage.vue'),
    },
    {
        path: '/networks',
        component: () => import('src/components/networks/pages/NetworksPage.vue'),
        meta: {
            category: 'networks'
        },
        children: [
            {
                path: '',
                name: 'networks',
                meta: {
                    name: 'networks',
                },
                component: () => import('src/components/networks/pages/NetworkSearch.vue'),
            },
            {
                path: 'manage/:networkId?',
                name: 'network-manage',
                meta: {name: 'manage', category: 'networks', sub: 'manage', ucan: true},
                component: () => import('src/components/networks/pages/NetworkManage.vue')
            },
            {
                path: 'invites/:networkId?',
                name: 'network-invites',
                meta: {name: 'manage', category: 'networks', sub: 'invites', ucan: true},
                component: () => import('src/components/networks/pages/NetworkInvites.vue')
            },
            {
                path: 'reqs/:networkId?',
                name: 'network-reqs',
                meta: {name: 'manage', sub: 'reqs', category: 'networks', ucan: true},
                component: () => import('src/components/networks/pages/NetworkManage.vue')
            },
            {
                path: 'network/:networkId',
                component: () => import('src/components/networks/pages/NetworkPage.vue'),
                meta: {
                    name: 'networks',
                },
                children: [
                    {
                        path: '',
                        name: 'network-page',
                        meta: {
                            sub: 'bundles'
                        },
                        component: () => import('src/components/networks/features/NetworkPricebook.vue'),
                    },
                    {
                        path: 'plans',
                        name: 'network-plans',
                        meta: {
                            sub: 'plans'
                        },
                        component: () => import('src/components/networks/features/NetworkPlans.vue'),
                    }
                ]
            }
        ].map((a: any) => {
            return {
                ...a,
                meta: {
                    ...a.meta,
                    category: 'networks'
                }
            }
        })
    },
    {
       path: '/compare/:gpsId?',
       name: 'compare',
       component: () => import('src/components/compare/pages/CompareLanding.vue')
    },
    {
        path: '/transparency/:gpsId/:gppsId?',
        name: 'compare-share',
        component: () => import('src/components/compare/pages/CompareShare.vue'),
        meta: {
            toolDark: true,
            toolColor: 'linear-gradient(90deg, var(--q-a12) 20%, var(--q-a7))'
        }
    },
    {
        path: '/oauth',
        name:
            'oauth',
        component:
            () => import('src/components/auth/pages/OauthLanding.vue')
    },
    {
        path: '/login/:action?',
        name: 'login',
        component: () => import('src/components/auth/pages/LoginPage.vue')
    },
    {
        path: 'health-shares/:hsId?',
        name: 'health-shares',
        component: () => import('src/components/coverages/info/health-shares/pages/HsPage.vue')
    },
    {
        path: 'group-compare/:gpsId?',
        name: 'group-compare',
        component: () => import('src/components/compare/group/GroupCompare.vue')
    },
    ...formBuilderRoutes()
].map((a:any) => {
    a.meta = { ...a.meta, common: true}
    return a;
})

