// import commoncare from './symbol';
import { _get } from 'symbol-syntax-utils';
const projects = {
	// symbol
};


const envConfig = ({ domain, store, defRoutes }) => {
	if(projects) {
		let { fqdn, domain: d } = domain;
		
		let keys = Object.keys(projects || {});
		
		const obtainByKey = (str) => {
			let config;
			let i = 0;
			do {
				let { domains } = _get(projects, [keys[i]], { domains: null });
				if (domains && Array.isArray(domains)) {
					if (domains.indexOf(str) > -1) {
						config = projects[keys[i]].config({ domain, store, defRoutes });
					} else i++;
				} else i++;
			} while (!config && i < keys.length);
			return config;
		};
		
		let config = obtainByKey(fqdn);
		if (!config) config = obtainByKey(d);
		
		return config;
	} else return undefined;
};

export default envConfig;
export {
	projects
};
