import {createRouter, createWebHistory} from 'vue-router';
import {DomainHandler} from 'src/utils/domain-handler';
import {route} from 'quasar/wrappers';
import {useAuth} from 'src/stores/auth';
import {Notify} from 'quasar';
import {canU} from 'src/utils/ucans/client-auth';
import {CapabilityParts} from 'src/utils/ucans';
import {SessionStorage} from 'symbol-auth-client';
import {useCaps} from 'stores/caps';


const domainUtils = new DomainHandler();
import projects from './projects';

import Routes from './routes';
import {defRoutes} from './routes';
import {useHosts} from 'stores/hosts';
// import * as wn from 'webnative';
// import {useWnfs} from 'src/stores/wnfs';


/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

const throwNotAuth = (next: (path: string | { [key: string]: any }) => void) => {
    return () => {
        Notify.create({
            type: 'negative',
            message: 'You don\'t have permission to go there',
            timeout: 10000,
            actions: [
                {
                    icon: 'close', color: 'white', handler: () => {
                        /* ... */
                    }
                }
            ]
        });
        next('/login');
    }
}

export default route(function ({store}: any) {
    const getDomain = () => {
        const domain = domainUtils.splitDomain(window.location.href);
        if (domain.fqdn) SessionStorage.setItem('fqdn', domain.fqdn);
        return domain;
    };
    const domain: { subdomain: string | undefined, fqdn: string } & { [key: string]: any } = getDomain() as any;
    console.log('domain', domain);
    let client_ucan: any = SessionStorage.getItem('client_ucan');
    let routes = Routes({store, domain, client_ucan});


    const config = projects({store, domain, defRoutes});
    // console.log('domainConfig', config);
    if (config) {
        // console.log('config', config);
        // if (config.theme) changeTheme(config.theme);
        // if (config) appEnv().setSettings(config);
        if (config.projectUrl) {
            // console.log('custom routes', customRoutes);
            routes = [...config.routes];
            // console.log('custom routes', routes);
        }
    }

    const createHistory = createWebHistory
    // const createHistory = process?.env?.SERVER
    //     ? createMemoryHistory
    //     : process?.env?.VUE_ROUTER_MODE === 'history' ? createWebHistory : createWebHashHistory;
    //
    const Router = createRouter({
        scrollBehavior(to, from, savedPosition) {
            if (savedPosition) {
                return savedPosition;
            } else {
                return {top: 0};
            }
        },
        routes,
        // Leave these as they are and change in quasar.conf.js instead!
        // quasar.conf.js -> build -> vueRouterMode
        // quasar.conf.js -> build -> publicPath
        history: createHistory(void 0)
    });

    Router.beforeEach(async (to, from, next) => {
        const authStore = useAuth();
        if (to.path === '/logout') {
            // Clear both native storage and symbol-auth-client storage
            ['client_ucan', 'ucan_aud', 'org_id', 'plan_id', 'provider_id', 'account_id', 'moov_id'].forEach(p => {
                if(window.localStorage.getItem(a)) window.localStorage.removeItem(p);
                if(window.sessionStorage.getItem(a)) window.sessionStorage.removeItem(p);
            })
            // Import and use the SessionStorage and LocalStorage from symbol-auth-client
            await authStore.logout();
            next('/');
        }

        const {subdomain, fqdn} = domain;
        if (!!subdomain && !['admin', 'console'].includes(subdomain)) {
            if (subdomain === 'host') {
                const subsub = fqdn.split('.')[1];
                const hostStore = useHosts();
                const res = await hostStore.find({query: {subdomain: subsub, $limit: 1}})
                    .catch(err => {
                        console.error(`Error loading host - ${err.message}`)
                        return undefined
                    })
                if (res?.data[0]) {
                    const {_id, subdomain: sub, dba} = res.data[0]
                    SessionStorage.setItem('host', {_id, subdomain: sub, dba})
                }
            } else {
                const sesh: any = SessionStorage.getItem('host');
                if (subdomain !== sesh?.subdomain) {
                    const hostStore = useHosts();
                    const res = await hostStore.find({query: {subdomain, $limit: 1}})
                        .catch(err => {
                            console.error(`Error loading host - ${err.message}`)
                            return undefined
                        })
                    if (res?.data[0]) {
                        const {_id, subdomain: sub, dba} = res.data[0]
                        SessionStorage.setItem('host', {_id, subdomain: sub, dba})
                    }
                }
            }
        }
        if (!client_ucan) {
            client_ucan = authStore.user?.ucan || ''
            if (client_ucan) SessionStorage.setItem('client_ucan', client_ucan);
            else await authStore.reAuthenticate();
            client_ucan = authStore.user?.ucan || ''
            if (client_ucan) SessionStorage.setItem('client_ucan', client_ucan);
        }
        if (!!to.meta.ucan) {

            if (to.meta.ucan === true) {

                if (client_ucan) next();
                else throwNotAuth(next)()
            } else {

                let verified:any = {};
                const ucanSettings = to.meta.ucan as { requiredCapabilities: Array<CapabilityParts> } & any
                if (ucanSettings.requiredCapabilities) {
                    // console.log('required caps', ucanSettings);
                    const hasUser = !!authStore.user;
                    const verify = async () => {

                        // REQUIRES CAPABILITY LIST FROM ROUTE SETTINGS
                        verified = await canU({
                            capStore: ucanSettings.cap_subjects ? useCaps() : undefined,
                            throwNotAuth: throwNotAuth(next),
                            login: authStore.user,
                            ...ucanSettings,
                        })
                            .catch(err => {
                                console.error(`Unable to authenticate route: ${err.message}`)
                                return {}
                            })

                        // console.log('after verified', verified, authStore.user)
                    }

                    await verify()
                    if (verified?.ok) {
                        // console.log('pass');
                        next();
                    } else if (!hasUser) {
                        await authStore.reAuthenticate();
                        await verify();
                        if (verified?.ok) next();
                        else throwNotAuth(next)()
                    } else throwNotAuth(next)()


                } else next();
            }
        } else next();
    })

    return Router;
});

