import {load, FPResult} from '../fingerprintjs';

import {getUserIP } from './index';
import { SessionStorage } from 'symbol-auth-client';

const defaults = {
  path: 'fingerprintAuth',
  strategy: 'password-less',
  storageKey: 'fingerprint-jwt',
  ipService: 'my-ip',
  fpService: 'fingerprints',
  sessionKey: 'sessionPrint',
  idPath: '_id',
  params: {},
};
import { IpInfo } from '../fingerprintjs';

type IpPrintOptions = {
  path?: string,
  strategy?: string,
  storageKey?: string,
  ipService?: string,
  fpService?: string,
  sessionKey?: string,
  idPath?: string,
  params?: {[key:string]:any},
  ipFn?:(ip:IpInfo) => Promise<IpInfo>,
  log?:boolean
}

const setQuickPrint = (data:FPResult, _options?:IpPrintOptions) => {

  const options = Object.assign({}, defaults, _options || {});

  const {fingerprint: { visitorId, product, osName, manufacturer, visits }, ipInfo } = data;

  const quickPrint = {
    product,
    osName,
    manufacturer,
    ipInfo,
    visitorId,
    visits,
    start: new Date(),
  };

  SessionStorage.setItem(options.sessionKey, quickPrint);
};


const constructFingerprint = async (ipInfo: IpInfo, options?: { log?: boolean }) => {
    const log = options?.log;
    if(log) console.log('top of construct fingerprint');
    try {
        const result = await load(ipInfo, {log});
        if (log) console.log('fingerprint result', result);
        // const incognito = detectIncognito();
        const visit = {date: new Date(), incognito: result.fingerprint.incognito || false, ipInfo};
        if (log) console.log('visit add', visit);
        result.fingerprint?.visits ? result.fingerprint.visits.unshift(visit) : result.fingerprint.visits = [visit];
        return result;
    } catch (err) {
        throw new Error(err as string);
    }
};


const ipPrint = async (_ip?: IpInfo, _options?: IpPrintOptions): Promise<FPResult> => {
    try {
        const options = Object.assign({}, defaults, _options || {});
        let ipInfo = _ip
        if (options?.log) console.log('*ip print*', _ip)
        if (!_ip) {
            const ip = await getUserIP();
            ipInfo = {ip};
            if (options.ipFn) {
                ipInfo = await options.ipFn(ipInfo);
                if (options?.log) console.log('ipInfo', ipInfo);
            }
        }
        if(options?.log) console.log('calling construct fingerprint', ipInfo);
        return await constructFingerprint(ipInfo as IpInfo, { log: options?.log });
    } catch (err) {
        console.log(err);
        throw new Error(err as string);
    }
};

export {
  defaults,
  ipPrint,
  constructFingerprint,
  setQuickPrint
};









