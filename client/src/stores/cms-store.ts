import {defineStore} from 'pinia';
import type {AnyObj} from 'src/utils/types';
type StateObj = {
    policies: AnyObj
}
export const useCmsStore = defineStore('cms-store', {
    state: (): StateObj => ({
        policies: {}
    }),
    getters: {
        get(state) {
            return (id) => {
                return state.policies[id]
            }
        },
    },
    actions: {
        setById(path, val) {
            if (val?.id) this[path][val.id] = val;
        },
        removeById(path, val) {
            if (val?.id && this[path][val.id]) delete this[path][val.id];
        },
        setPolicies(data:Array<any>){
            if(!Array.isArray(data)) return;
            for(const v of data){
                this.policies[v._id] = v;
            }
        }
    },
});
