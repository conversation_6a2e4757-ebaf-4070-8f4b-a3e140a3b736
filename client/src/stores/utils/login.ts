import { useAuth } from 'src/stores/auth';
import {storeToRefs} from 'pinia';
import {computed, ref} from 'vue';
import {idGet} from 'src/utils/id-get.js';
import {usePpls} from 'stores/ppls.js';


export const loginPerson = () => {
    const authStore = useAuth();

    const { user, isAuthenticated } = storeToRefs(authStore);
    const login = computed(() => user.value || {})
    const { item:person } = idGet({
        store: usePpls() as any,
        value: computed(() => user.value?.owner),
        params: ref({
            runJoin: { add_files: true }
        })
    })
    return {
        pplStore: usePpls(),
        person,
        login,
        isAuthenticated,
    }
}

