import {defineStore} from 'pinia';

export const useValidate = defineStore('validate', {
    state: () => ({
        vErrors: {},
        vDirty: {},
        isVDirty: false,
        form: {},
        oldForm: {},
        hasVErrors: false
    }),
    getters: {

    },
    actions: {
        setForm(payload) {
            this.form = payload;
        },
        setState(payload){
            Object.keys(payload).forEach(k => {
                this[k] = payload[k];
            });
        }
    },
});
