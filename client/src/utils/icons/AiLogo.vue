<template>
  <div class="relative-position" :style="{ height: size, width: size }">
    <q-img :class="`__imgs __i${opaque ? ' __op' : ''}${dark ? ' __dark' : ''}`" fit="contain" :src="c_icon"></q-img>
    <q-img class="__imgs __p" fit="contain" :src="dot_p"></q-img>
    <q-img class="__imgs __s" fit="contain" :src="dot_s"></q-img>
    <q-img class="__imgs __a" fit="contain" :src="dot_a"></q-img>
  </div>
</template>

<script setup>
  import c_icon from 'src/assets/ai/cc_no_dot.svg'
  import dot_p from 'src/assets/ai/cc_dot_primary.svg'
  import dot_s from 'src/assets/ai/cc_dot_secondary.svg'
  import dot_a from 'src/assets/ai/cc_dot_accent.svg'

  const props = defineProps({
    size: { default: '25px' },
    opaque: <PERSON>olean,
    dark: <PERSON><PERSON>an
  })

</script>

<style lang="scss" scoped>
  .__imgs {
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  .__op {
    opacity: .3;
  }
  .__dark {
    filter: brightness(0) invert(1);
  }
  .__i {
    z-index: 4;
  }
  .__p {
    z-index: 1;
    animation: rotate 2s infinite;
  }
  .__s {
    z-index: 3;
    animation: rotate_back 4s infinite;
  }
  .__a {
    z-index: 2;
    animation: rotate_around 3s infinite;
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  @keyframes rotate_back {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(-360deg);
    }
  }
  @keyframes rotate_around {
    0% {
      transform: rotate(0deg);
    }
    25% {
      transform: rotate(180deg);
    }
    50% {
      transform: rotate(0deg);
    }
    75% {
      transform: rotate(-180deg);
    }
    100% {
      transform: rotate(0deg);
    }
  }
</style>
