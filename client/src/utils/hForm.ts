import {AnyObj} from 'src/utils/types';
import {ComputedRef, nextTick, ref, Ref, watch} from 'vue';
import {_get, _set, _unset, UndoRedoOptions} from 'symbol-syntax-utils';
import VStatic from 'src/stores/validate';
import {Notify} from 'quasar';
import {vErrNotify} from 'src/stores/validate/methods';


declare interface hFormType {
    name?: string,
    store?: any,
    beforeFn?: (val?: AnyObj) => void | Promise<void>
    afterFn?: (val: AnyObj, err?: string | boolean) => void | Promise<void>,
    formFn?: <T>(defs?: T) => T,
    params?: Ref<AnyObj> | ComputedRef<AnyObj>,
    value: Ref<AnyObj> | ComputedRef<AnyObj>,
    successMessage?: string,
    errMessage?: string,
    errNotify?: boolean,
    notify?: boolean,
    displayError?: boolean,
    errWatch?: boolean,
    noSync?: boolean,
    undoRedo?: UndoRedoOptions | boolean,
    resetOnSave?: boolean,
    validate?: boolean,
    vOpts?: Ref<AnyObj> | ComputedRef<AnyObj>,
    vFn?: (val: any) => AnyObj,
    timeout?: number,
    deepWatch?: boolean,
    sync?: (val: AnyObj) => void
    log?: boolean,
    onChange?: (val: AnyObj) => void
}

export const HForm = (
    {
        name,
        store,
        afterFn,
        beforeFn,
        params,
        undoRedo,
        value = ref({}),
        formFn,
        errWatch,
        successMessage = 'Saved!',
        errMessage = 'Error saving - ',
        displayError = true,
        errNotify = true,
        resetOnSave = false,
        log,
        validate = true,
        vOpts,
        vFn,
        sync,
        notify = true,
        timeout = 10,
        deepWatch,
        onChange
    }: hFormType) => {

    if (log) console.log('h form', name);

    const fn = formFn || ((val?: AnyObj) => {
        return val || {};
    });

    let ur;
    if (undoRedo) ur = undoRedo === true ? {defs: fn(value.value)} : undoRedo;

    const {
        isVDirty,
        setForm,
        clearForm,
        form,
        hasVErrors,
        vErrors,
        getErrorList,
        vCheck,
        refresh,
        vDirty
    } = VStatic({ur, formFn: fn});

    const updateForm = (val: AnyObj, key?: string): void => {
        if (log) console.log('update form', key, val);
        let f;
        if (key) {
            f = val;
            isVDirty.value = true;
        } else f = fn(val);
        setForm(f, key);
    };

    const runAfterFn = (res: any) => {
        if (log) console.log('saved form', res);
        if (afterFn) afterFn(res);
        if (notify) Notify.create({
            message: successMessage,
            color: 'positive',
            timeout: 2000
        });
        if (resetOnSave) clearForm(fn())
        else form.value = res;
    }

    const runErrFn = (f: AnyObj, err: AnyObj) => {
        if (afterFn) afterFn(f, err.message as string);
        console.error('Error saving in hForm', name, err)
        if (errNotify) Notify.create({
            message: (errMessage + displayError ? err.message : '') as string,
            color: 'negative',
            position: 'top',
            icon: 'mdi-alter-circle',
            timeout: 10000,
            actions: [{icon: 'mdi-close', color: 'white'}],
        })
        console.error((errMessage + displayError ? err.message : '') as string);
    }
    const save = (path?: any) => {
        if (!store) console.error('must have store instance to save form');
        else {
            const s = store['getFromStore'] ? store : store.value;
            let f: any = {}
            if (beforeFn) f = beforeFn(form.value);
            else f = form.value;
            if (!f?.save) f = s.new(f);
            const sv = () => {
                if (!!f) {
                    if (!path || typeof path !== 'string') {
                        if (log) console.log('saving full form', f);
                        return f.save(params?.value)
                            .then((res: AnyObj) => {
                                runAfterFn(res);
                                return res;
                            })
                            .catch((err: AnyObj) => runErrFn(f, err))
                    } else {
                        if (log) console.log('saving form at path', path, f);
                        return s.patch(f._id, {[path]: f[path]}, {ltail: window.location.href, ...params?.value})
                            .then((res: AnyObj) => {
                                runAfterFn(res);
                                return res;
                            })
                            .catch((err: AnyObj) => runErrFn(f, err))
                    }
                }
            }
            if (validate && (vOpts?.value || vFn)) {
                if (log) console.log('validating form', form.value, vOpts?.value, vFn);
                const vObj = vFn ? vFn(f) : vOpts?.value || {};
                const errors = vCheck(f, vObj);
                if (log) console.log('found errors?', errors);
                if (errors && errors.length) {
                    errors.forEach(err => {
                        console.error('form validation error', err);
                        vErrNotify(err)
                    })
                } else return sv();
            } else if (!!f) return sv();
        }
    }

    watch(value, (nv, ov) => {
        const isObj = typeof nv === 'object'
        if (nv && isObj && (!ov || JSON.stringify(nv) !== JSON.stringify(ov))) {
            setTimeout(() => {
                const v = isObj ? fn(nv) : fn();
                form.value = v;
                if (log) console.log('set form', form.value);
            }, timeout);
        }
        setTimeout(() => {
            if (!form.value?._id && !form.value?.__tempId && store?.getFromStore) {
                // form.value = store.createInStore(form.value || {});
            }
        }, timeout + 100)
    }, {immediate: true, deep: deepWatch})

    watch(form, (nv, ov) => {
        if (onChange) onChange(nv)
        if (nv && errWatch) {
            if (log) console.log('watch form', nv, ov);
            refresh(Object.freeze({...nv}), Object.freeze({...ov}), vOpts ? vOpts.value : {});
        }
        if (sync) sync(nv);
    }, {immediate: true})

    return {
        hasVErrors,
        vErrors,
        getErrorList,
        save,
        updateForm,
        form,
        isVDirty,
        vDirty,
        vCheck
    }
}

type HSaveOptions = {
    form: Ref<AnyObj>,
    save?: (val?: any) => void | Promise<void>,
    delay?: number,
    store?: any,
    params?: AnyObj,
    pause?: Ref<boolean> | ComputedRef<boolean>
    emit?: (val?: any) => void | Promise<void>,
    log?: boolean,
    onChange?: (val?: any) => void | Promise<void>,
    afterFn?: (val?: any) => void | Promise<void>,
}
export const HSave = ({form, log, save, delay = 3000, store, params, emit, pause, onChange, afterFn}: HSaveOptions
) => {

    const defSave = () => console.log('no save fn passed')
    const useSave = save || defSave
    const timeout: Ref<any> = ref(null);

    const getEmpty = (val: string | number) => {
        if (isNaN(Number(val))) return {};
        else return [];
    }

    const patchObj: Ref<AnyObj> = ref({})

    const nestSet = (path: string, args: any, unset?: boolean) => {
        if (path) {
            const arr: string[] = path.split('.');
            let f = typeof form.value?.clone === 'function' ? form.value.clone() : form.value;
            if (arr.length === 1) {
                if (!unset) {
                    const spl = path.split('[');
                    if (spl.length === 1) f[path] = args;
                    else f = _set(f, [path], args);
                } else {
                    f = _unset(f, [path]);
                }
            } else {
                let ov: any = _get(f, arr[0]);
                ov = !unset ? _set(ov, arr.slice(1), args) : _unset(ov, arr.slice(1));
                f = _set(f, arr[0], ov);
            }
            form.value = f;
            return f;
        }
        return form.value;

    }
    const setForm = (path: string, args: any, unset?: boolean) => {
        console.log('set form', path, args)
        const f = nestSet(path, args, unset);
        const s = store ? store['getFromStore'] ? store : store.value : {patchInStore: (...args: any) => console.warn('No store passed to set form')}
        try {
            s.patchInStore(form.value._id, f);
        } catch (err) {
            console.error('Error patching in store', err.message);
        } finally {
            if (log) console.log('form set', f, form.value);
            autoSave(path, args, f)
        }
    }

    const maybeSave = ref(useSave);
    // if you want to $set or $unset - pass $set or $unset as the paths argument and val is the $unset object
    const autoSave = (paths?: string | string[], val?: any, useForm?: AnyObj) => {
        const nestSetPatchObj = (path: string | Array<string>, value: any) => {

            const keys = Array.isArray(path) ? path : path.split('.');
            if (keys.length === 1) patchObj.value[keys[0]] = value
            else {
                console.log('should be here', keys, value);
                const obj = _set(patchObj.value[keys[0]] || isNaN(Number(keys[1])) ? {} : [], keys.slice(1), value);
                patchObj.value[keys[0]] = obj;
            }
        }
        nextTick(() => {
            if (timeout.value) clearTimeout(timeout.value);
            if (log) console.log('autoSave called', paths)
            if (paths) {
                const setPatchObj = (p: string) => {
                    if (useForm) {
                        const set = _get(useForm, p)
                        if (patchObj.value.$set) (patchObj.value.$set as any)[p] = set
                        else patchObj.value.$set = {[p]: set}
                    } else if (['$unset', '$set'].includes(p)) {
                        if (val && typeof val === 'object') {
                            nestSetPatchObj(p, val);
                            for (const pk in val) {
                                nestSet(pk, undefined, true);
                            }
                        }
                    } else {
                        nestSetPatchObj(p, (val || val === false || val === 0) ? val : _get(form.value, p))
                    }
                }
                if (Array.isArray(paths)) paths.map(a => setPatchObj(a));
                else setPatchObj(paths);

                if (log) console.log('patch obj?', !!patchObj.value, !!store);
                if (patchObj.value && store) {
                    if (log) console.log('patching then', paths, patchObj.value)
                    maybeSave.value = async () => {
                        if (log) console.log('finally saving', patchObj.value);
                        const s = store['getFromStore'] ? store : store.value;
                        s.patchInStore(form.value._id, patchObj.value)
                        let saved;
                        /** Set patch obj to empty in case changes are processed while save is occurring - because cancelling the timeout wouldn't properly merge changes that were in transit and emptying patchObj after save would overwrite changes made during the save */
                        const obj = JSON.parse(JSON.stringify(patchObj.value));
                        patchObj.value = {};
                        try {
                            saved = await s.patch(form.value['_id'], obj, params)
                                .then((res: AnyObj) => {
                                    if (log) console.log('did it save?', res);
                                    if (emit) emit(res);
                                    patchObj.value = {};
                                })
                                .catch((err: AnyObj) => {
                                    console.log(`Error saving: ${paths} - ${err.message}`);
                                })
                        } catch (err: any) {
                            console.log(`Error catch saving: ${err.message}`);
                        } finally {
                            /** if not successful, merge failed changes with any changes that occurred during saving */
                            if (!saved?._id) {
                                patchObj.value = {...obj, ...patchObj.value}
                            } else if (afterFn) afterFn(saved)
                        }
                    }
                }
            }
            timeout.value = setTimeout(() => {
                if (!pause?.value) {
                    maybeSave.value(patchObj.value)
                }
            }, delay);
        })
        if (onChange) onChange(form.value)
    }

    const setBeforeUnmount = () => {
        window.addEventListener('beforeunload', () => {
            if (pause?.value) return null
            if (timeout.value) {
                clearTimeout(timeout.value);
                timeout.value = null;
                maybeSave.value();
                return 'Saving unsaved changes'
            }
            return timeout.value;
        })
    }

    return {
        timeout,
        setBeforeUnmount,
        autoSave,
        setForm,
        getEmpty,
        patchObj,
        maybeSave
    }
}
