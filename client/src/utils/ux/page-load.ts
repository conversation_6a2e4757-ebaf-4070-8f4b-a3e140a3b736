import {ComputedRef, ref, Ref, watch} from 'vue';

type Options = {
    delay?: number,
    trigger: Ref<any> | ComputedRef<any>,
    already?: boolean
}
export const pageLoad = ({delay = 100, trigger, already}: Options) => {
    const loading = ref(already || false);
    watch(trigger, (nv) => {
        if (nv) {
            setTimeout(() => {
                loading.value = false
            }, delay)
        }
    })
    return {
        loading
    }
}
