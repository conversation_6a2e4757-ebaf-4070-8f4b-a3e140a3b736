<template>
  <div class="row">
    <div v-for="(sub, idx) in cat.cats" :key="`sub-${idx}`" class="col-12 col-md-4 q-pa-sm">
      <div class="q-pa-sm tw-five font-3-4r">{{sub.label}}</div>
      <div class="__vid q-ma-sm" v-if="videoObj[sub.key]?.uploadId">
        <div class="t-r-a">
          <remove-proxy name="Video" dense size="sm" color="red" :flat="false"
                        @remove="remove('intro')"></remove-proxy>
        </div>
        <q-video ratio="1.7777" :src="videoObj[sub.key].url"></q-video>
      </div>
      <div v-else class="__vid">
        <div class="__in" @click="add = sub.key">
          <div>Add Video</div>
        </div>
      </div>
    </div>
    <common-dialog :model-value="!!add" @update:model-value="val => val ? undefined : add = undefined" setting="smmd">
      <div class="_fw q-pa-md bg-white">
        <video-form
            allow-url
            :existingIds="fullHost?.allVideos || []"
            @remove="remove(add)"
            @update:model-value="setVideo(add, $event)"
            :display-url="videoObj[add]?.url"
        ></video-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';
  import VideoForm from 'components/common/uploads/video/VideoForm.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {useHosts} from 'stores/hosts';
  import {computed, ref, watch} from 'vue';
  import {useUploads} from 'stores/uploads';

  const hostStore = useHosts();
  const uploadStore = useUploads();

  const props = defineProps({
    cat: { required: true, type: Object },
    modelValue: { required: true, type: Object },
    host: { required: true }
  })

  const fullHost = computed(() => props.host)
  const videoObj = ref({})

  const add = ref(undefined)

  const setObj = async (p) => {
    videoObj.value = { ...(p.videos || {})[props.cat?.key] || {} };
    const keys = Object.keys(videoObj.value || {});
    const idList = [];
    for (let i = 0; i < keys.length; i++) {
      const id = videoObj.value[keys[i]]?.uploadId;
      if (!uploadStore.getFromStore(id)?.value) idList.push(id)
    }
    await uploadStore.find({ query: { _id: { $in: idList } } })
        .catch(err => console.error(`Error fetching uploads: ${err.message}`));
    for (let i = 0; i < keys.length; i++) {
      const ul = uploadStore.getFromStore(videoObj.value[keys[i]].uploadId)
      if (ul.value) videoObj.value[keys[i]].url = ul.value.url
    }
  }
  watch(fullHost, (nv, ov) => {
    if (nv && nv._id !== ov?._id) setObj(nv)
  }, { immediate: true })


  const remove = (k) => {
    const patchObj = { $unset: { [`videos.${props.cat.key}.${k}`]: '' } }
    hostStore.patch(fullHost.value._id, patchObj)
        .catch(err => console.error(`Error removing video: ${err.message}`))
    delete videoObj.value[k]
  }
  const setVideo = async (k, val) => {
    const patchObj = { $set: { [`videos.${props.cat.key}.${k}`]: val }, $addToSet: { allVideos: val.uploadId } }
    const id = fullHost.value._id;
    hostStore.patchInStore(id, patchObj);
    await hostStore.patch(id, patchObj);
    const upload = await uploadStore.get(val.uploadId)
    videoObj.value[k] = { ...videoObj.value[k], url: upload.url }
    add.value = undefined;
  }
</script>

<style lang="scss" scoped>
  .__vid {
    position: relative;
    height: 180px;
    width: 320px;
    max-width: 95vw;
    max-height: calc(95vw * .5);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0, 0, 0, .15);
  }
  .__in {
    height: 100%;
    width: 100%;
    border-radius: inherit;
    background: rgba(0,0,0,.7);
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    div {
      color: white;
      font-weight: 600;
      font-size: .9rem;
    }
  }
</style>
