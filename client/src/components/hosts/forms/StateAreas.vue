<template>
  <div class="q-pa-sm font-1r">
    <q-btn class="_i_i"  flat  @click="adding = true" no-caps>
      <span class="q-mr-sm">Add State</span>
      <q-icon name="mdi-plus" color="primary"></q-icon>
    </q-btn>
  </div>
  <div class="__sa">
    <div class="__c flex items-center" v-for="(k, i) in keys" :key="`k-${i}`" >
      <div class="q-pa-sm" @click="editing = k">
        <q-img class="h30 w30" fit="contain" :src="getStateByKey(k)?.icon"></q-img>
      </div>
      <div class="q-pa-sm _l1-2" @click="editing = k">
        <div class="font-7-8r tw-six">{{getStateByKey(k)?.text}}</div>
        <div class="q-pt-xs font-3-4r">
          <span v-if="mv[k].all">Entire State</span>
          <span v-else>
            <span v-if="Object.keys(mv[k].counties || {}).length === 1">{{Object.keys(mv[k].counties)[0]}} County</span>
            <span v-else>{{$possiblyPlural('Count', Object.keys(mv[k].counties), 'y', 'ies')}}</span>
          </span>
        </div>
      </div>
      <div class="q-pa-sm">
        <remove-proxy :name="k" dense flat icon="mdi-close" label="" @remove="removeState(k)"></remove-proxy>
      </div>
    </div>
    <common-dialog :model-value="!!editing || adding" @update:model-value="toggleDialog" setting="smmd">
      <div class="_fw q-pa-md bg-white">
        <state-area-form :filter-out="Object.keys(mv)" :adding="adding" :model-value="mv[editing]" @update:model-value="setArea"></state-area-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import StateAreaForm from 'components/hosts/forms/StateAreaForm.vue';

  import {computed, ref} from 'vue';
  import { getStateByKey } from 'components/common/geo/data/states';
  import {$possiblyPlural} from 'src/utils/global-methods';
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: Object
  })
  const mv = computed(() => props.modelValue || {})

  const keys = computed(() => Object.keys(mv.value).sort((a, b) => a.localeCompare(b)));

  const adding = ref(false);
  const editing = ref(undefined);
  const toggleDialog = (val) => {
    if(!val) {
      editing.value = undefined
      adding.value = val;
    }

  }

  const setArea = (val) => {
    const k = val.state
    editing.value = k;
    emit('update:model-value', { ...mv.value, [k]: val})
  }

  const removeState = (k) => {
    const obj = { ...mv.value};
    delete obj[k];
    emit('update:model-value', obj)
  }

</script>

<style lang="scss" scoped>
.__sa {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 250px));
}
.__c {
  width: 100%;
  display: grid;
  grid-template-columns: auto 1fr auto;
  cursor: pointer;
  //box-shadow: 0 2px 6px #dedede;
  border-radius: 6px;
  background: white;
  transform: none;
  transition: transform .2s;

  &:hover {
    transform: translate(0, -2px);
  }
}
</style>
