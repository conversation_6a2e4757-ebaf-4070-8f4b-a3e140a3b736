<template>
  <div class="__gender_picker">
    <div @click="emit('update:model-value', 'male')">
      <q-icon :size="size" name="mdi-human-male" :class="`${modelValue === maleValue ? '' : '__inactive'}`"></q-icon>
      <q-tooltip class="text-xxs tw-six">Male</q-tooltip>
    </div>
    <div @click="emit('update:model-value', 'female')">
      <q-icon :size="size" name="mdi-human-female" :class="`${modelValue === femaleValue ? '' : '__inactive'}`"></q-icon>
      <q-tooltip class="text-xxs tw-six">Female</q-tooltip>

    </div>
  </div>
</template>

<script setup>
  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    modelValue: String,
    maleValue: { default: 'male' },
    femaleValue: { default: 'female' },
    size: { default: '30px' }
  })
</script>

<style lang="scss" scoped>
  .__gender_picker {
    position: relative;
    display: flex;
    align-items: center;
    align-content: center;

    > div {
      &:first-child {
        color: var(--q-ir-light-blue);
      }
      &:last-child {
        color: var(--q-ir-pink-4);
      }
      &:first-child, &:last-child {
        transition: all .2s;
        cursor: pointer;
        transform: translate(0, -2px);

        &:hover {
          transform: translate(0, -4px);
        }
      }
    }
  }

  .__inactive {
    color: var(--ir-mid);
  }
</style>
