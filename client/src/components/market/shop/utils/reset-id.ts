import {ref} from 'vue';

export const shopResetId = () => {

    const getResetId = (shot:any) => {
        const { age, people, risk, smoker, place, income } = shot?.stats || {}
        const pplId = (people || []).map(a => a.age).join('|')
        const fips = place?.countyfips || ''
        return `${pplId}|${income}|${fips}|${age}|${risk}|${smoker}`
    }

    const lastResetId = ref('')
    const resetTo = ref();

    const validPlace = (place:any) => {
        if(!place) return false
        return place.countyfips && place.zipcode && place.state
    }
    return {
        resetTo,
        getResetId,
        validPlace,
        lastResetId
    }
}

