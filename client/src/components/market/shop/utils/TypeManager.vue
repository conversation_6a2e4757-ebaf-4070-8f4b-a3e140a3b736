<template>
  <div class="row items-center">
    <div class="flex items-center" v-for="tp in ['mm', 'aca', 'hs']" :key="`tp-${tp}`">
      <q-chip
          class="q-py-xs"
          color="ir-bg2"
          text-color="ir-text"
          clickable
      >
        <q-avatar :color="productTypes[tp].color"></q-avatar>
        <span class="q-ml-xs tw-five">{{ productTypes[tp].label }}</span>
        <q-popup-proxy :breakpoint="50000">
          <div class="q-pa-lg bg-ir-bg2 text-ir-text">
            <div class="q-py-md row items-center">
              <q-avatar size="20px" :color="productTypes[tp].color"></q-avatar>
              <div class="q-pl-sm tw-six">{{ productTypes[tp].label }}</div>
              <q-space></q-space>
              <div class="text-xxxs tw-six">SHOW</div>
              <q-checkbox
                  class="q-ml-sm text-ir-deep"
                  size="sm"
                  :model-value="modelValue[tp]" @update:model-value="toggleType(tp)"></q-checkbox>
            </div>
            <div class="text-xs tw-five" v-html="productTypes[tp].description"></div>
          </div>
        </q-popup-proxy>
        <q-checkbox
            class="q-ml-sm text-ir-deep"
            size="sm"
            :model-value="modelValue[tp]"
            @update:model-value="toggleType(tp)"></q-checkbox>

      </q-chip>
    </div>
  </div>
</template>

<script setup>

import {productTypes} from 'components/market/utils';

const emit = defineEmits(['update:model-value', 'toggle'])
const props = defineProps({
  modelValue: { default: () => {
      return {
        aca: true,
        mm: true,
        hs: true
      }
    }}
})

const toggleType = (t) => {
  emit('toggle', t);
  emit('update:model-value', { ...props.modelValue, [t]: !props.modelValue[t] })
}
</script>

<style lang="scss" scoped>

</style>
