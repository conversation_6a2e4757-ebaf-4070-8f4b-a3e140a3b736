import {ref} from 'vue';

export const countdownUtils = () => {
    const countdown = ref(0);
    const interval = ref()
    const resetCountdown = () => {
        countdown.value = 10;
        interval.value = setInterval(() => {
            if(countdown.value > 0) countdown.value--;

            if (countdown.value <= 0) {
                clearInterval(interval.value);
            }
        }, 1000);
    }
    return {
        countdown,
        interval,
        resetCountdown
    }
}
