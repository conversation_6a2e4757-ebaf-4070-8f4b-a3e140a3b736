import {ref} from 'vue';
import { useStaticPlans } from 'stores/static-plans';

export const byIdObj = () => {
  return {
      all: {},
      top1: [],
      top3: [],
      average: [],
      blended: [],
      blend_scores: {},
      blend_rank: {},
      scores: {}
  }
}

export const planTypeManager = (cb: (v:any) => Promise<any>) => {
    const activeTypes = ref({
        'aca': true,
        'mm': true,
        'hs': true
    })
    const toggleType = (val) => {
        activeTypes.value[val] = !activeTypes.value[val];
        cb(activeTypes.value);
    }

    return {
        toggleType,
        activeTypes
    }
}
export const rankById = async (shop:any, activeTypes: {aca: boolean, mm: boolean, hs: boolean}) => {
    const obj = byIdObj();

    const coverageObj = {};
    const staticStore:any = useStaticPlans();
    const query = { $limit: shop.coverages.length, _id: { $in: shop.coverages || [] } }
    const coverages = staticStore.findInStore({ query })
    for(const cov of coverages.data){
        coverageObj[cov._id] = cov;
    }
    if(coverages.total < shop.coverages.length) {
        const notInStore = shop.coverage.filter(a => !coverageObj[a]);
        const fetched = await staticStore.find({ ...query, $limit: notInStore.length })
            .catch(err => console.error(`Error fetching shop coverages: ${err.message}`))
        for(const cov of fetched?.data || []){
            coverageObj[cov._id] = cov;
        }
    }
    const scores = shop.useAptc ? shop.coverage_scores_ptc || {} : shop.coverage_scores || {}
    obj.scores = scores;
    const coverageList = Object.keys(coverageObj).map(a => coverageObj[a])
    for (let i = 0; i < coverageList.length; i++) {
        const c = coverageList[i];
        if(!activeTypes[c.acaPlan ? 'aca' : c.type]) continue;
        obj.all[c._id] = c;
        if (i === 0) {
            obj.top1.push(c._id);
            obj.top3.push(c._id);
            obj.average.push(c._id);
        } else {
            let placedTop1;
            for (let t = 0; t < obj.top1.length; t++) {
                if ((scores[c._id]?.top1 || 0) > (scores[obj.top1[t]]?.top1 || 0)) {
                    obj.top1.splice(t, 0, c._id);
                    placedTop1 = true;
                    break;
                }
            }
            if (!placedTop1) obj.top1.push(c._id);

            let placedTop3
            for (let t = 0; t < obj.top3.length; t++) {
                if ((scores[c._id]?.top3 || 0) > (scores[obj.top3[t]]?.top3 || 0)) {
                    obj.top3.splice(t, 0, c._id)
                    placedTop3 = true;
                    break;
                }
            }
            if (!placedTop3) obj.top3.push(c._id);

            let placedAverage;
            for (let t = 0; t < obj.average.length; t++) {
                if ((scores[c._id]?.average || 0) < (scores[obj.average[t]]?.average || 0)) {
                    obj.average.splice(t, 0, c._id)
                    placedAverage = true;
                    break;
                }
            }
            if (!placedAverage) obj.average.push(c._id);
        }
    }

    for (let i = 0; i < coverageList.length; i++) {
        const c = coverageList[i];
        const top1 = obj.top1.indexOf(c._id)
        const top3 = obj.top3.indexOf(c._id)
        const average = obj.average.indexOf(c._id)
        const blend = top1 + top3 + average;
        obj.blend_scores[c._id] = blend;
        let placedBlend;
        for (let t = 0; t < obj.blended.length; t++) {
            if (blend < obj.blend_scores[obj.blended[t]]) {
                obj.blended.splice(t, 0, c._id);
                placedBlend = true;
                break;
            }
        }
        if (!placedBlend) obj.blended.push(c._id)
        for (let t = 0; t < obj.blended.length; t++) {
            obj.blend_rank[obj.blended[t]] = t + 1
        }
    }
    return obj;
}
