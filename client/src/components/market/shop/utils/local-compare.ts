import {getAcaOop, getCoverageRate, getFixedRateKey, getPrivateOop} from 'components/coverages/utils/display';
import {Ref, ref, watch} from 'vue';
import {AnyObj, AnyRef} from 'src/utils/types';

type Options = {
    coverages: AnyRef<AnyObj>,
    def_key?: AnyRef<string>,
    def_age?: AnyRef<number>,
    aptc?: AnyRef<number>,
    enrollment?: AnyRef<any>
    tax_rate?:AnyRef<number>
}

const getSpend = (spend, key, {moop, coinsurance, deductible, familyAcc}) => {
    if (key === 'single') {
        const ded = Math.min(spend, deductible.single || deductible.family)
        const coins = (spend - ded) * (Number(coinsurance) <= 1 ? Number(coinsurance) : 0)
        return Math.min(moop.single || moop.family, ded + coins)
    } else {
        const spnd = spend;
        const familyDed = Math.max(0, deductible?.family - (familyAcc || 0));
        const ded = Math.min(spnd, deductible?.single, familyDed)
        const coins = (spnd - ded) * (Number(coinsurance) <= 1 ? Number(coinsurance) : 0)
        const familyMoop = (moop.family || moop.single) - (familyAcc || 0);
        return Math.max(0, Math.min(ded + coins, (moop.single || moop.family), familyMoop))
    }
}
export const localCompareShop = ({coverages, def_key, def_age, aptc, enrollment, tax_rate}: Options) => {
    const sortTo:Ref<any> = ref();
    const spd = ref(1000)
    const lastSpd = ref(1000);
    const sorted = ref([]);
    const sorted_ptc = ref([])
    const evts = ref(1);

    const sort = () => {
        if (sortTo.value) clearTimeout(sortTo.value);
        lastSpd.value = spd.value;
        const s = [];
        const list = Object.keys(coverages.value || {})
        const key = getFixedRateKey({
            def_key: def_key.value,
            def_age: def_age.value,
            enrolled: Object.keys(enrollment?.value?.enrolled || {}).map((a:any) => {
                return {
                    age: a.age,
                    relation: a.relation
                }
            })
        }).key
        for (let i = 0; i < list.length; i++) {
            const item = {...coverages.value[list[i]]};
            const premium = Math.max(0, getCoverageRate({
                coverage: item,
                enrollment: enrollment?.value,
                def_key: key,
                def_age: def_age?.value
            })) * 12
            const premium_ptc = Math.max(0, premium - ((item.acaPlan ? item.off_exchange ? 0 : (aptc?.value || 0) : 0) * 12));
            const {
                moop,
                deductible,
                coinsurance
            } = item.acaPlan ? getAcaOop({policy: item}) : getPrivateOop({policy: item})

            let total = 0;
            for (let idx = 0; idx < evts.value; idx++) {
                total += getSpend(spd.value / evts.value, key, {moop, coinsurance, deductible} as any)
            }

            const tax_savings = premium * (tax_rate?.value ? tax_rate.value + .153 : 0)

            item._fastjoin = {
                ...item._fastjoin,
                deductible,
                coinsurance,
                moop,
                spend: spd.value,
                oop: total,
                total: total + premium - tax_savings,
                total_ptc: total + premium_ptc,
                premium,
                premium_ptc
            }
            s.push(item)
        }
        sorted.value = [];
        sorted_ptc.value = [];
        for (let i = 0; i < s.length; i++) {
            let s_placed;
            for (let j = 0; j < sorted.value.length; j++) {
                if (s[i]._fastjoin.total < sorted.value[j]._fastjoin.total) {
                    sorted.value.splice(j, 0, s[i]);
                    s_placed = true;
                    break;
                }
            }
            if (!s_placed) sorted.value.push(s[i]);
            let sp_placed
            for (let j = 0; j < sorted_ptc.value.length; j++) {
                if (s[i]._fastjoin.total_ptc < sorted_ptc.value[j]._fastjoin.total_ptc) {
                    sorted_ptc.value.splice(j, 0, s[i]);
                    sp_placed = true;
                    break;
                }
            }
            if (!sp_placed) sorted_ptc.value.push(s[i]);
        }
    }

    const runSort = (ovr?:any) => {
        if (ovr) return sort()
        if (Math.abs(lastSpd.value - spd.value) > 1000) return sort()
        if (sortTo.value) clearTimeout(sortTo.value);
        sortTo.value = setTimeout(() => {
            sort()
        }, 1000)
    }

    const incEvts = (v) => {
        const nv = v + evts.value;
        if (nv > 0 && nv < 10) {
            evts.value = nv
            if (sortTo.value) clearTimeout(sortTo.value);
            sortTo.value = setTimeout(() => {
                sort()
            }, 1000)
        }
    }

    watch(spd, (nv, ov) => {
        if ((typeof nv === 'number') && (typeof ov === 'number') && nv !== ov) {
            runSort()
        }
    }, {immediate: true});
    watch(evts, (nv, ov) => {
        if ((typeof nv === 'number') && (typeof ov === 'number') && nv !== ov) {
            if (sortTo.value) clearTimeout(sortTo.value);
            sortTo.value = setTimeout(() => {
                sort()
            }, 1000)
        }
    }, {immediate: true});
    return {
        evts,
        incEvts,
        sort,
        spd,
        sorted,
        sorted_ptc,
        sortTo,
        lastSpd,
        runSort
    }
}
