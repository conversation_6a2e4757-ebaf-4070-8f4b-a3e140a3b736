import {dollarString} from 'src/utils/global-methods';

export const spendByAge = {
    18: 2283,
    34: 5227,
    44: 6898,
    54: 9122,
    64: 10632,
    120: 13921
}

export const getSpendByAge = (age: number) => {
    for (const k in spendByAge) {
        if (age <= Number(k)) return spendByAge[k];
    }
    return spendByAge[120]
}

export const riskLabels = {
    1: 'Minimal',
    2: 'Very Low',
    3: 'Low',
    4: 'Below Average',
    5: 'Average',
    6: 'Above Average',
    7: 'Medium High',
    8: 'High',
    9: 'Very High',
    10: 'Catastrophic'
}

export const riskMultiples = {
    1: .1,
    2: .2,
    3: .5,
    4: .8,
    5: 1,
    6: 1.2,
    7: 1.6,
    8: 2.5,
    9: 6,
    10: 12
}
export const displayRisk = (risk: number, age: number, add?:Array<number>) => {
    if(!add?.length) return dollarString(getSpendByAge(age) * riskMultiples[risk], '$', 0)
    else return dollarString([age, ...add].reduce((acc, v) => acc + getSpendByAge(v), 0), '$', 0)
}

export const riskColors = {
    1: 'p4',
    2: 'primary',
    3: 'p6',
    4: 'a6',
    5: 'accent',
    6: 'a4',
    7: 's4',
    8: 'secondary',
    9: 's6',
    10: 's7'
}

export const getChild = (defs?:any) => {
    return {
        age: 10,
        gender: Math.random() > .5 ? 'male' : 'female',
        child: true,
        smoker: false,
        ...defs
    }
}

export const getSpouse = (stats?:any) => {
    const s = stats || {};
    return {
        age: s.age || 40,
        gender: s.gender === 'male' ? 'female' : 'male',
        child: false,
        smoker: s.smoker || false
    }
}

export const getStats = (defs: any) => {
    const age = 40
    return {
        preEx: true,
        income: 85000,
        gender: 'male',
        // state: undefined,
        age,
        spend: 2000,
        ded: 3000,
        people: [getSpouse({ age }), getChild(), getChild()],
        // spouse: true,
        // plus: 2,
        household_size: 1,
        city: undefined,
        smoker: false,
        // ppl: 1,
        // events: 2,
        risk: 2,
        place: {
            countyfips: undefined,
            zipcode: undefined,
            state: undefined
        },
        ...defs
    }
}

export const reloadPaths = ['age', 'people', 'risk', 'smoker', 'place', 'city', 'income']

export const enrollExtras = {
    monthsSinceSmoked: {type: 'number'},
    disabled: {type: 'boolean'},
    incarcerated: {type: 'boolean'},
    latino: { type: 'boolean' },
    native: { type: 'boolean' },
    pregnant: { type: 'boolean' },
    us_citizen: { type: 'boolean' },
    adl_assist: { type: 'boolean' },
    medicaid: { type: 'boolean' },
    medicaid_ineligible: { type: 'boolean' },
    outside_coverage: { type: 'boolean' },
    outside_coverage_end: { type: 'string' },
    job_coverage: { type: 'boolean' }

}

export const qualifyingEvents = {
    'marriage': { label: 'Got Married' },
    'lost_coverage': { label: 'Lost Coverage' },
    'moved': { label: 'Moved' },
    'liberated': { label: 'Released from jail' },
    'adoption': { label: 'Adopted a child' }
}
