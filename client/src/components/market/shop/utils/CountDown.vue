<template>
  <div :class="`__cdown ${countdown ? '' : '__cdown_off'}`">
    <div class="text-center row justify-center items-center">
      <div class="text-xxs tw-six">Changes detected - re-simulating in <span class="alt-font text-primary">{{ countdown }}</span>.
      </div>
      <q-chip clickable @click="emit('reset')" color="ir-bg2">
        <span class="q-mr-sm">Run Now</span>
        <q-icon color="green" name="mdi-play"></q-icon>
      </q-chip>
      <q-btn dense flat size="sm"  class="q-ml-md" color="red" icon="mdi-close" @click="emit('close')"></q-btn>
    </div>
  </div>
</template>

<script setup>

  const emit = defineEmits(['reset', 'close']);
  const props = defineProps({
    countdown: Number,
  })

</script>

<style lang="scss" scoped>
  .__cdown {
    position: fixed;
    z-index: 10000;
    bottom: 0;
    left: 50%;
    width: 800px;
    max-width: 95vw;
    transform: translate(-50%, 0);
    background: rgba(255, 255, 255, .6);
    backdrop-filter: blur(5px);
    border-radius: 10px 10px 0 0;
    padding: 10px 20px;
    box-shadow: 0 -2px 18px rgba(0,0,0,.1);
    border: solid 5px var(--q-p2);
    border-bottom: none;
    transition: all .3s;
  }
  .__cdown_off {
    max-height: 0;
    padding: 0;
    overflow: hidden;
    border: none;
  }
</style>
