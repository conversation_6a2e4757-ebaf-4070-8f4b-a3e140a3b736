<template>
  <div class="_fw _oh">
    <div class="__fg">

      <div>Primary Email</div>
      <div class="mw500">
        <email-field hide-bottom-space dense filled :model-value="person.email" @update:model-value="set<PERSON>erson(person._id, 'email', $event)"></email-field>
      </div>

      <div>Primary Phone</div>
      <div class="mw500">
        <phone-input :input-attrs="{ dense: true, filled: true }" :model-value="person.phone" @update:model-value="setPerson(person._id, 'phone', $event)"></phone-input>
      </div>

      <div>Home Address</div>
      <div class="mw500">
        <tomtom-autocomplete :chip-attrs="{ color: 'white' }" filled dense :model-value="person.address" @update:model-value="setPerson(person._id, 'address', $event)"></tomtom-autocomplete>
      </div>

      <div>Household Members</div>
      <div>
        <div class="__hh_table">
        <household-table :model-value="hh"></household-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import EmailField from 'components/common/input/EmailField.vue';
  import PhoneInput from 'components/common/phone/PhoneInput.vue';
  import TomtomAutocomplete from 'components/common/address/tomtom/TomtomAutocomplete.vue';
  import HouseholdTable from 'components/households/forms/HouseholdTable.vue';

  import {computed} from 'vue';
  import {usePpls} from 'stores/ppls';

  const pplStore = usePpls();

  const props = defineProps({
    household: { required: true },
    person: { required: true }
  })

  const hh = computed(() => props.household);
  const ppl = computed(() => props.person);

  const setPerson = (id, path, val) => {
    pplStore.patch(ppl.value._id, { [path]: val })
  }

</script>

<style lang="scss" scoped>

  .__fg {
    width: 100%;
    display: grid;
    grid-template-columns: auto minmax(0, 1fr);


    > div {
      &:nth-child(odd) {
        padding: 5px 10px;
        font-weight: 600;
        max-width: 120px;
        font-size: .8rem;
        border-right: solid .3px var(--ir-mid);
      }
      &:nth-child(even) {
        padding: 10px;
        width: 100%;
      }
    }
  }
  .__hh_table {
    width: 100%;
    overflow-x: scroll;
  }
</style>
