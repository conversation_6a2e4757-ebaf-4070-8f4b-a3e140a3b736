<template>
  <div class="q-pa-sm font-7-8r">Some questions about your household to ensure we do this right</div>

  <div class="__fg">
    <div>Select anyone with physical or mental conditions making it challenging to work or carry out activities of daily
      living
    </div>
    <div>
      <member-toggle :by-id="pplById" path="adl_assist" @by-id="setMember"></member-toggle>
    </div>
    <div>Select anyone who has recently enrolled in Medicaid or CHIP</div>
    <div>
      <member-toggle path="medicaid" :by-id="pplById" @by-id="setMember"></member-toggle>
    </div>
    <div>Select anyone who have been found ineligible for Medicaid/CHIP in the past 90 days</div>
    <div>
      <member-toggle path="medicaid_ineligible" :by-id="pplById" @by-id="setMember"></member-toggle>
    </div>
    <div>Select anyone with existing health insurance either through the marketplace, a private policy (not health
      shares), or their employer
    </div>
    <div>
      <member-toggle path="outside_coverage" :by-id="pplById" @by-id="setMember">
        <template v-slot:side="scope">
          <date-select-chip
              empty-label="End Date"
              :model-value="pplById[scope.id].outside_coverage_end" dense true
              class="bg-ir-bg2"
              square
              @update:model-value="setMember(scope.id, $event, 'outside_coverage_end')">
            <template v-slot:left>
              <q-icon class="q-mr-xs" name="mdi-calendar" color="primary"></q-icon>
            </template>
          </date-select-chip>
        </template>
      </member-toggle>
    </div>
    <div>Select who is offered affordable major medical coverage through their job</div>
    <div>
      <member-toggle path="job_coverage" :by-id="pplById" @by-id="setMember"></member-toggle>
    </div>
    <div>Select any of the following that have occurred in the past 60 days</div>
    <div>
      <div class="row items-center" v-for="(k, i) in Object.keys(qualifyingEvents)" :key="`k-${i}`">
        <div class="text-xxs text-ir-deep">{{ qualifyingEvents[k].label }}:
          <date-select-chip
              :model-value="(hh.qual_events || {})[k]"
              empty-label="None"
              @update:model-value="setQualEvent(k, $event)"
          ></date-select-chip>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup>
  import MemberToggle from 'components/market/shop/utils/ua/MemberToggle.vue';
  import DateSelectChip from 'components/common/dates/DateSelectChip.vue';

  import {computed, ref, watch} from 'vue';
  import {usePpls} from 'stores/ppls';
  import {useHouseholds} from 'stores/households';
  import {_pick} from 'symbol-syntax-utils';
  import {enrollExtras, qualifyingEvents} from 'components/market/shop/utils';

  const pplStore = usePpls();
  const hhStore = useHouseholds();

  const props = defineProps({
    person: { required: true },
    household: { required: true },
  })

  const hh = ref({});
  watch(() => props.household, (nv) => {
    if (nv) hh.value = nv;
  }, { immediate: true });

  const prsn = computed(() => props.person);

  const pplById = ref({})

  const setPplById = async () => {
    const mbrs = Object.keys(hh.value.members || {})
    const extras = Object.keys(enrollExtras)
    pplById.value[prsn.value._id] = { ...prsn.value, ..._pick(hh.value, extras) };
    const inStore = pplStore.findInStore({ query: { _id: { $in: mbrs } } })
    for (let i = 0; i < inStore.data.length; i++) {
      pplById.value[inStore.data[i]._id] = { ...inStore.data[i], ...hh.value.members[inStore.data[i]._id] };
    }
    const filtered = mbrs.filter(a => !pplById.value[a]);
    if (filtered.length) {
      const db = await pplStore.find({ query: { $limit: filtered.length, _id: { $in: filtered } } })
      for (let i = 0; i < db.data.length; i++) {
        pplById.value[db.data[i]._id] = { ...db.data[i], ...hh.value.members[db.data[i]._id] };
      }
    }
  }
  watch(hh, async (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      setPplById();
    }
  }, { immediate: true });


  const saveTo = ref();
  const patchObj = ref({})
  const maybeSave = () => {
    if (saveTo.value) clearTimeout(saveTo.value)
    saveTo.value = setTimeout(async () => {
      await hhStore.patch(hh.value._id, { $set: patchObj.value })
      patchObj.value = {}
    }, 3000)
  }
  const setMember = (id, val, path) => {
    let p = `members.${id}.${path}`
    if (id === prsn.value._id) {
      p = path;
      hh.value[path] = val;
    } else {
      console.log('setting member', id, path, hh.value.members[id])
      hh.value.members[id][path] = val;
      console.log('done', hh.value.members[id])
    }
    // hhStore.patchInStore(hh.value._id, { $set: { [p]: val } })
    setPplById();
    patchObj.value[p] = val;
    maybeSave();
  }
  const setQualEvent = (k, val) => {
    hh.value.qual_events = { ...hh.value.qual_events, [k]: val }
    patchObj.value[`qual_events.${k}`] = val;
    maybeSave();
  }

</script>

<style lang="scss" scoped>

  .__fg {
    font-size: .9rem;
    font-weight: 400;

    > div {
      &:nth-child(odd) {
        padding: 10px;
        background: var(--ir-bg);
        z-index: 1;
        position: relative;
        font-weight: 500;
      }

      &:nth-child(even) {
        padding: 10px;
        border: solid .3px var(--ir-light);
        transform: translate(0, -10px);
        border-radius: 8px;
      }
    }
  }
</style>
