<template>
  <div class="row items-center">
    <q-chip v-bind="{ label, color: 'ir-bg2', clickable: true, iconRight: 'mdi-menu-down', ...chipAttrs}">
      <q-menu>
        <div class="w300 mw100">
          <q-list separator>
            <q-item v-for="(id, i) in Object.keys(byId)" :key="`opt-${i}`" clickable
                    @click="emit('by-id', id, !(byId[id] || {})[path], path)">
              <q-item-section>
                <q-item-label>{{ byId[id]?.firstName }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <div :class="`text-xxs tw-six text-${color(byId[id])}`">{{ byId[id].age }}</div>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </q-menu>
    </q-chip>
    <div class="flex items-center" v-for="(id, i) in Object.keys(byId).filter(a => byId[a][path])" :key="`id-${i}`">
      <q-chip class="flex items-center"
              v-bind="{ color: 'ir-bg2', ...chipAttrs}">
        <q-avatar dark :color="color(byId[id])">
          <div class="_fa flex flex-center tw-five text-white">{{ byId[id].firstName?.charAt(0) }}</div>
        </q-avatar>
        <span>{{ byId[id].firstName }}</span>
        <q-btn color="red" icon="mdi-close" dense flat size="xs" @click="emit('by-id', id, false, path)"></q-btn>
      </q-chip>
      <slot name="side" :id="id"></slot>
    </div>

  </div>
</template>

<script setup>

  const emit = defineEmits(['by-id'])
  const props = defineProps({
    path: { required: true },
    label: { default: 'Select Members' },
    chipAttrs: Object,
    byId: {
      default: () => {
        return {}
      }
    }
  })

  const color = (p) => {
    const first = p.gender?.charAt(0).toLowerCase()
    if (first === 'm') return 'ir-light-blue'
    if (first === 'f') return 'ir-pink-4'
    return 'ir-grey-6'
  }
</script>

<style lang="scss" scoped>

</style>
