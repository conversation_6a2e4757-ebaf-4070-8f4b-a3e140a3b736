<template>
  <div class="__aich" :id="id">
    <div class="row justify-center">
      <div class="_cent">

        <div class="__stats">
          <div class="z2 text-xs q-pb-md tw-six text-ir-mid alt-font q-px-md">Your Household</div>
          <div class="row items-start">
            <div class="col-12 col-md-6">

              <div class="__second _fw q-py-md __s">
                <div class="__dsbl flex flex-center" v-if="loading">
                  <ai-logo opaque size="30px"></ai-logo>
                </div>
                <div class="row justify-center">
                  <div class="__st relative-position _oh">
                    <div class="__ovr"></div>

                    <div class="text-sm tw-six _fw relative-position z2">

                      <div class="row items-center">
                        <div class="col-12">
                          <zip-picker class="text-xs" @zip-data="setZipData" :model-value="place.zipcode">
                            <template v-slot:left>
                              <q-icon name="mdi-map-marker" color="red" class="q-mr-sm"></q-icon>
                            </template>
                          </zip-picker>
                        </div>
                      </div>


                      <stat-picker :model-value="shop" @reload="resetMarket"></stat-picker>

                      <div class="q-py-md _fw">
                        <risk-profile
                            :age="stats.age"
                            :model-value="stats.risk"
                            :household="hh"
                            @update:model-value="setStat('risk', $event, resetMarket)"
                        ></risk-profile>
                      </div>

                    </div>
                  </div>
                </div>
              </div>

            </div>
            <div class="col-12 col-md-6">

              <div class="__s">

                <div class="text-lg tw-six">
            <span id="SimsCount" class="__clip alt-font tw-nine">{{
                dollarString(simsRun, '', 0)
              }}</span>
                  simulations run
                </div>
                <div class="text-xs">AI simulations based on millions of actuarial healthcare billing reports matching
                  your
                  household profile. <span class="tw-six cursor-pointer" @click="simDialog   = true">Learn More</span>
                </div>


                <div class="text-xs tw-six q-pb-sm q-pt-lg text-ir-deep">
                  <q-icon name="mdi-filter" color="primary"></q-icon>
                  Plan Types:
                </div>
                <div class="_fw  q-pb-md">
                  <type-manager
                      :model-value="activeTypes"
                      @toggle="toggleType"
                  ></type-manager>
                </div>

                <div class="text-xs tw-six q-pt-md q-px-sm text-ir-deep">
                  <div class="_fw">Show totals as:</div>
                  <q-radio class="tw-five" :dark="dark" label="Annual" :model-value="mult" :val="12"
                           @update:model-value="updateMult"></q-radio>
                  <q-radio class="tw-five" :dark="dark" label="Monthly" :model-value="mult" :val="1"
                           @update:model-value="updateMult"></q-radio>
                </div>

              </div>
            </div>
          </div>
        </div>


      </div>
    </div>

    <div class="row justify-center">

      <div class="_sent __explore">


        <template v-if="shop._id">
          <div class="text-md tw-six q-py-sm text-ir-deep">Explore the results</div>

          <div class="text-sm">We simulated an average of <span
              class="tw-six alt-font text-secondary">{{ dollarString(shop?.spend, '$', 0) }}</span> in annual medical
            bills
            for
            your household (some years much less, some much more) and applied them against each plan below.
            <div class="q-pt-sm text-xs text-ir-mid tw-six text-ir-deep">Does that seem right? It is for some households
              -
              adjust your risk profile if needed.
            </div>
          </div>
        </template>

        <div class="_fw pd6 pw2 text-sm text-ir-mid tw-six" v-else>
          <div v-if="stats.place?.countyfips" class="text-center">
            <span>Once your household profile looks accurate</span>
            <div class="row justify-center q-pt-md">
              <q-btn @click="resetMarket('', true,true)" rounded push class="__but" :disable="loading" no-caps>
                <template v-if="!loading">
                <span class="tw-five">Run the simulation</span>
                <q-icon class="q-ml-sm" name="mdi-chevron-right"></q-icon>
                </template>
                <template v-else>
                  <span class="tw-five q-mr-sm">Running Simulation</span>
                  <q-spinner-dots color="white"></q-spinner-dots>
                </template>
              </q-btn>
            </div>
          </div>
          <div v-else class="text-center">
            Zip code is required to run a simulation
          </div>

        </div>


        <div class="__pt __bow">
          <div class="__ptc">
            <ptc-display
                :loading="loading"
                :mult="mult"
                :dark="dark"
                :ptc="(shop?.aptc || 0) * mult"
                :model-value="shop.useAptc"
                @update:model-value="toggleAptc"
            ></ptc-display>

            <div class="__allowance" v-if="shop.enrollment && (employer || subsidy)">
              <div class="_fw">
                <q-chip class="text-sm tw-five bg-accent text-white">Employer Contributions:</q-chip>
              </div>
              <div class="_fw text-xs" v-if="subsidy">
                <div class="q-px-sm tw-six text-sm">Premium Subsidy: <span
                    class="text-lg text-a7 alt-font">{{ dollarString((subsidy || 0) * mult, '$', 0) }}</span><span
                    class="text-xs tw-five q-ml-xs">{{ mult === 12 ? '/yr' : '/mo' }}</span></div>
                <div class="q-px-sm">This can only be used on the coverages here</div>
              </div>
              <div class="_fw text-xs" v-if="employer">
                <div class="q-px-sm tw-six text-sm">Allowance: <span
                    class="text-accent alt-font text-lg">{{ dollarString((employer || 0) * mult, '$', 0) }}</span><span
                    class="text-xs tw-five q-ml-xs">{{ mult === 12 ? '/yr' : '/mo' }}</span></div>
                <div class="q-px-sm">This can be used on other benefits including take-home</div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>


    <result-category
        :shop="shop"
        :def_key="def_key"
        :tax_rate="tax_rate"
        :enrollment="enrollment"
        v-if="shop._id"
        @update:selected="selected = $event"
        :by-id="byId"
        :active-types="activeTypes"
    ></result-category>

    <div v-else class="_fw h500 row items-center justify-center">
      <ai-logo opaque :dark="dark"></ai-logo>
    </div>


    <q-dialog maximized v-model="simDialog">
      <div class="_fa flex flex-center __d" @click="simDialog = !simDialog">
        <div class="w500 mw100 q-pa-lg" @click.stop="undefined">
          <div class="row justify-center q-pb-lg">
            <ai-logo size="80px" :dark="dark"></ai-logo>
          </div>
          <div class="text-xs tw-five">We use billing data from the healthcare spend of millions of
            Americans matching your household profile and run thousands of probability simulations for each coverage
            option to see what your total spend would be - premiums + out of pocket - and rank the results you see here.
          </div>
        </div>
      </div>
    </q-dialog>

    <popup-sleeve :model-value="!!selected" @update:model-value="togglePopup">
      <div class="__popup_wrap bg-ir-bg text-ir-text _fh">
        <coverage-viewer
            :open="openCheckout"
            :age="stats.age"
            :enrolled="[{ age: stats.age, relation: 'self' }, ...stats.people]"
            :by-id="byId"
            :model-value="selected"
            :enrollment="enrollment"
        ></coverage-viewer>
      </div>
    </popup-sleeve>

    <turnstile-popup v-if="!isAuthenticated && !notabot" v-model:interactive="showTurnstile" v-model="notabot"
                     @update:model-value="resetMarket('', $event)"></turnstile-popup>

    <count-down @close="countdown = 0;" :countdown="countdown" @reset="resetMarket('', true)"></count-down>

  </div>
</template>

<script setup>
  import StatPicker from './cards/StatPicker.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import PtcDisplay from 'components/market/shop/cards/PtcDisplay.vue';
  import ResultCategory from 'components/market/shop/cards/ResultCategory.vue';
  import PopupSleeve from 'components/market/utils/PopupSleeve.vue';
  import CoverageViewer from 'components/market/shop/cards/CoverageViewer.vue';
  import TurnstilePopup from 'src/utils/turnstile/TurnstilePopup.vue';
  import ZipPicker from 'components/common/geo/pickers/ZipPicker.vue';
  import RiskProfile from 'components/market/shop/utils/RiskProfile.vue';
  import CountDown from 'components/market/shop/utils/CountDown.vue';
  import TypeManager from 'components/market/shop/utils/TypeManager.vue';

  import {dollarString} from 'symbol-syntax-utils';
  import {SessionStorage} from 'symbol-auth-client';
  import {computed, onMounted, ref, watch} from 'vue';
  import {
    getStats,
  } from 'components/market/shop/utils';
  import {countUp} from '../utils';
  import {shopGet} from 'components/market/utils/shop-get';
  import {darkness} from 'src/utils/env/darkness';
  import {useRouter} from 'vue-router';
  import {
    genCmsHousehold,
    placeManager,
    setLocation,
    isNewPlace,
    shopHousehold
  } from 'components/market/household/utils';
  import {loginPerson} from 'stores/utils/login';
  import {erSubsidy} from 'components/market/shop/utils/subsidy';
  import {$infoNotify, fakeId} from 'src/utils/global-methods';
  import {sessionFamily} from 'components/households/utils/session-family';
  import {useEnvStore} from 'stores/env';
  import {getStateCode} from 'components/common/geo/data/states';
  import {rankById, byIdObj, planTypeManager} from './utils/rank-by-id'
  import {shopResetId} from './utils/reset-id';
  import {countdownUtils} from 'components/market/shop/utils/countdown';

  const { isAuthenticated } = loginPerson()

  const envStore = useEnvStore()

  const { dark } = darkness()
  const router = useRouter();

  const props = defineProps({
    id: { default: 'AiShop' },
    plan: { required: false },
    enrollment: { required: false }
  })


  const setShop = ref()
  const { shop, shopStore, route, maybeSave, stats, setStat, hh } = shopGet({ shop: setShop });
  const { person, household: house, hhToCms } = shopHousehold(shop, hh)

  const def_key = computed(() => {
    const len = hh.value.children.length;
    if (hh.value.spouse) {
      if (len) return 'family'
      return 'plus_spouse'
    } else if (len === 1) return 'plus_child'
    else if (len) return `plus_child__${Math.min(3, len)}`
    return 'single'
  })
  const tax_rate = shop.value.plan ? .05 : 0;

  const selected = ref();
  const togglePopup = (val) => {
    if (!val) {
      selected.value = undefined;
    }
  }

  const fullPlan = computed(() => props.plan || {})
  const fullEnrollment = computed(() => props.enrollment || {});

  const loading = ref(false);

  const simDialog = ref(false);

  const byId = ref(byIdObj())

  const mult = computed(() => shop.value.mult || 12)
  const updateMult = (val) => {
    maybeSave('mult', val)
  }

  const { checkPlace, place } = placeManager(stats, (val, path) => setStat(val, path, resetMarket))

  const setZipData = (data) => {
    if (data?.fips) {
      const newLoc = setLocation(data, envStore)
      const newPlace = { state: getStateCode(newLoc.region), countyfips: newLoc.fips, zipcode: newLoc.postal }
      setStat('place', newPlace, resetMarket)
      envStore.setPlace(newPlace)
      // setTimeout(() => {
      //   console.log('next place', stats.value.place)
      // }, 500)
    }
  }

  const setById = async () => {
    byId.value = await rankById(shop.value, activeTypes.value);
  }

  const { activeTypes, toggleType } = planTypeManager(async (val) => byId.value = await rankById(shop.value, val))

  const toggleAptc = (val) => {
    if (val !== shop.value.useAptc) {
      maybeSave('useAptc', val);
      setById()
    }
  }

  const { employer, subsidy } = erSubsidy({
    plan: fullPlan,
    enrollment: fullEnrollment,
    coverageId: computed(() => shop.value.plan_coverage),
    person,
    household: house
  })

  const simsRun = computed(() => (Object.keys(shop.value?.coverage_scores || {}).length || 1) * 2.39 * (shop.value?.simsCount || 100) * 10)
  const runCountUp = () => {
    countUp({ elementId: 'SimsCount', target: simsRun.value, duration: 4000, symbol: '' })
  }
  const notabot = ref(false);
  const showTurnstile = ref(true);

  const sf = sessionFamily(envStore)

  const { getResetId, lastResetId, validPlace, resetTo } = shopResetId()

  const { countdown, interval, resetCountdown } = countdownUtils()

  const resetMarket = async (path, rerun, force) => {

    if (resetTo.value) clearTimeout(resetTo.value);
    if (interval.value) clearInterval(interval.value);

    countdown.value = 0;
    const shopId = shop.value._id || SessionStorage.getItem('shop_id') || route.params.shopId;

    resetTo.value = setTimeout(async () => {
      countdown.value = 0;
      if (interval.value) clearInterval(interval.value);
      if (!notabot.value && !isAuthenticated.value) return;
      if (route.params.shopId && !shopId) {
        if (sf.age.value) setStat('age', sf.age.value)
        if (sf.gender.value) setStat('gender', sf.gender.value)
        const addingPpl = [];
        if (sf.spouse.value) addingPpl.push({ age: sf.spouse.value[0], gender: sf.spouse.value[1], relation: 'spouse' })
        if (sf.deps.value.length) {
          for (const dep of sf.deps.value) {
            addingPpl.push({ age: dep[0], gender: dep[1], relation: 'child', child: true })
          }
        }
        if (addingPpl.length) setStat('people', addingPpl)
        if (sf.income.value) setStat('income', sf.income.value)
        setTimeout(() => resetMarket(path, rerun), 500)
        return;
      }
      loading.value = true;
      try {
        const household = house.value?._id ? {
          people: hhToCms.value,
          income: stats.value.income
        } : genCmsHousehold(stats.value);
        const household_size = (stats.value.people?.length || 1) + 1
        if (stats.value.place?.countyfips) {
          envStore.setPlace(stats.value.place);
        }
        await checkPlace()

        console.log('checked place', place.value);

        const resetId = getResetId(shop.value);
        lastResetId.value = resetId;
        const idMismatch = resetId !== lastResetId.value

        const shouldRerun = validPlace(place.value) && ((shop.value._id && !shop.value.spend_dist) || idMismatch || force)
        console.log('should rerun?', shouldRerun, path, rerun, shop.value._id);
        if (!shouldRerun) {
          console.log('no rerun')
          setById()
        } else {
          console.log('yes rerun');
          lastResetId.value = resetId;
          const planId = fullEnrollment.value?.plan || fullPlan.value?._id
          const mr = await shopStore.get(shop.value?._id || route.params.shopId || fakeId, {
            query: { $limit: 150, household, household_size, place: place.value },
            runJoin: {
              cost_sim: {
                tax_rate: planId ? .05 : 0,
                skip_aptc: !!fullPlan.value.ale,
                data: {
                  resetId,
                  person: fullEnrollment.value?.person || person.value?._id || shop.value?.person,
                  enrollment: fullEnrollment.value?._id,
                  plan: planId
                },
                stats: stats.value,
                household,
                risk: stats.value.risk || 5
              }
            }
          })
              .catch(err => {
                console.error(`Error fetching shop data: ${err.message}`)
                loading.value = false;
                return shop.value || {}
              })

          if (mr._id) {
            setShop.value = mr;
            SessionStorage.setItem('shop_id', mr._id);
            const { href } = router.resolve({ ...route, params: { ...route.params, shopId: mr._id } })
            window.history.pushState({}, '', href)
            await shopStore.get(mr._id);
            setById()
          }
        }

      } catch (e) {
        console.error(`Could not reset market: ${e.message}`)
        loading.value = false;
      } finally {
        loading.value = false;

        if (shop.value._id) {
          runCountUp()
          const { href } = router.resolve({ ...route, params: { ...route.params, shopId: shop.value._id } })
          window.history.pushState({}, '', href)
        }
        //SET STATS
        // if(shop.value._id) stats.value = getStats(shop.value?.stats)
        toggleAptc(!!shop.value?.useAptc)
      }
    }, force ? 100 : shopId ? 10000 : 200)
    if (shopId && !force) resetCountdown()
  }

  watch(() => shop.value._id, (nv, ov) => {
    if (nv && nv !== ov) {
      setById()
    }
  }, { immediate: true });


  const countedUp = ref(false);
  const senseScroll = () => {
    if (!countedUp.value) {
      const element = document.getElementById('SimsCount')
      const observer = new IntersectionObserver(
          ([entry], obs) => {
            const isVisible = entry.intersectionRatio >= 0.25;
            if (isVisible) {
              runCountUp()
              countedUp.value = true
              obs.disconnect();
            }
          },
          {
            threshold: [0.25], // Trigger when 25% of the element is visible
          }
      );

      observer.observe(element);
    }
  }

  const openCheckout = () => {
    router.push({ name: 'shop-checkout', params: { shopId: shop.value._id } })
  }
  onMounted(() => {
    senseScroll();
    if (shop.value._id) lastResetId.value = getResetId(shop.value);
    const hostId = route.query.hostId
    setTimeout(() => {
      if (shop.value._id) {
        const patchObj = {};
        let run;
        if (hostId && !shop.value.host) {
          patchObj.host = hostId;
          run = true;
        }
        if (!shop.value.person && person.value._id) {
          patchObj.person = person.value._id
          run = true;
        }
        if (run) shopStore.patch(shop.value._id, patchObj)
      }
    }, 2000)
  })
</script>

<style lang="scss" scoped>
  .__but {
    font-weight: 600;
    font-family: var(--alt-font);
    color: white;
    font-size: var(--text-sm);
    background: linear-gradient(90deg, var(--q-primary), var(--q-accent), var(--q-secondary));

  }

  .__clip {
    background: linear-gradient(90deg, var(--q-primary), var(--q-accent), var(--q-secondary));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .__popup_wrap {
    width: 100%;
    z-index: 1000;
  }

  .__aich {
    color: var(--ir-text);


    .__stats {
      width: 100%;
      color: var(--ir-text);
      //background: var(--ir-bg-grad);
      background: var(--ir-bg2);
      padding: 30px 2vw;
      margin: max(2vw, 20px) 0;
      border-radius: 10px;
    }

    .__s {
      width: 100%;
      padding: 20px 2vw;
    }

    .__second {
      position: relative;
    }

    .__dsbl {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: inherit;
      z-index: 10;
      pointer-events: none;
      background: rgba(255, 255, 255, .5);
      backdrop-filter: blur(5px);
    }

    .__ovr {
      z-index: 1;
      position: absolute;
      top: 14%;
      left: 0;
      width: 100%;
      height: 38%;
      background: linear-gradient(90deg, var(--ir-bg2), var(--ir-p), var(--ir-a), var(--ir-s), var(--ir-bg2));
    }

    .__st {
      width: 700px;
      max-width: 100%;
      z-index: 2;
    }

    .__pt {
      border-radius: 10px;
      margin-top: 60px;
      padding: min(3vw, 20px) 0;

      > div {
        height: 100%;
        width: 100%;
        //border-radius: inherit;
        background: var(--ir-bg);
      }
    }

    .__bow {
      background: var(--ir-bg-grad);
    }

    .__ptc {
      padding: 30px min(1vw, 20px);
    }

    .__allowance {
      padding: 30px min(1vw, 20px);
      padding-top: 0;
      cursor: pointer;
      font-weight: 500;

      > div {
        padding: 5px min(1vw, 15px);
      }
    }

    .__pi {
      border-radius: 10px;
      padding: 8px;
      margin: 10px 0;
    }

    .__ex {
      width: 100%;
      padding: 10px 0;

      > div {
        display: grid;
        align-items: center;
        grid-template-rows: auto auto;

        > div {
          font-size: var(--text-xxs);
          padding: 0 5px;

          &:first-child {
            font-weight: 600;
            color: var(--ir-off);
            padding: 15px 5px 5px 5px;
          }
        }
      }
    }


  }

  .__d {
    backdrop-filter: blur(5px);

    > div {
      background: var(--ir-bg2);
      color: var(--ir-text);
      border-radius: 10px;
    }
  }

  .__explore {
    padding: 30px max(2vw, 15px);
  }


</style>
