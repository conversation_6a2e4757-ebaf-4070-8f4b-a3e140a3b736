<template>
  <q-page class="bg-ir-bg1">
    <template v-if="!attested">
      <div class="row justify-center q-py-md">
        <div class="_cent pw2">
          <q-chip class="bg-ir-light text-ir-text tw-six text-xxs" clickable
                  @click="$router.push({ name: 'shop', params: { shopId: shop._id }})">
            <q-icon name="mdi-chevron-left" color="primary" class="q-mr-sm"></q-icon>
            <span>Back to analysis</span>
          </q-chip>
        </div>
      </div>
      <div class="row justify-center bg-ir-bg1">
        <div class="_sent pw2 q-pb-xl">

          <div class="row pd8">
            <div class="col-12 col-md-4" v-for="(step, i) in steps" :key="`step-${i}`">
              <div class="__step">
                <div class="row justify-center items-center">
                  <div class="__num">{{ i + 1 }}</div>
                  <div>
                    {{ step.label }}
                  </div>
                </div>
                <div v-show="i < 2">
                  <q-icon class="text-lg" name="mdi-menu-right"></q-icon>
                </div>
              </div>
            </div>
          </div>

          <div class="__c">
            <div class="__t">
              {{ steps[0].label }}
            </div>
            <policy-card
                :place="shop.stats?.place"
                v-if="policy?.acaPlan"
                :mult="shop.mult"
                :aptc="shop.aptc"
                :enrollment="enrollment"
                :dark="dark"
                :model-value="policy"
                :subsidy="subsidy"
                :enrolled="[{ age: stats.age, relation: 'self' }, ...stats.people]"

            ></policy-card>
            <coverage-page
                v-else-if="policy?._id"
                :model-value="policy"
                :dark="dark"
                :subsidy="subsidy"
                :enrollment="enrollment"
                :mult="shop.mult"
                :age="stats.age"
                :enrolled="[{ age: stats.age, relation: 'self' }, ...stats.people]"
            ></coverage-page>
            <div v-else class="row justify-center q-py-lg">
              <ai-logo size="50px" opaque></ai-logo>
            </div>

            <div class="row justify-center" v-if="shop.enrollment && employer">
              <div class="__er">
                <div>You have <span class="text-accent">{{
                    dollarString(employer * (shop.mult || 1), '$', 0)
                  }}</span><span
                    class="text-xxs tw-four">{{ shop.mult === 12 ? '/yr' : '/mo' }}</span> in allowance to spend on this
                  option
                </div>
                <div class="text-xxs tw-six">This allowance is not taken into account above because you can opt to spend
                  it
                  elsewhere
                </div>
              </div>
            </div>
          </div>
          <div class="q-py-lg"></div>
          <div class="__c">
            <div class="__t">
              {{ steps[1].label }}
            </div>
            <div class="_fw">
              <div class="row">
                <div class="w600 mw100 relative-position">
                  <div :class="`__cover ${loading ? '' : '__off'}`">
                    <ai-logo dark opaque size="50px"></ai-logo>
                  </div>
                  <div :class="`_form_grid ${person?._id ? '__off' : ''}`">
                    <div class="_form_label">Your Name</div>
                    <div class="q-pa-sm">
                      <q-input :dark="dark" placeholder="Tu nombre aqui..." v-model="personForm.name" dense class="_inp"
                               borderless></q-input>
                    </div>
                    <div class="_form_label">Your Email</div>
                    <div class="q-pa-sm">
                      <email-field :dark="dark" @update:valid="validEmail = $event" @blur="checkEmail"
                                   v-model="personForm.email" dense
                                   class="_inp"
                                   borderless></email-field>

                    </div>
                  </div>
                  <q-slide-transition>
                    <div class="_fw" v-if="person?._id">
                      <div v-if="hhChanges" class="q-pa-sm tw-five text-ir-mid text-xs">
                        ⚠️ Household profile changes have modified your premium (we updated the policy above)
                      </div>
                      <div class="__hr">
                        <div class="_fw q-px-md">
                          <household-row
                              :grey-list="(stats.people || []).filter(a => a._id && a.inactive).map(a => a._id)"
                              form size="40px"
                              :model-value="house">
                            <template v-slot:bottom="scope">
                              <template v-if="scope.member">
                                <div class="_form_label">Active Status</div>
                                <div class="q-pa-sm">
                                  <q-checkbox
                                      label="Enroll in coverage"
                                      :model-value="!!shopStatPerson(scope.member._id, stats).inactive"
                                      :true-value="false"
                                      :false-value="true"
                                      @update:model-value="setInactive(scope.member._id, $event)"
                                  ></q-checkbox>
                                </div>
                              </template>
                            </template>
                          </household-row>
                        </div>
                      </div>
                      <div class="__hhm">
                        <household-members
                            :dense="true"
                            :list-attrs="{ dark }"
                            special-change
                            :person="person"
                            :people="stats.people"
                        >
                          <template v-slot:bottom="scope">
                            <template v-if="scope.member">
                              <div class="_form_label">Active Status</div>
                              <div class="q-pa-sm">
                                <q-checkbox
                                    label="Enroll in coverage"
                                    :model-value="!!shopStatPerson(scope.member._id, stats).inactive"
                                    :true-value="false"
                                    :false-value="true"
                                    @update:model-value="setInactive(scope.member._id, $event)"
                                ></q-checkbox>
                              </div>
                            </template>
                          </template>
                        </household-members>
                      </div>
                    </div>
                  </q-slide-transition>
                </div>
              </div>
            </div>

          </div>

          <div class="q-py-lg"></div>
          <div class="__c">
            <div class="__t">
              {{ steps[2].label }}
            </div>

            <wrap-up :policy="policy" :shop="shop"></wrap-up>
          </div>
        </div>
      </div>
    </template>
    <template v-if="attested || !policy.acaPlan && person._id">
      <div class="row justify-center">
        <div class="_xsent pw2 pd5">
          <div class="__c">
            <div class="q-py-lg text-center tw-six text-accent text-sm">You're all done for now. We'll be in touch.
            </div>
            <contact-bar v-if="attested"></contact-bar>

            <div v-if="shop.enrollment" class="row justify-center q-pt-lg">
              <q-btn push color="accent" no-caps class="tw-six" @click="backToEnroll">
                <span>Return to Enrollment</span>
                <q-spinner class="q-ml-sm" color="white" v-if="enrollLoading"></q-spinner>
              </q-btn>
            </div>
          </div>
        </div>
      </div>
    </template>
  </q-page>
</template>

<script setup>
  import CoveragePage from 'components/coverages/pages/CoveragePage.vue';
  import PolicyCard from 'components/enrollments/ichra/cards/PolicyCard.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import EmailField from 'components/common/input/EmailField.vue';
  import HouseholdMembers from 'components/households/cards/HouseholdMembers.vue';
  import HouseholdRow from 'components/market/household/HouseholdRow.vue';
  import WrapUp from 'components/market/shop/closing/WrapUp.vue';
  import ContactBar from 'components/market/shop/closing/ContactBar.vue';

  import {shopGet} from 'components/market/utils/shop-get';
  import {computed, onMounted, ref, watch} from 'vue';
  import {darkness} from 'src/utils/env/darkness';
  import {placeManager, shopHousehold, shopStatPerson} from 'components/market/household/utils';
  import {usePpls} from 'stores/ppls';

  const pplStore = usePpls();
  const hhStore = useHouseholds();
  const erStore = useEnrollments();
  const planStore = usePlans();

  const router = useRouter();

  import {loginPerson} from 'stores/utils/login';
  import {$errNotify, $infoNotify} from 'src/utils/global-methods';
  import {getAgeDate} from 'src/utils/date-utils';
  import {useHouseholds} from 'stores/households';
  import {attestations} from 'components/market/shop/utils/attest';
  import {useEnrollments} from 'stores/enrollments';
  import {useRouter} from 'vue-router';
  import {erSubsidy} from 'components/market/shop/utils/subsidy';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import {dollarString} from 'symbol-syntax-utils';
  import {getCoverageRate} from 'components/coverages/utils/display';


  const { person: loginperson } = loginPerson();

  const { dark } = darkness();
  const { shop, route, maybeSave, shopStore, stats, hh } = shopGet();
  const setStat = (path, val) => {
    maybeSave(`stats.${path}`, val);
    // if (!hold) loadPlans()
  }
  const { place, checkPlace } = placeManager(stats, setStat)
  const { hhChanges, household:house, person, policy, loadPlans } = shopHousehold(shop, hh, { place, checkPlace})

  const personForm = ref({ name: '', email: '' })

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => shop.value.plan)
  })
  const { item: enrollment } = idGet({
    store: erStore,
    value: computed(() => shop.value.enrollment)
  })

  const { employer, subsidy } = erSubsidy({
    plan,
    enrollment,
    coverageId: computed(() => shop.value.plan_coverage),
    person
  })


  const loading = ref(false);
  const validEmail = ref(false);
  const checkEmail = async () => {
    if (validEmail.value) {
      loading.value = true;
      personForm.value.dob = getAgeDate(shop.value.stats.age);
      personForm.value.gender = shop.value.stats.gender;
      const prsn = await pplStore.create(personForm.value)
          .catch(err => {
            console.error(`Error adding person: ${err.message}`)
            $errNotify(`Error saving: ${err.message}`)
          })
      if (prsn) {
        const addHousehold = await hhStore.create({ person: prsn._id })
        await shopStore.patch(shop.value._id, { person: prsn._id })
            .catch(err => {
              console.error(`Error adding person to shopping session: ${err.message}`);
            })
        const newPeople = [];
        let childCount = 1;
        for (const p of stats.value.people) {
          let firstName = 'Spouse';
          const lastName = prsn.lastName;
          let age = stats.value.age;
          if (p.child) {
            firstName = `child-${childCount}`
            childCount++
            age = 10;
          }
          const newPerson = await pplStore.create({
            firstName,
            lastName,
            household: addHousehold._id,
            dob: getAgeDate(age)
          })
          newPeople.push(newPerson)
        }

        const members = {};
        for (const res of newPeople) {
          if (res._id) {
            const child = res.firstName?.includes('child')
            members[res._id] = { relation: child ? 'child' : 'spouse', dependent: !!child }
          }
        }
        await hhStore.patch(addHousehold._id, { members }, { special_change: '*' })
        pplStore.patchInStore(prsn._id, { household: addHousehold._id })
      }
      hhOpen.value = true;
      loading.value = false;
    }
  }

  const syncPremium = async (tries = 0) => {
    if (enrollment.value._id && shop.value._id) {
      const premium = policy.value.acaPlan ? policy.value.premium : getCoverageRate({
        coverage: policy.value,
        enrollment: enrollment.value
      })
      if (premium && premium !== enrollment.value.coverages[shop.value.plan_coverage].premium) await erStore.patch(enrollment.value._id, { $set: { [`coverages.${shop.value.plan_coverage}.premium`]: premium } })
    } else if (tries < 10 && shop.value.enrollment) setTimeout(() => syncPremium(tries + 1), 500);
  }
  watch(policy, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      syncPremium(0);
      if (shop.value.stage === 'shopping') shopStore.patch(shop.value._id, { stage: 'review' })
    }
  }, { immediate: true });


  const setInactive = (id, val) => {
    const ppl = stats.value.people;
    for (let i = 0; i < ppl.length; i++) {
      if (id && ppl[i]._id === id) ppl[i].inactive = val;
    }
    setStat('people', ppl);
    loadPlans()
  }

  watch(hhChanges, (nv, ov) => {
    if (nv && !ov) {
      loadPlans()
    }
  }, { immediate: true })

  const loadId = computed(() => shop.value.policy || shop.value.coverage);
  watch(loadId, async (nv, ov) => {
    if (nv && nv !== ov) {
      // console.log('shop change', nv._id, ov?._id)
      loadPlans()

      if (!nv.person && loginPerson.value) await shopStore.patch(shop.value._id, { person: loginperson.value._id })
    }
  }, { immediate: true })

  const steps = [
    {
      label: 'Confirm Plan'
    },
    {
      label: 'Add Household'
    },
    {
      label: 'Wrap things up'
    }
  ]

  const { sections } = attestations({})


  const attested = computed(() => {
    if (!shop.value?.attest) return false;
    let attest = true;
    for (const k in sections.value) {
      if (!shop.value.attest[k]?.acceptedAt) attest = false;
    }
    return attest;
  })

  const setPolicy = () => {
    const id = shop.value.policy;
    const ex = enrollment.value.coverages[shop.value.plan_coverage] || {}
    return {
      [`coverages.${shop.value.plan_coverage}`]: {
        ...ex,
        participants: [person.value._id, ...shop.value.stats.people.filter(a => !a.inactive).map(a => a._id)],
        policy: id,
        fullPolicy: policy.value,
        premium: policy.value.premium,
        aptc: { ...ex.aptc, aptc: policy.value.off_exchange ? 0 : shop.value.aptc }
      }
    }
  }

  const setCoverage = () => {
    const id = shop.value.coverage;
    const ex = enrollment.value.coverages[shop.value.plan_coverage] || {}
    return {
      [`coverages.${shop.value.plan_coverage}`]: {
        ...ex,
        participants: [person.value._id, ...shop.value.stats.people.filter(a => !a.inactive).map(a => a._id)],
        individual_coverage: id,
        fullCoverage: policy.value,
        premium: getCoverageRate({ coverage: policy.value, enrollment: enrollment.value })
      }
    }
  }

  const syncEnrollment = async (tries = 0) => {
    if(enrollment.value.status === 'complete') return;
    if(policy.value?._id) {
      if (shop.value.enrollment) {
        if (!shop.value.plan_coverage) return $infoNotify('Your enrollment is not set up for individual shop experience - talk to your plan administrator')
        const $set = shop.value.policy ? setPolicy() : setCoverage();
        await erStore.patch(shop.value.enrollment, { $set })
      }
    } else if (tries < 10) setTimeout(() => syncEnrollment(tries + 1), 500)
  }

  watch(attested, (nv, ov) => {
    if (nv && !ov) {
      syncEnrollment();
      if (shop.value.stage !== 'complete') shopStore.patch(shop.value._id, { stage: 'sign' })
    }
  }, { immediate: true })

  const enrollLoading = ref(false);
  const backToEnroll = async () => {
    enrollLoading.value = true;
    await syncEnrollment();
    enrollLoading.value = false;
    router.push({ name: 'enroll', params: { enrollId: shop.value.enrollment } })
  }

  onMounted(() => {
    const hostId = route.query.hostId
    setTimeout(() => {
      if (shop.value._id) {
        const patchObj = {};
        let run;
        if (hostId && !shop.value.host) {
          patchObj.host = hostId;
          run = true;
        }
        if (!shop.value.person && person.value._id) {
          patchObj.person = person.value._id
          run = true;
        }
        if (run) shopStore.patch(shop.value._id, patchObj)
      }
    }, 2000)
  })

</script>

<style lang="scss" scoped>

  .__step {
    width: 100%;
    position: relative;
    text-align: center;
    margin: 10px;
    font-size: var(--text-xxs);
    text-transform: uppercase;
    color: var(--ir-deep);
    font-weight: 600;

    > div {
      .__num {
        margin: 0 6px;
        height: 30px;
        width: 30px;
        display: grid;
        align-content: center;
        justify-content: center;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--q-primary), var(--q-accent));
        //background: var(--q-primary);
        color: white;
        font-weight: 600;
        font-family: var(--alt-font);
      }

      &:last-child {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translate(50%, -50%);
        color: var(--q-primary);

        opacity: .5;
      }
    }
  }

  .__c {
    padding: 55px min(20px, 3vw);
    border-radius: 12px;
    background: var(--ir-bg);
    color: var(--ir-text);
    box-shadow: 0 2px 6px var(--ir-bg2);
    position: relative;

    .__t {
      position: absolute;
      top: 0;
      left: 2.5%;
      transform: translate(0, -30%);
      border-radius: 5px;
      box-shadow: 0 2px 4px var(--ir-light);
      padding: 5px 10px;
      font-size: var(--text-sm);
      font-weight: 600;
      background: linear-gradient(135deg, var(--q-primary) 20%, var(--q-accent));
      //background: var(--q-primary);
      color: white;
    }
  }

  .__cover {
    z-index: 5;
    border-radius: 8px;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    opacity: 1;
    transition: all .3s;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(100, 100, 100, .4);
    backdrop-filter: blur(3px);
  }

  .__hhm {
    transition: all .4s;
    max-height: 2000px;
    opacity: 1;
    padding: 10px 0;
    margin: 5px 0;
    border-top: solid .3px var(--ir-mid);
  }

  .__off {
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    pointer-events: none;
    padding: 0;
    margin: 0;
  }

  .__hr {
    cursor: pointer;

    > div {
      &:first-child {
        //pointer-events: none;
      }
    }
  }

  .__er {
    margin: 10px 0;
    padding: 20px 20px;
    border-radius: 8px;
    font-size: var(--text-xs);
    font-weight: 600;
    width: 100%;
    max-width: 600px;
    background: linear-gradient(90deg, var(--ir-a), var(--ir-bg));
  }

</style>
