<template>
  <div class="_fw">
    <div class="text-lg tw-six text-ir-deep text-center">And what if...</div>
    <div class="text-center text-xs tw-five text-ir-deep">You had no coverage at all - what would it cost?</div>


    <div class="row justify-center">
      <div class="w1000 mw100">
        <div class="row justify-center">
          <div class="w500 mw100">
            <div class="text-center text-xs tw-five text-ir-deep">Let's look at 50 years of simulated spend for your
              family to show how medical expense risk really works in the U.S.
            </div>
          </div>
        </div>

        <!--        <div class="row justify-center items-center">-->
        <!--          <div class="font-1r">If you live to</div>-->
        <!--          <q-input class="q-ml-sm" style="width: 5ch" dense filled input-class="tw-six font-1-1-8r alt-font text-accent" v-model.number="liveTo"></q-input>-->
        <!--        </div>-->

        <div class="q-py-lg _fw">
          <bill-chart :shop="shop" @total="total = $event" @average="average = $event"></bill-chart>
        </div>
      </div>
    </div>
    <div class="row __nums">
      <div class="col-12 col-md-6 q-py-md pw2">
        <div class="__head">All Costs - <span class="text-primary">No</span> Coverage</div>
        <div class="__amt">
          <div>{{dollarString(total, '$', 0)}}</div>
          <div>{{dollarString(total / 50, '$', 0)}}<span>/yr</span></div>
        </div>
      </div>
      <div class="col-12 col-md-6 q-py-md pw2">
        <div class="__head">All Costs - <span class="text-accent">With</span> Coverage</div>
        <div class="__amt text-accent">
          <div>{{dollarString((average || 0) * 50, '$', 0)}}</div>
          <div>{{dollarString((average || 0), '$', 0)}}<span>/yr</span></div>
        </div>

      </div>
    </div>
    <div class="_fw text-center">
      <div class="text-sm tw-five text-ir-deep tw-six q-pb-sm">What does this data mean for you?</div>
      <div class="row justify-center text-center">
        <div class="text-xs tw-five text-ir-deep w800 mw100">
          Insurance carriers make a predictable profit. In-fact their profits are regulated. So this means if you are in the high-end of actuarial claims (medical bills) you will be one of the few who spends <i>less</i> by purchasing insurance. Otherwise, you'll spend more - but the bumps will be smoother. Here are some things to consider:
          <ul class="text-left">
            <li>The actual underlying care costs matter <b>the most</b> here.</li>
            <li>The actuarial data we use is largely priced by insurance networks (because that's what 90% of people use)</li>
            <li>Cash prices are often as much as 50% less than those prices. That means if you can spend less on care, you'll do <i>much</i> better with no coverage.</li>
            <li>To get the best of both worlds, choose an open-network policy or health share where cash prices prevail. Be protected + pay less for care.</li>
          </ul>
        </div>

        <div class="q-pt-lg text-center text-sm text-primary tw-five">We can help you beat these projections by improving your care costs. <br>Send us your bills, get wholesale meds, get a DPC physician driving your experience.</div>
      </div>
    </div>

<!--    <div class="row __nums">-->
<!--      <div class="col-12 col-md-6 q-py-md pw2">-->
<!--        <div class="__head">Costs w/DPC & <span class="text-primary">No</span> Coverage</div>-->
<!--        <div class="__amt">-->
<!--          <div>{{dollarString(years.dpcTotal, '$', 0)}}</div>-->
<!--          <div>{{dollarString(years.dpcTotal / 50, '$', 0)}}<span>/yr</span></div>-->
<!--        </div>-->
<!--      </div>-->
<!--      <div class="col-12 col-md-6 q-py-md pw2">-->
<!--        <div class="__head">Costs w/DPC & <span class="text-accent">With</span> Coverage</div>-->
<!--        <div class="__amt text-accent">-->
<!--          <div>{{dollarString((coverage_dpc || 0) * 50, '$', 0)}}</div>-->
<!--          <div>{{dollarString((coverage_dpc || 0), '$', 0)}}<span>/yr</span></div>-->
<!--        </div>-->

<!--      </div>-->
<!--    </div>-->
  </div>
</template>

<script setup>
  import BillChart from 'components/market/shop/cards/BillChart.vue';

  import {dollarString} from 'symbol-syntax-utils';
  import {computed, ref} from 'vue';

  const props = defineProps({
    shop: { required: true }
  })

  const total = ref(0)
  const average = ref(0)

  // const ageRisk = {
  //   '0': 2.35, '1': 2.34, '2': 2.33, '3': 2.32, '4': 2.31, '5': 2.31, '6': 2.3, '7': 2.29, '8': 2.29, '9': 2.28,
  //   '10': 1.02, '11': 1.02, '12': 1.02, '13': 1.01, '14': 1.01, '15': 1.01, '16': 1.0, '17': 1.0, '18': 1.0,
  //   '19': 1.0, '20': 1.0, '21': 1.0, '22': 1.0, '23': 1.0, '24': 1.0, '25': 1.01, '26': 1.01, '27': 1.01,
  //   '28': 1.02, '29': 1.02, '30': 1.02, '31': 1.03, '32': 1.04, '33': 1.04, '34': 1.05, '35': 1.06, '36': 1.06,
  //   '37': 1.07, '38': 1.08, '39': 1.09, '40': 1.1, '41': 1.11, '42': 1.12, '43': 1.13, '44': 1.14, '45': 1.16,
  //   '46': 1.17, '47': 1.18, '48': 1.2, '49': 1.21, '50': 1.22, '51': 1.24, '52': 1.26, '53': 1.27, '54': 1.29,
  //   '55': 1.31, '56': 1.32, '57': 1.34, '58': 1.36, '59': 1.38, '60': 1.4, '61': 1.47, '62': 1.58, '63': 1.72,
  //   '64': 1.88, '65': 2.07, '66': 2.26, '67': 2.48, '68': 2.71, '69': 2.95, '70': 3.21, '71': 3.47, '72': 3.75,
  //   '73': 4.05, '74': 4.35, '75': 4.66, '76': 4.98, '77': 5.32, '78': 5.66, '79': 6.01, '80': 6.37, '81': 6.74,
  //   '82': 7.12, '83': 7.51, '84': 7.9, '85': 8.31, '86': 8.72, '87': 9.14, '88': 9.56, '89': 10.0, '90': 10.44,
  //   '91': 10.89, '92': 11.35, '93': 11.81, '94': 12.28, '95': 12.76, '96': 13.24, '97': 13.74, '98': 14.23,
  //   '99': 14.74, '100': 15.25
  // }



  // const liveTo = ref(100)
  // const age = computed(() => props.shop?.stats?.age || 30)



  // const coverages = computed(() => {
  //   const obj = props.shop?.coverage_scores || {}
  //   const list = [];
  //   for(const k in obj){
  //     list.push({...obj[k], _id: k })
  //   }
  //   return list.sort((a, b) => a.average - b.average)
  // })
  //
  // const coverage_dpc = computed(() => {
  //   const dpc_cost = dpcCost.value;
  //   const average = coverages.value[0]?.average || 0
  //   const oop = average - (coverages.value[0]?.premium || 0)
  //   const lessVisits = oop * .21;
  //   const lessSavings = lessVisits * .5;
  //   return average - lessVisits - lessSavings + dpc_cost;
  // })

</script>

<style lang="scss" scoped>


  .__nums {
    padding: 5vh 0;
  }

  .__head {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--ir-mid);
    text-align: center;

  }
  .__amt {
    color: var(--q-p5);

    > div {
      font-weight: 600;
      font-family: var(--alt-font);
      text-align: center;

      &:first-child {
        font-size: var(--text-lg);
      }
      &:nth-child(2) {
        font-size: var(--text-xs);
        > span {
          font-size: var(--text-xxs);
          font-weight: 500;
        }
      }
    }
  }

  ul {
    padding: 20px 3vw;

    li {
      padding: 5px 0;
    }
  }
</style>
