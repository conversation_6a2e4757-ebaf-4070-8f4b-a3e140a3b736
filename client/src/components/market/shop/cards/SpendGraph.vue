<template>
  <div class="_fw">
    <div v-for="(g, i) in graphs" :key="`g-${i}`" class="__g">
      <div>
        {{ g[1] }}
      </div>
      <div class="_fw">
        <div :style="{ maxWidth: g[2] * 100 + '%', background: `var(--q-${colors[i]})` }">
        </div>
        <div class="__label">{{ dollarString(g[2] * 100, '', 0) }}% of spend | {{ dollarString(g[3], '$', 0) }} avg
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>

  import {computed} from 'vue';
  import {dollarString} from 'src/utils/global-methods';

  const props = defineProps({
    allSpend: { default: 1 },
    totals: {
      default: () => {
        return {}
      }
    }
  })

  const graphs = computed(() => {
    const t = props.totals || {};
    const def = () => {
      return { count: 1, spend: 0 }
    }
    const all = props.allSpend || 1;
    const one = t['1'] || def();
    const five = t['5'] || def();
    const ten = t['10'] || def();
    const tw = t['20'] || def();
    const fifty = t['50'] || def();
    const zero = t['0'] || def();
    return [
      ['0', 'Bot 50%', zero.spend / all, (zero.spend || 0) / (zero.count || 1)],
      ['50', 'Top 50%', fifty.spend / all, (fifty.spend || 0) / (fifty.count || 1)],
      ['20', 'Top 20%', tw.spend / all, (tw.spend || 0) / (tw.count || 1)],
      ['10', 'Top 10%', ten.spend / all, (ten.spend || 0) / (ten.count || 1)],
      ['5', 'Top 5%', five.spend / all, (five.spend || 0) / (five.count || 1)],
      ['1', 'Top 1%', one.spend / all, (one.spend || 0) / (one.count || 1)]
    ]
  })
  const colors = ['primary', 'accent', 'accent', 'secondary', 'secondary', 's7']
</script>

<style lang="scss" scoped>

  .__g {
    width: 100%;
    padding: 0 5px;
    display: grid;
    grid-template-columns: 40px 1fr;
    align-items: center;

    > div {
      &:first-child {
        font-size: var(--text-xxs);
      }

      &:nth-child(2) {
        padding: 10px 5px;
        position: relative;

        > div {
          &:first-child {
            border-radius: 5px;
            height: 50px;
            width: 100%;
            max-width: 0;
            transition: all .5s ease-out;
            min-width: 2%;
            position: relative;
            overflow: visible;
            opacity: .5;
          }

          &:nth-child(2) {
            position: absolute;
            top: 50%;
            left: 20px;
            transform: translate(0, -50%);
            color: var(--ir-off);
            font-size: var(--text-xxs);
            font-weight: 600;
            text-wrap: nowrap;
          }
        }
      }
    }
  }
</style>
