<template>
  <div class="__chart">
    <div v-for="(yr, i) in years.all" :key="`yr-${i}`"
         :style="`width: ${years.barWidth}; height: ${years.heights[i]}%; margin-right: ${years.gapWidth}; background: var(--q-${getColor(i)})`">
      <div v-show="$q.screen.gt.sm || (i+1) % 5 === 0">{{ i + 1 }}</div>
      <q-tooltip class="tw-six text-xxs">{{ dollarString(yr.value, '$', 0) }}</q-tooltip>
    </div>
  </div>
</template>

<script setup>

  import {dollarString} from 'symbol-syntax-utils';
  import {computed, ref, watch} from 'vue';

  const emit = defineEmits(['total', 'average']);
  const props = defineProps({
    shop: Object,
    dpcCost: Number
  })

  const getColor = (idx) => {
    const pct = years.value.byIdx[idx] || 0;
    if (pct < 5) return 'secondary'
    if (pct < 25) return 'accent'
    return 'primary'
  }

  const floor = (v) => {
    return Math.floor(v * 100)
  }


  const years = ref({
    all: [],
    length: 0,
    heights: [],
    barWidth: '0%',
    gapWidth: '0%',
    byIdx: {},
    total: 0,
    dpcTotal: 0
  })

  const dpcCost = computed(() => {
    const costs = {
      1: 95,
      2: 150,
      3: 210,
      4: 260,
      5: 310,
      6: 370,
      7: 400,
      8: 400,
      9: 400,
      10: 400,
      11: 400,
      12: 400
    }
    return (costs[(props.shop?.stats?.people || []).length + 1] || 300) * 12
  })

  const coverages = computed(() => {
    const obj = props.shop?.coverage_scores || {}
    const list = [];
    for(const k in obj){
      list.push({...obj[k], _id: k })
    }
    return list.sort((a, b) => a.average - b.average)
  })

  const setYears = () => {
    const arr = [];
    let total = 0;
    let dpcTotal = 0;
    for (let i = 0; i < 50; i++) {
      const v = bills.value[i] || 0
      total += v;
      const wdpc = ((v * .79) * .5) + dpcCost.value
      dpcTotal += wdpc;
      arr.push({ value: v, idx: i, wdpc })
    }
    const sorted = arr.slice(0).sort((a, b) => b.value - a.value)
    const byIdx = {}
    for (let i = 0; i < sorted.length; i++) byIdx[sorted[i].idx] = i;
    emit('total', total)
    emit('average', coverages.value[0]?.average || 0)
    years.value = {
      total,
      dpcTotal,
      byIdx,
      all: arr,
      length: arr.length,
      heights: arr.slice(0).map((a) => floor(a.value / sorted[0].value)),
      barWidth: (100 / arr.length) * .75 + '%',
      gapWidth: (100 / arr.length) * .25 + '%'
    }
  }

  const bills = computed(() => ((props.shop || {}).byYear || []).slice(0, 50))

  watch(bills, (nv, ov) => {
    if (nv && !ov || ((nv[0] !== ov[0]) || nv[5] !== ov[5])) setYears()
  }, { immediate: true })


</script>

<style lang="scss" scoped>
  .__chart {
    width: 100%;
    height: 300px;
    max-height: 50vw;
    display: flex;
    align-items: flex-end;
    border-bottom: solid 2px var(--ir-mid);

    > div {
      background: blue;
      border-radius: 3px 3px 0 0;
      position: relative;

      > div {
        position: absolute;
        bottom: 0%;
        left: 50%;
        transform: translate(-50%, 100%);
        font-size: .7rem;
        color: var(--ir-mid);
        font-family: var(--alt-font);
      }
    }
  }
</style>
