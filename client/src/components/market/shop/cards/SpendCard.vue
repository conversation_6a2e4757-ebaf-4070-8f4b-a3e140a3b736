<template>
  <div class="_fw">

    <div class="__top">
      <div>{{ mv.carrierName }}</div>
      <div v-if="logo" class="q-pl-sm">
        <q-img class="h15 w15" :src="logo">
        </q-img>
      </div>
    </div>
    <div class="__t alt-font">{{ shortName }}
      <q-tooltip class="text-xxs tw-six">{{ mv.name }}</q-tooltip>
    </div>

    <div class="__num">
      <div>
        <div>Premium ({{ mult === 12 ? 'annual' : 'monthly' }}):</div>
        <div class="__amt text-primary">{{ dollarString(prm * (mult || 1), '$', 0) }}</div>
      </div>

      <div class="__ded">
        <div>Deductible (single/family)</div>
        <div>
          {{ dollarString(scores.deductible?.single, '$', 0) }}/
          {{ dollarString(scores.deductible?.family, '$', 0) }}
        </div>
      </div>

<!--      <div>-->
<!--        <div>Max OOP (single/family)</div>-->
<!--        <div>-->
<!--          {{ dollarString(mv._stats.moop.single, '$', 0) }}/-->
<!--          {{ dollarString(mv._stats.moop.family, '$', 0) }}-->
<!--        </div>-->
<!--      </div>-->

      <div>
        <div>Your OOP (@ {{dollarString(scores.spend, '$', 0)}} /yr bills):</div>
        <div class="__amt text-secondary">{{ dollarString(scores.oop * ((mult || 1)/12), '$', 0) }}</div>
      </div>
      <div v-if="taxDed" class="__ded">
        <div>Tax Savings:</div>
        <div class="text-ir-light">{{ dollarString(scores.tax_savings * ((mult || 1)/12), '$', 0) }}</div>

      </div>
      <div>
        <div>Total cost:</div>
        <div>
          <span class="__amt text-a4">{{ dollarString(scores[useAptc ? 'total_ptc' : 'total'] * ((mult || 1)/12), '$', 0) }}</span>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
  import {computed, ref, watch} from 'vue';
  import {useUploads} from 'stores/uploads';
  import {dollarString} from 'symbol-syntax-utils';
  import {getCoverageRate} from 'components/coverages/utils/display';

  const uploadStore = useUploads()

  const props = defineProps({
    mult: Number,
    modelValue: { required: true },
    dark: Boolean,
    aptc: Number,
    useAptc: Boolean,
    def_key: String,
    def_age: Number,
    tax_rate: Number,
    scores: { default: () => {
        return {}
      }
    }
  })
  const mv = computed(() => {
    return props.modelValue || {}
  })

  const actualAptc = computed(() => {
    if(mv.value.off_exchange) return 0;
    if(!props.useAptc || !mv.value?.acaPlan) return 0;
    return Math.min(mv.value.premium || 0, props.aptc || 0);
  })

  const premium = computed(() => getCoverageRate({ coverage: mv.value, def_age: props.def_age, def_key: props.def_key }))

  const prm = computed(() => {
    if(!mv.value) return 0;
    return premium.value - actualAptc.value;
  })

  const taxDed = computed(() => {
    if(!mv.value) return 0;
    if(actualAptc.value && mv.value.acaPlan) return 0;
    if(mv.value.type === 'hs') return 0;
    if(!props.tax_rate) return 0;
    return prm.value * ((props.tax_rate || 0) + .153);
  })

  const uploadId = ref('')
  const upload = ref({})
  watch(mv, async (nv) => {
    if(nv){
      const uid = nv.carrierLogo?.uploadId || '';
      if(uid !== uploadId.value) {
        uploadId.value = uid;
        if (uid) {
          const inStore = uploadStore.getFromStore(uid);
          if (inStore?.value) upload.value = inStore.value;
          else {
            const ul = await uploadStore.get(uid);
            upload.value = ul || {};
          }
        } else upload.value = {}
      }
    }
  }, { immediate: true })


  const logo = computed(() => {
    const url = upload.value?.url || mv.value.carrierLogo
    if (typeof url === 'string') return url
    return ''
  })

  const shortName = computed(() => {
    if (!mv.value.carrierName) return mv.value.name;
    else {
      const regex = new RegExp(mv.value.carrierName, 'gi');
      return mv.value.name.replace(regex, '').split('  ').join(' ')
    }
  })
</script>

<style lang="scss" scoped>
  .__top {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: end;
    font-size: var(--text-xxs);
    font-weight: 600;
    color: var(--ir-deep);

    > div {

      &:first-child {
        text-align: left;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: var(--text-xxs);
      }
    }
  }

  .__t {
    font-size: var(--text-xxs);
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    color: var(--ir-mid);
  }

  .__num {
    line-height: 1.2rem;
    font-size: var(--text-xxs);
    font-weight: 400;
    padding: 3px 0;
    width: 100%;

    > div {
      padding: 5px;
      width: 100%;
      display: grid;
      grid-template-columns: auto 1fr;
      border-bottom: solid .3px var(--ir-light);

      &:last-child {
        border-bottom: none;
      }

      > div {
        &:last-child {
          color: var(--ir-deep);
          font-weight: 600;
          text-align: right;
        }
      }
      .__amt {
        font-weight: 600;
        font-size: var(--text-xs);
        font-family: var(--alt-font);
      }
    }
  }

</style>
