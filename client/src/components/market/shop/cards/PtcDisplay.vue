<template>
  <div class="__ptc">
    <div @click="show = !show" class="flex items-center">
      <div :class="`text-${sm} tw-five`">
        <q-chip :class="`text-${sm} tw-five bg-p4 text-white`">Your premium tax credit:</q-chip>
      </div>
      <div :class="`q-px-sm tw-six text-${lg} text-primary alt-font`">{{ dollarString(ptc, '$', 0) }}<span :class="`tw-four text-${sm}`">{{ mult === 12 ? '/yr' : '/mo' }}</span>
        <q-icon size="18px" class="q-ml-sm" color="p4" name="mdi-information"></q-icon>
      </div>
    </div>
    <div class="row items-center q-py-sm">
      <q-checkbox :disable="loading" :dark="dark" :model-value="modelValue" @update:model-value="emitUp"></q-checkbox>
      <div @click="emitUp(!modelValue)" :class="`tw-six text-${xs} text-ir-deep`"> include in outcomes below <span
          v-if="modelValue" class="text-primary">(YES)</span><span v-else class="text-secondary">(NO)</span></div>
    </div>

    <div class="row q-pt-sm" v-if="modelValue">
      <div class="flex items-center no-wrap q-px-sm">
        <q-avatar :color="productTypes['aca'].color" size="20px"></q-avatar>
        <div :class="`q-pl-md text-${xs} tw-five`">
          <q-chip :class="`text-${xs} tw-six`" color="ir-pink" dense dark square>These</q-chip>plans are eligible for the premium tax credit
        </div>
      </div>
    </div>
  </div>

  <common-dialog setting="smmd" v-model="show">
    <div :class="`__bg ${dark ? '__dark' : '__light'}`">
      <q-checkbox :disable="loading" class="q-mb-md text-xxs tw-six text-ir-deep" label="Include the PTC in analysis"
                  :model-value="modelValue" @update:model-value="emitUp"></q-checkbox>
      <q-list separator>
        <q-expansion-item content-inset-level=".5" dense expand-icon="mdi-menu-down" v-for="(item, i) in items"
                          :key="`item-${i}`">
          <template v-slot:header>
            <q-item class="_fw">
              <q-item-section>
                <q-item-label class="font-1r tw-six text-ir-deep">{{ item.label }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
          <div class="q-pb-md font-7-8r tw-five text-ir-text" v-html="item.text"></div>
        </q-expansion-item>
      </q-list>
    </div>
  </common-dialog>

</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {dollarString} from 'symbol-syntax-utils';
  import {computed, ref} from 'vue';
  import {productTypes} from 'components/market/utils';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    mult: Number,
    ptc: { default: 0 },
    modelValue: Boolean,
    dark: Boolean,
    loading: Boolean,
    sm: { default: 'sm' },
    xs: { default: 'xs' },
    lg: { default: 'lg' }
  })

  const emitUp = (val) => {
    emit('update:model-value', val);
  }

  const show = ref(false);

  const items = computed(() => [
    {
      label: 'What\'s the premium tax credit (PTC)?',
      text: 'Under the Affordable Care Act (ACA) federal subsidies are provided to pay your health insurance premiums based on your income level and household details.'
    },
    {
      label: 'How do I claim it?',
      text: `When you enroll in an eligible plan, the carrier receives the credit directly to reduce your premium. You simply get a 1095 tax form at the end of the year disclosing how much credit was claimed. <div class="q-pt-sm">${dollarString(props.ptc, '$', 0)} is your credit amount based on the household info you provided. If this data is inaccurate, your credit could change - causing you to miss funds or to over-claim, resulting in you owing at tax time.</div>`
    },
    {
      label: 'Who should use it?',
      text: 'Anyone who can find a marketplace-eligible plan that meets their needs and who is reasonably confident their household income won\'t be significantly more than listed here for the year should likely use their PTC.'
    },
    {
      label: 'Who should NOT?',
      text: 'Anyone who\'s household income may increase substantially during the year may not want to use their PTC. Also anyone who doesn\'t find the marketplace plans worthwhile due to network constraints - and would do better to opt for a private plan or health share. <div class="q-pt-sm">This is why our simulations are valuable - you can see the true cost of each plan type including the premium tax credit. </div>'
    }
  ])
</script>

<style lang="scss" scoped>

  .__ptc {
    width: 100%;
    display: grid;
    grid-template-columns: 100%;
    align-items: center;
    cursor: pointer;

    > div {
      padding: 5px min(1vw, 15px);
    }
  }

  .__bg {
    padding: 30px 20px;
    font-size: .8rem;
    font-weight: 500;
  }

  .__dark {
    background: linear-gradient(90deg, var(--q-p12), var(--q-a12), var(--q-s12));
    color: white !important;
  }

  .__light {
    background: linear-gradient(90deg, var(--q-p0), var(--q-a0), var(--q-s0));
    color: black !important;

  }

  ._form_label {
    color: var(--ir-deep) !important;
  }

  .__clip {
    background: linear-gradient(90deg, var(--q-primary), var(--q-accent), var(--q-secondary));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
</style>
