<template>
  <div :id="id" class="_fw">
    <!--          ******STATS*******-->
    <div class="row">
      <div class="col-12">
        <div class="row">
          <div class="col-12" v-if="!household?._id">
            <div class="row">
              <div class="col-12 q-pa-xs">
                <!--                AGE-->
                <div :class="statClass">
                  <div class="text-xxs tw-six">Age:</div>
                  <div>{{ stats.age }}
                    <q-icon v-bind="icons"/>
                    <q-menu :dark="dark">
                      <div class="w200 mw100">
                        <q-list separator>
                          <q-item v-for="i in 50" :key="`age-${i}`" clickable @click="setAndSpouse('age', i + 17)">
                            <q-item-section>
                              <q-item-label class="tw-six font-1r">{{ i + 17 }}</q-item-label>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </div>
                    </q-menu>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-12 q-pa-xs">
                <!--                SPOUSE-->
                <div :class="statClass">
                  <div>Spouse:</div>
                  <div>
                    <div class="flex items-center">
                      <q-checkbox
                          :dark="dark"
                          :model-value="!!hh.spouse?.age"
                          @update:model-value="toggleSpouse"></q-checkbox>
                      <div v-if="!!hh.spouse?.age">{{ hh.spouse.age }}
                        <q-icon v-bind="icons"/>
                        <q-menu :dark="dark">
                          <div class="w200 mw100">
                            <q-list separator>
                              <q-item v-for="i in 50" :key="`s-age-${i}`" clickable @click="setSpouseAge(i + 17)">
                                <q-item-section>
                                  <q-item-label class="tw-six font-1r">{{ i + 17 }}</q-item-label>
                                </q-item-section>
                              </q-item>
                            </q-list>
                          </div>
                        </q-menu>
                      </div>

                    </div>


                  </div>
                </div>
              </div>
              <div class="col-12 q-pa-xs">
                <!--                DEPENDENTS-->
                <div :class="statClass">
                  <div>Dependents:</div>
                  <div>
                    {{ hh.children.length }}
                    <q-icon v-bind="icons"/>
                    <q-menu :dark="dark">
                      <div class="w200 mw100">
                        <q-list separator>
                          <q-item v-for="i in 10" :key="`plus-${i}`" clickable @click="setDeps(i - 1)">
                            <q-item-section>
                              <q-item-label class="font-1r tw-six">{{ i - 1 }}</q-item-label>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </div>
                    </q-menu>
                  </div>
                </div>
              </div>

            </div>
          </div>
          <div v-else class="col-12 q-pa-xs">
            <div :class="statClass">
              <div>Household:</div>
              <div>
                <household-row form @update:model-value="setHh" :model-value="household"
                               :grey-list="(stats.people || []).filter(a => a._id && a.inactive).map(a => a._id)">
                  <template v-slot:bottom="scope">
                    <template v-if="scope.member">

                      <div class="_form_label">Active Status</div>
                      <div class="q-pa-sm">
                        <q-checkbox
                            label="Enroll in coverage"
                            :model-value="!!shopStatPerson(scope.member._id, stats).inactive"
                            :true-value="false"
                            :false-value="true"
                            @update:model-value="setInactive(scope.member._id, $event)"
                        ></q-checkbox>
                      </div>
                    </template>
                  </template>
                </household-row>
              </div>
            </div>
          </div>
          <div class="col-12">

            <div class="row">

              <div class="col-12 q-pa-xs">
                <div :class="statClass">
                  <div>Household Income:</div>
                  <div>
                    <q-input
                        :dark="dark"
                        dense
                        :model-value="dollarString(setIncome, '$', 0)"
                        @update:model-value="setIncome = Number($event?.replace(/[^\d.]/g, '') || 0)"
                        @blur="setStat('income', setIncome, reload)"
                        class="w150 mw100"
                        borderless
                        input-class="__inp text-sm tw-six text-right"
                    ></q-input>
                  </div>
                </div>

              </div>
              <!--              <div class="col-12 q-pa-xs">-->
              <!--                <div class="__stat">-->
              <!--                  <div>Pre-exising Conditions</div>-->
              <!--                  <div>-->
              <!--                    <q-checkbox :model-value="!!stats.preEx" @update:model-value="setStat('preEx', $event)"></q-checkbox>-->
              <!--                  </div>-->
              <!--                </div>-->
              <!--              </div>-->

            </div>

          </div>
        </div>
      </div>

    </div>

    <div class="row items-center justify-end q-pa-sm">
      <div class="q-mr-md" v-if="!household?._id">
        <gender-picker :model-value="stats.gender" @update:model-value="setAndSpouse('gender', $event)"></gender-picker>
      </div>
      <smoker-toggle :model-value="stats.smoker" @update:model-value="setAndSpouse('smoker', $event)"></smoker-toggle>
    </div>

    <!--    <div class="__hh">-->
    <!--      <div class="text-xxs tw-six">Total Household Size:-->
    <!--        <q-icon name="mdi-information" color="accent" class="_i_i"></q-icon>-->
    <!--        <q-tooltip>-->
    <!--          <div class="w300 mw100 text-xxs tw-five">For calculating an accurate premium tax credit, list your household-->
    <!--            size even if members are not going to enroll on the plan.-->
    <!--          </div>-->
    <!--        </q-tooltip>-->
    <!--      </div>-->
    <!--      <q-chip :dark="dark" class="text-xs" :color="dark ? '' : 'ir-grey-2'" clickable square>-->
    <!--        {{ stats.household_size }}-->
    <!--        <q-icon class="q-ml-sm" name="mdi-menu-down"/>-->
    <!--        <q-menu :dark="dark">-->
    <!--          <div class="w200 mw100">-->
    <!--            <q-list separator>-->
    <!--              <q-item v-for="i in 10" :key="`plus-${i}`" clickable @click="setStat('household_size', i - 1, reload)">-->
    <!--                <q-item-section>-->
    <!--                  <q-item-label class="font-1r tw-six">{{ i }}</q-item-label>-->
    <!--                </q-item-section>-->
    <!--              </q-item>-->
    <!--            </q-list>-->
    <!--          </div>-->
    <!--        </q-menu>-->
    <!--      </q-chip>-->
    <!--    </div>-->


  </div>
</template>

<script setup>
  import SmokerToggle from 'components/market/utils/SmokerToggle.vue';
  import GenderPicker from 'components/market/household/GenderPicker.vue';
  import HouseholdRow from 'components/market/household/HouseholdRow.vue';

  import {dollarString} from 'src/utils/global-methods';
  import {computed, ref, watch} from 'vue';
  import {darkness} from 'src/utils/env/darkness';
  import {shopGet} from 'components/market/utils/shop-get';
  import {getChild, getSpouse} from 'components/market/shop/utils';
  import {shopHousehold, shopStatPerson} from 'components/market/household/utils';
  import {taxBreakdown} from 'components/households/utils/tax-tables';

  const emit = defineEmits(['update:model-value', 'reload']);
  const { dark } = darkness();

  const props = defineProps({
    modelValue: Object,
    id: { default: 'TheStatPicker' }
  })
  const icons = { size: '18px', name: 'mdi-menu-down', class: '_i_i' }

  const statClass = computed(() => dark.value ? '__stat __dark' : '__stat')

  const shop = computed(() => props.modelValue);
  const { stats, setStat, hh } = shopGet({ shop });
  const { actualHh, household, person, hhChanges } = shopHousehold(shop, hh)

  const reload = (path) => emit('reload', path);

  const setDeps = (n) => {
    const len = hh.value.children.length
    if (n < len) {
      const newHh = [];
      if (actualHh.value.spouse) newHh.push(actualHh.value.spouse);
      for (let i = 0; i < actualHh.value.children.length; i++) {
        if (i < n) newHh.push(actualHh.value.children[i]);
      }
      setStat('people', newHh, reload);
    } else if (n !== len) {
      const arr = [...stats.value.people || []];
      for (let i = 0; i < n - len; i++) {
        arr.push(getChild())
      }
      setStat('people', arr, reload);
    }
  }

  const toggleSpouse = (val) => {
    if (val && !actualHh.value.spouse) {
      const arr = [...stats.value.people || []];
      arr.push(getSpouse(stats.value))
      setStat('people', arr);
      setTimeout(() => {
        reload('people')
      }, 500)
    } else if (!val && actualHh.value.spouse) {
      const arr = [...actualHh.value.children]
      setStat('people', arr);
      setTimeout(() => {
        reload('people')
      }, 500)
    }
  }

  const setSpouseAge = (val) => {
    const age = actualHh.value.spouse?.age || stats.value.age;
    if (val !== age) {
      for (let i = 0; i < stats.value.people.length; i++) {
        if (stats.value.people[i].age >= age) {
          stats.value.people[i].age = val;
          break;
        }
      }
      setStat('people', stats.value.people);
      setTimeout(() => {
        reload('people')
      }, 500)
    }
  }

  const setIncome = ref(0);
  watch(() => stats.value.income, (nv) => {
    if (nv) {
      setIncome.value = nv || 60000;
    }
  }, { immediate: true })

  const setInactive = (id, val) => {
    const ppl = stats.value.people;
    for (let i = 0; i < ppl.length; i++) {
      if (id && ppl[i]._id === id) ppl[i].inactive = val;
    }
    setStat('people', ppl);
    setTimeout(() => {
      reload('people')
    }, 500)
  }

  const setAndSpouse = (path, v) => {
    if (['smoker', 'gender'].includes(path) && actualHh.value.spouse) {
      const arr = [...stats.value.people || []];
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].age === actualHh.value.spouse.age) {
          arr[i][path] = path === 'gender' ? v === 'male' ? 'female' : 'male' : v;
        }
      }
      setStat('people', arr);
    }
    setStat(path, v, reload);
  }

  const setHh = () => {
    if (hhChanges.value) {
      const arr = [...actualHh.value.children];
      if (actualHh.value.spouse) arr.push(actualHh.value.spouse);
      for (const k of ['age', 'smoker', 'gender']) {
        if (actualHh.value.self[k] !== stats.value[k]) setStat(k, actualHh.value.self[k]);
      }
      setStat('people', arr, reload);
    }
  }

  watch(household, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      setHh()
      if (nv.magi) setStat('income', nv.magi, reload);
      else {
        const income = taxBreakdown(nv, {})?.magi

        if (income > stats.value.income) setStat('income', income, reload)
        if (person.value?.gender) setStat('gender', person.value.gender, reload)
      }
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>

  .__inp {
    color: var(--stat-txt);
  }

  .__stat {
    width: 100%;
    display: grid;
    border-radius: 3px;
    grid-template-columns: 1fr auto;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    height: 45px;
    background: rgba(255, 255, 255, 0.7);
    color: var(--ir-text);
    backdrop-filter: blur(10px);
    padding: 2px 10px;

    > div {
      &:first-child {
        height: 100%;
        display: flex;
        align-items: center;
        text-align: left;
        font-size: var(--text-xxs);
      }

      &:nth-child(2) {
        font-size: var(--text-sm);
        cursor: pointer;
      }
    }
  }

  .__hh {
    color: var(--ir-text);
    padding: 2px 10px;
    width: 100%;
    display: grid;
    align-items: center;
    grid-template-columns: 1fr auto;
  }

  .__dark {
    background: rgba(255, 255, 255, 0.2);
  }
</style>
