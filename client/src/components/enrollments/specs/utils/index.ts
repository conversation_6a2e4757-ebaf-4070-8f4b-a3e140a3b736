import HsaChange from 'components/enrollments/specs/forms/HsaChange.vue';
import HouseholdChange from 'components/enrollments/specs/forms/HouseholdChange.vue';
import HouseholdChangeViewer from 'components/enrollments/specs/cards/HouseholdChangeViewer.vue';
import CafeChangeViewer from 'components/enrollments/specs/cards/CafeChangeViewer.vue';

import {dollarString} from 'src/utils/global-methods.js';
import RequestSpecialEnrollment from 'components/enrollments/specs/forms/RequestSpecialEnrollment.vue';


export const qEvents = {
    'hsa': {
        name: 'HSA Changes',
        description: 'You can change your HSA contribution elections anytime.',
        component: HsaChange,
    },
    'household': {
        name: 'Household Changes',
        description: 'Family changes due to birth, death, or legal changes.',
        component: HouseholdChange,
    },
    'employment': {
        name: 'Job Changes',
        description: 'New hires, changes in hours, position, or other eligibility.',
        component: RequestSpecialEnrollment
    },
    'plan': {
        name: 'Plan Changes',
        description: 'Changes in coverage, geography, or spouse\'s plan.',
        component: RequestSpecialEnrollment

    }
}

export const specPaths = {
    'cafe.*.*': {
        label: 'HSA Amount Change',
        format: (val: number) => dollarString(val, '$', 0),
        component: CafeChangeViewer
    },
    'coverages.*.participants': {
        label: 'Add/Remove Participants',
        format: () => 'See Household For Details',
        component: HouseholdChangeViewer
    }
}

export const getSpec = (p:string) => {
    if(specPaths[p as keyof typeof specPaths]) return specPaths[p as keyof typeof specPaths];
    else if(/cafe\./.test(p)) return specPaths['cafe.*.*']
    else if(/coverages\./.test(p)) return specPaths['coverages.*.participants']
}

export const statuses = {
    'approved': {
        color: 'green'
    },
    'rejected': {
        color: 'red-7'
    },
    'pending': {
        color: 'blue'
    },
    'archived': {
        color: 'grey-8'
    }
}
