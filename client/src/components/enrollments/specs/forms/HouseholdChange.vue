<template>
  <div class="_fw">
    <div class="row items-center q-py-sm">
      <q-chip clickable @click="panel = 'base'" color="white" class="tw-six">Household Changes</q-chip>
      <template v-if="panel === 'cafe'">
        <q-icon name="mdi-chevron-right"></q-icon>
        <q-chip class="tw-six" color="white">Cafeteria Elections</q-chip>
      </template>
    </div>
    <q-tab-panels class="_panel" v-model="panel" animated>
      <q-tab-panel class="_panel" name="base">

        <div class="text-grey-8 q-py-sm tw-six">Qualifying Event</div>
        <q-list separator dense>
          <q-item v-for="(evt, i) in Object.keys(events).filter(a => events[a][tab])" :key="`evt-${i}`" clickable
                  @click="setForm('description', evt)">
            <q-item-section avatar>
              <q-icon v-if="form.description === evt" color="green" name="mdi-check"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ evt }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>

        <div class="text-grey-8 q-py-sm tw-six">Details</div>
        <q-input class="q-my-sm" filled autogrow placeholder="Include more information on this event..."
                 v-model="form.message"></q-input>
        <q-btn-group rounded class="q-mb-sm q-mt-md">
          <q-btn @click="setTab('remove')" color="p6" :flat="tab !== 'remove'" class="tw-six" no-caps
                 label="Remove Members"></q-btn>
          <q-btn @click="setTab('add')" color="p6" :flat="tab !== 'add'" class="tw-six" no-caps
                 label="Add Members"></q-btn>
        </q-btn-group>
        <q-tab-panels class="_panel" v-model="tab" animated transition-next="jump-up" transition-prev="jump-right">
          <q-tab-panel class="_panel" name="remove">
            <q-list separator>
              <q-item-label header>Remove Household Member</q-item-label>
              <member-item
                  clickable
                  @click="select(mbr)"
                  v-for="(mbr, i) in enrolled"
                  :key="`mbr-${i}`"
                  :model-value="mbr"
              ></member-item>
            </q-list>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="add">
            <q-list separator>
              <q-item-label header>Add/Enroll Household Member</q-item-label>
              <member-item
                  clickable
                  @click="select(mbr)"
                  v-for="(mbr, i) in dormant"
                  :key="`mbr-${i}`"
                  :model-value="mbr"
              ></member-item>
              <q-expansion-item hide-expand-icon>
                <template v-slot:header>
                  <q-item class="_fw">
                    <q-item-section avatar>
                      <q-icon name="mdi-plus" color="primary"></q-icon>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label class="text-grey-8 font-1r">Add Household Member</q-item-label>
                      <q-item-label caption>
                        Due to birth or legal event - like marriage or adoption
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
                <member-form :household="household" @update:model-value="add"></member-form>
              </q-expansion-item>
            </q-list>
          </q-tab-panel>
        </q-tab-panels>

        <q-slide-transition>
          <div class="_fw" v-if="list[tab].length">
            <div class="_fw">
              <div class="text-grey-8 q-py-sm tw-six q-mt-md">{{ $capitalizeFirstLetter(tab) }}({{ list[tab].length }})
              </div>
              <div class="q-my-sm row items-center">
                <member-chip show-age v-for="(mbr, i) in list[tab]" :key="`lir-${i}`" :model-value="mbr" removable
                             icon-remove="mdi-close" @remove="select(mbr)"></member-chip>
              </div>

              <div class="text-grey-8 q-pt-lg q-pb-md tw-six" v-if="tab === 'remove'">
                {{ nameList }} will be removed from the following
              </div>
              <div class="text-grey-8 q-pt-lg q-pb-md tw-six" v-else>
                Select coverages to add {{ nameList }} to
              </div>
              <div class="row">
                <div class="col-12 col-md-6 q-pa-sm">
                  <q-list separator>
                    <coverage-item
                        clickable @click="addCoverage(cvg)"
                        v-for="(cvg, i) in coverages.list"
                        :key="`c-${i}`"
                        :model-value="cvg">
                      <template v-slot:side>
                        <q-item-section side>
                          <q-icon size="20px" v-if="selectedCoverages.includes(cvg._id)" color="green"
                                  name="mdi-check"></q-icon>
                        </q-item-section>
                      </template>
                    </coverage-item>
                  </q-list>
                  <div class="q-pa-md">
                    <q-btn no-caps @click="panel = 'cafe'" label="Adjust Payroll Elections" color="black"
                           text-color="white" push glossy></q-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-slide-transition>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="cafe">
        <choose-benefits
            @update:changes="setCafeChanges" emit-up :model-value="enrollment"></choose-benefits>
      </q-tab-panel>
    </q-tab-panels>


    <div class="col-12 q-pa-md">

      <div class="row justify-center q-py-md">
        <q-spinner v-if="loading" size="40px" color="primary"></q-spinner>
      </div>
      <div v-if="!confirming && coverages.list?.length" class="row _fh items-center justify-center">
        <q-btn push icon-right="mdi-check" class="_s_btn tw-six" no-caps label="Request Changes"
               @click="confirming = true"></q-btn>
      </div>
      <q-slide-transition>
        <div class="_fw" v-if="confirming">
          <div class="row justify-center">
            <div>
              <div class="tw-six text-grey-8 font-1r">Are you sure you want to request these changes?</div>
              <div class="row items-center justify-end q-py-md">
                <q-btn no-caps flat @click="confirming = false">
                  <span class="q-mr-sm">Cancel</span>
                  <q-icon color="red" name="mdi-close"></q-icon>
                </q-btn>
                <q-btn no-caps push class="_p_btn" dark label="Confirm" icon-right="mdi-check"
                       @click="confirm"></q-btn>
              </div>
            </div>
          </div>

        </div>
      </q-slide-transition>
    </div>

  </div>
</template>

<script setup>
  import MemberItem from 'components/households/cards/MemberItem.vue';
  import MemberForm from 'components/households/forms/MemberForm.vue';
  import MemberChip from 'components/households/cards/MemberChip.vue';
  import CoverageItem from 'components/coverages/cards/CoverageItem.vue';
  import ChooseBenefits from 'components/enrollments/forms/ChooseBenefits.vue';

  import {HFind} from 'src/utils/hFind';
  import {computed, ref} from 'vue';
  import {usePpls} from 'stores/ppls';
  import {$capitalizeFirstLetter, $errNotify} from 'src/utils/global-methods';
  import {useCoverages} from 'stores/coverages';
  import {useSpecs} from 'stores/specs';
  import {_get, _set} from 'symbol-syntax-utils';
  import {getCoverageRate} from 'components/coverages/utils/display';

  const pplsStore = usePpls();
  const cStore = useCoverages();
  const specsStore = useSpecs();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    enrollment: { required: true },
    household: { required: true },
  })

  const selectedCoverages = ref([]);
  const confirming = ref(false);

  const list = ref({
    add: [],
    remove: []
  })
  const tab = ref('remove');
  const panel = ref('base');

  const nameList = computed(() => list.value[tab.value].map(a => a.firstName).join(' & '))

  const formFn = (defs) => {
    return {
      description: '',
      message: '',
      event: 'household',
      ...defs
    }
  }

  const setForm = (path, val) => {
    form.value[path] = val;
  }

  const form = ref(formFn());

  const setTab = (val) => {
    tab.value = val;
    form.value = formFn();
    selectedCoverages.value = [];
  }

  const memberIds = computed(() => {
    return Object.keys(props.enrollment?.enrolled || {});
  })
  const coverageObj = computed(() => props.enrollment.coverages || {})

  const enrolledPeople = computed(() => {
    let idList = [];
    for (const k in coverageObj.value) {
      idList = [...idList, ...coverageObj.value[k].participants || []]
    }
    return Array.from(new Set(idList));
  })

  const { h$ } = HFind({
    store: pplsStore,
    limit: ref(20),
    params: computed(() => {
      return {
        query: {
          _id: { $in: [...memberIds.value, ...enrolledPeople.value] }
        }
      }
    })
  })

  const coverages = computed(() => {
    if (tab.value === 'remove') {

      const idList = [];
      const ids = list.value[tab.value].map(a => a._id);
      for (const k in coverageObj.value) {
        const participants = (coverageObj.value[k]?.participants || []);
        for (const p of participants) {
          if (ids.includes(p)) idList.push(k);
        }
      }

      return {
        list: c$.data.filter(a => idList.includes(a._id)),
        idList,
      }
    } else {
      return {
        list: c$.data,
        idList: c$.data.map(a => a._id)
      }
    }
  })

  const { h$: c$ } = HFind({
    store: cStore,
    pause: computed(() => !list.value.add.length && !list.value.remove.length),
    limit: ref(25),
    params: computed(() => {
      return {
        query: {
          _id: { $in: Object.keys(coverageObj.value) || [] }
        }
      }
    })
  })

  const enrolled = computed(() => {
    return h$.data.filter(a => memberIds.value.includes(a._id))
  })

  const dormant = computed(() => {
    return h$.data.filter(a => !enrolledPeople.value.includes(a._id));
  })

  const events = computed(() => {
    return {
      'New baby': { add: true },
      'Adoption': { add: true, remove: true },
      'Marriage': { add: true, remove: true },
      'Divorce': { add: true, remove: true },
      'Death': { add: true, remove: true },
      'Other': { add: true, remove: true }
    }
  })


  const pendingChanges = ref({
    add: {},
    remove: {}
  })

  const setCafeChanges = (val) => {
    for (const subK in val) {
      const newVal = val[subK];
      const oldVal = _get(props.enrollment, subK);
      for (const k in pendingChanges.value) {
        console.log('newVal', newVal);
        console.log('oldVal', oldVal);
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) pendingChanges.value[k][subK] = { newVal, oldVal }
        else delete pendingChanges.value[k][subK]
      }
    }
  }

  const coverageChanges = ref({
    byC: {},
    newPrice: 0,
    oldPrice: 0
  });
  const loading = ref(false);

  const modifiedEr = computed(() => {
    const er = JSON.parse(JSON.stringify(props.enrollment));
    const changes = pendingChanges.value[tab.value]
    for (const k in changes) {
      er.coverages = _set(er.coverages, k.split('.').slice(1).join('.'), changes[k].newVal)
    }
    return er;
  })
  const setChanges = () => {
    loading.value = false;
    const obj = {
      byC: {},
      newPrice: 0,
      oldPrice: 0
    };
    const changes = pendingChanges.value[tab.value];
    for (const k in changes) {
      const splitKey = k.split('.').slice(1)[0];
      if (splitKey) {
        const v = cStore.getFromStore(splitKey);
        const newPrice = getCoverageRate({ coverage: v.value, enrollment: modifiedEr.value})
        const oldPrice = getCoverageRate({ coverage: v.value, enrollment: props.enrollment});
        obj.byC[k] = { newPrice, oldPrice, key: k };
        obj.newPrice += newPrice;
        obj.oldPrice += oldPrice;
      }
    }
    coverageChanges.value = obj;
  }
  const t = ref(undefined);
  const genRemoveCoverages = () => {
    const changes = {};
    for (const c of coverages.value.idList) {
      const newVal = [...coverageObj.value[c].participants || []].slice(0);
      for (const p of list.value.remove) {
        const idx = (coverageObj.value[c].participants || []).indexOf(p._id);
        newVal.splice(idx, 1);
      }
      changes[`coverages.${c}.participants`] = { newVal, oldVal: coverageObj.value[c].participants }
    }
    pendingChanges.value.remove = changes;
    loading.value = true;
    if (t.value) window.clearTimeout(t.value);
    t.value = window.setTimeout(() => {
      setChanges();
    }, 1000)
  }

  const genAddCoverages = () => {
    const changes = {};
    const l = selectedCoverages.value;
    for (const c of l) {
      let newVal = [...coverageObj.value[c]?.participants || []].slice(0);
      for (const person of list.value[tab.value]) {
        newVal.push(person._id);
      }
      changes[`coverages.${c}.participants`] = { newVal, oldVal: coverageObj.value[c].participants };
    }
    pendingChanges.value.add = changes;
    loading.value = true;
    if (t.value) window.clearTimeout(t.value);
    t.value = window.setTimeout(() => {
      setChanges();
    }, 1000)
  }


  const addCoverage = (c) => {
    if (tab.value === 'add') {
      const idx = selectedCoverages.value.indexOf(c._id);
      if (idx > -1) selectedCoverages.value.splice(idx, 1);
      else selectedCoverages.value.push(c._id);
      genAddCoverages()
    }
  }

  const select = (mbr) => {
    const obj = {
      add: genAddCoverages,
      remove: genRemoveCoverages
    }
    const idx = list.value[tab.value].map(a => a._id).indexOf(mbr._id);
    if (idx > -1) list.value[tab.value].splice(idx, 1);
    else list.value[tab.value].push(mbr);
    obj[tab.value]()
  }


  const confirm = async () => {
    const { org, plan, _id, planYear } = props.enrollment;
    const changes = pendingChanges.value[tab.value];
    const spec = await specsStore.create({
      org,
      plan,
      planYear,
      enrollment: _id,
      ...form.value,
      changes
    })
        .catch(err => $errNotify(`${err.message}`));
    emit('update:model-value', spec);

  }

</script>

<style lang="scss" scoped>
  table {
    tr {
      td {
        text-align: right;
        padding: 5px 10px;
      }
    }
  }
</style>
