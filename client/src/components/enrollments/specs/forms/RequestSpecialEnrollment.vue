<template>
  <div class="_fw">
    <div class="q-py-md tw-six text-grey-8 font-1r">Request your plan administrator open a special enrollment period for you</div>
    <q-list separator>
      <q-item-label header>Qualifying Event Type</q-item-label>
      <q-item v-for="e in ['household', 'employment', 'plan']" :key="e" clickable @click="form.event = e">
        <q-item-section avatar>
          <q-icon v-if="form.event === e" color="green" name="mdi-check"></q-icon>
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-p6">{{qEvents[e].name}}</q-item-label>
          <q-item-label caption>{{qEvents[e].description}}</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>

    <q-input type="textarea" filled class="q-my-md" placeholder="Include more information to help the admin make a decision..."
             v-model="form.message"></q-input>

    <q-btn class="_p_btn tw-six" push no-caps label="Submit Request" icon-right="mdi-send" @click="update"></q-btn>
  </div>
</template>

<script setup>
  import {ref, watch} from 'vue';
  import { qEvents } from 'components/enrollments/specs/utils';
  import {$errNotify} from 'src/utils/global-methods';
  import {useSpecs} from 'stores/specs';
  const specsStore = useSpecs();

  const emit = defineEmits(['update:model-value']);

  const props = defineProps({
    enrollment: Object,
    evt: String
  })

  const formFn = (defs) => {
    return {
      description: 'Request for special enrollment period',
      message: '',
      specialEnrollment: true,
      ...defs
    }
  }

  const form = ref(formFn());

  const update = async () => {
    if (!form.value.event) $errNotify('Choose qualifying event');
    else {
      const { org, plan, _id, planYear } = props.enrollment;
      const spec = await specsStore.create({
        org,
        plan,
        planYear,
        enrollment: _id,
        ...form.value
      })
          .catch(err => $errNotify(`${err.message}`));

      emit('update:model-value', spec);
    }
  }

  watch(() => props.evt, (nv) => {
    if(nv) form.value. event = nv;
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
