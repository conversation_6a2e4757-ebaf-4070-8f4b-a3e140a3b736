<template>
  <div class="q-py-md q-px-sm">
    <div class="tw-six text-grey-8 font-1r">{{ acronym }} Change</div>
    <div class="__hg text-center" v-if="/amount/.test(path)">
      <div class="__c __b">
        <div class="tw-six text-s6">Old {{acronym}} Contribution</div>
        <div class="font-1-1-4r tw-six text-s6">{{ dollarString(modelValue?.oldVal, '$', 0) }}/Mo</div>
      </div>
      <div class="__c">
        <div class="tw-six text-p6">New {{acronym}} Contribution</div>
        <div class="font-1-1-4r tw-six text-p6">{{ dollarString(modelValue?.newVal, '$', 0) }}/Mo</div>
      </div>
    </div>
    <div v-else>
      <q-chip color="transparent" class="tw-six">
        <q-icon color="red" name="mdi-cancel"></q-icon>
        <span class="q-ml-sm">Opt out of plan <span
            class="text-uppercase">{{ path.split('.').slice(1)[0] }}</span></span>
      </q-chip>
    </div>
  </div>
</template>

<script setup>
  import {dollarString} from 'src/utils/global-methods';
  import {computed} from 'vue';

  const props = defineProps({
    modelValue: Object,
    path: String
  })

  const acronym = computed(() => {
    if (props.path) return props.path.split('.').slice(1)[0]?.toUpperCase() || '';
    else return '';
  })
</script>

<style lang="scss" scoped>
  .__hg {
    display: grid;
    grid-template-rows: repeat(auto-fit, auto);
    grid-template-columns: repeat(auto-fill, minmax(250px, 350px));
    grid-gap: 10px;
  }

  .__c {
    border-radius: 10px;
    border: solid 2px var(--q-primary);
    padding: 20px 14px;
  }

  .__b {
    border-color: var(--q-secondary);
  }
</style>
