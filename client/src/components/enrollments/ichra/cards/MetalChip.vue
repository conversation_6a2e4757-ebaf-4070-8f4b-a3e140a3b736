<template>
  <q-chip v-bind="{ color: 'transparent', ...$attrs}">
    <metal-avatar :model-value="modelValue"></metal-avatar>

    <slot name="text">
    <span v-if="!noLabel" class="q-ml-xs tw-six font-7-8r">{{ $capitalizeFirstLetter(modelValue) }} Tier</span>
    </slot>
    <slot name="right"></slot>
  </q-chip>
</template>

<script setup>

  import {$capitalizeFirstLetter} from 'src/utils/global-methods';
  import MetalAvatar from 'components/enrollments/ichra/cards/MetalAvatar.vue';

  const props = defineProps({
    modelValue: String,
    noLabel: { required: false }
  })
</script>

<style lang="scss" scoped>
  .__bronze {
    background: #CD7F32;
  }

  .__silver {
    background: #C0C0C0;
  }

  .__gold {
    background: #FFD700;
  }

  .__platinum {
    background: #e5e4e2;
  }
</style>
