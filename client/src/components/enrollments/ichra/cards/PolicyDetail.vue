<template>
  <div class="_fw">
    <div class="q-pt-md q-pb-sm" v-if="combined && ichra">
      <div class="row items-center justify-end">
        <div class="font-7-8r">Total out of pocket cost for <span
            class="tw-six">{{ dollarString(ichra?.annualSpend, '$', 0) }}</span> annual medical bills: <span
            class="tw-six text-p6">{{ dollarString(combined, '$', 0) }}</span></div>
      </div>

    </div>
    <div class="w500 mw100 q-py-sm">
      <q-input v-model="searchInput" label="Search Benefits">
        <template v-slot:prepend>
          <q-icon name="mdi-magnify"></q-icon>
        </template>
      </q-input>
    </div>
    <slot name="benefits">
      <table class="__benefits q-my-md">
        <thead>
        <tr class="__head">
          <th>Benefit</th>
          <th>Detail</th>
          <th>Covered</th>
        </tr>
        </thead>
        <template v-for="(b, i) in benefitRows" :key="`ben-${i}`">
          <tr>
            <td>{{policy.benefits[b].label}}</td>
            <td>{{policy.benefits[b].detail?.split(' | ').slice(0, -1).join(' | ')}}</td>
            <td>
              <q-checkbox :model-value="!!policy.benefits[b].covered"></q-checkbox>
            </td>
          </tr>
        </template>
      </table>
    </slot>
    <slot name="bottom"></slot>
  </div>
</template>

<script setup>
  import {dollarString} from 'src/utils/global-methods';
  import {computed, ref} from 'vue';

  const props = defineProps({
    policy: { required: true },
    coins: Number,
    combined: Number,
    names: Object,
    deductibles: Object,
    benefits: Array,
    moops: Object,
    ichra: Object,
    openLink: Function,
    dark: Boolean
  })

  const searchInput = ref('')
  const benefitRows = computed(() => {
    const arr = Object.keys(props.policy?.benefits || {});
    return arr.filter(a => a.toLowerCase().includes(searchInput.value.toLowerCase()));
  })
</script>

<style lang="scss" scoped>

  .__benefits {
    width: 100%;
    border-collapse: collapse;
    text-align: left;

    tr {
      th {
        color: var(--ir-mid);
        padding: 2px 8px;
      }
      td {
        padding: 4px 8px;
        //border-bottom: solid .5px var(--ir-mid);
        font-size: .8rem;
        &:first-child {
          font-weight: 600;
          color: var(--ir-deep);
        }

      }
      &:nth-child(even) {
        background: var(--ir-bg2);
      }
    }

    .__head {
      td {
        padding: 10px;
        background: var(--ir-off);
        font-weight: 600;
      }
    }
  }
</style>
