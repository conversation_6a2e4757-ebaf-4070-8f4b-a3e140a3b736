<template>
  <div class="_fw relative-position __pf">
    <div class="t-r-a">
      <metal-chip :no-label="true" :model-value="plcy?.metal?.toLowerCase()"></metal-chip>
    </div>
    <div class="font-1r tw-six __name">
      <div class="__logo q-mr-sm" v-if="carrierLogo._id">
        <q-img :src="carrierLogo.url" fit="contain" class="_fa"></q-img>
      </div>
      <div>{{ plcy?.carrierName }}</div>
    </div>
    <div class="row items-start">
      <div class="_fw">
        <div class="tw-six font-1r __nm">{{ plcy.title || plcy.name }}</div>
        <div class="font-7-8r">{{ plcy.subtitle }}</div>
      </div>
    </div>

    <div class="q-py-xs row items-center">
      <q-chip class="tw-six" size="sm">{{ plcy?.plan_type }}
        <q-tooltip class="tw-six">Network Type: {{ plcy?.plan_type }}</q-tooltip>
      </q-chip>
      <q-btn :color="btnColor" @click="openLink('benefits_url', 'brochure_url')" size="sm" dense flat
             icon="mdi-file-document">
        <q-tooltip class="tw-six">View Detail Sheet</q-tooltip>
      </q-btn>
      <q-btn :color="btnColor" @click="openLink('formulary_url', 'brochure_url')" size="sm" dense flat
             icon="mdi-pill">
        <q-tooltip class="tw-six">View Drug Coverage</q-tooltip>
      </q-btn>
      <q-btn :color="btnColor" @click="openLink('network_url', 'benefits_url')" size="sm"
             dense flat icon="mdi-hospital-marker">
        <q-tooltip class="tw-six">Full Network Coverage</q-tooltip>
      </q-btn>
    </div>

    <div class="q-pa-sm _fw row items-end">
      <div v-if="plcy.premium" class="font-3-4r"><span :class="`tw-six text-${pColor} font-1-1-4r alt-font`">{{
          dollarString(maxZero(plcy.premium * (mult || 1)), '$', 0)
        }}</span>
        {{mult === 12 ? '/yr' : '/mo'}}
      </div>
      <div v-else-if="plcy.rating_areas" class="font-3-4r"><span :class="`tw-six text-${pColor} font-1-1-4r alt-font`">{{
          dollarString(rate * (mult || 1), '$', 0)
        }}</span>
        {{mult === 12 ? '/yr' : '/mo'}}
      </div>
    </div>
    <div v-if="actualAptc || subsidy" class="_fw q-pb-md font-1r">
      <div class="_fw">
        <q-chip class="tw-six text-ir-bg1 bg-ir-off alt-font">
          <q-icon class="q-mr-sm" color="ir-bg1" name="mdi-minus"></q-icon>
          <span class="font-1-1-4r">{{dollarString(actualAptc, '$', 0)}}</span><span class="font-3-4r tw-four">{{mult === 12 ? ' /yr' : ' /mo'}} PTC</span>
          <q-tooltip class="tw-six text-xxs">Premium Tax Credit</q-tooltip>
        </q-chip>
      </div>
      <div class="_fw" v-if="actualSubsidy">
        <q-chip class="tw-six text-ir-bg1 bg-accent alt-font">
          <q-icon class="q-mr-sm" color="ir-bg1" name="mdi-minus"></q-icon>
          <span class="font-1-1-4r">{{dollarString(actualSubsidy, '$', 0)}}</span><span class="font-3-4r tw-four">{{mult === 12 ? ' /yr' : ' /mo'}} Subsidy</span>
          <q-tooltip class="tw-six text-xxs">Employer Contributions</q-tooltip>
        </q-chip>
      </div>
      <div class="_fw">
        <q-chip class="tw-six text-ir-off alt-font" color="ir-bg2">
          <q-icon class="q-mr-sm" color="ir-off" name="mdi-equal"></q-icon>
          <span class="font-1-1-4r">{{dollarString(maxZero(((plcy?.premium || 0) * (mult || 1)) - (actualAptc + actualSubsidy)), '$', 0)}}</span><span class="font-3-4r tw-four">{{mult === 12 ? ' /yr' : ' /mo'}} your cost</span>
        </q-chip>
      </div>
    </div>
    <div class="flex items-center">
      <table>
        <tbody>
        <tr>
          <td>Deductible</td>
          <td></td>
          <td>Max OOP</td>
          <td></td>
        </tr>
        <tr>
          <td>
            <q-icon size="15px" name="mdi-account" :color="pColor"></q-icon>
          </td>
          <td>
            <q-icon size="15px" :color="pColor" name="mdi-account-group"></q-icon>
          </td>
          <td>
            <q-icon size="15px" name="mdi-account" :color="btnColor"></q-icon>
          </td>
          <td>
            <q-icon size="15px" :color="btnColor" name="mdi-account-group"></q-icon>
          </td>
        </tr>
        <tr class="alt-font">
          <td>{{ dollarString(getDeductible(plcy, 'single'), '$', 0) }}</td>
          <td>{{ dollarString(getDeductible(plcy, 'family'), '$', 0) }}</td>
          <td>{{ dollarString(getMoop(plcy, 'single'), '$', 0) }}</td>
          <td>{{ dollarString(getMoop(plcy, 'family'), '$', 0) }}</td>
        </tr>
        </tbody>
      </table>
    </div>


  </div>
</template>

<script setup>
  import MetalChip from 'components/enrollments/ichra/cards/MetalChip.vue';

  import {dollarString} from 'src/utils/global-methods';
  import {computed} from 'vue';
  import {getDeductible, getMoop} from 'components/enrollments/ichra/utils';
  import {getEnrolled, simRelations} from 'components/coverages/utils/display';
  import {useUploads} from 'stores/uploads';
  import {idGet} from 'src/utils/id-get';
  const uploadStore = useUploads();

  const props = defineProps({
    mult:Number,
    dark: Boolean,
    policy: { required: true },
    coins: Number,
    combined: Number,
    openLink: Function,
    enrType: String,
    aptc: Number,
    subsidy: Number,
    zip: String,
    age: Number,
    enrollment: Object,
    enrolled: Array,
    place: Object
  })

  const maxZero = (v, mx) => Math.max(mx || 0, v);

  const btnColor = computed(() => props.dark ? 'a3' : 'a7')
  const pColor = computed(() => 'primary')

  const plcy = computed(() => props.policy || {})

  const { item:carrierLogo } = idGet({
    store: uploadStore,
    value: computed(() => plcy.value._fastjoin?.files?.carrierLogo || plcy.value.carrierLogo?.uploadId)
  })

  const rate = computed(() => {
    if(plcy.value.premium) return plcy.value.premium;
    if(!plcy.value.rating_areas) return 'N/A';
    const e = props.enrollment || {}
    let areaData;
    const fips = e.county?.fips || props.place?.countyfips;
    if(fips){
      for(const k in plcy.value.rating_areas){
        if((plcy.value.rating_areas[k]?.fips || []).includes(fips)) {
          areaData = plcy.value.rating_areas[k]
          break;
        }
      }
    }
    if(!areaData){
      const zip = props.zip || e.address?.postal;

      if(zip) {
      for (const k in plcy.value.rating_areas) {
        if ((plcy.value.rating_areas[k]?.zips || []).includes(zip)) areaData = plcy.value.rating_areas[k];
      }
    }
    } else areaData = plcy.value.rating_areas[Object.keys(plcy.value.rating_areas)[0]]

    // console.log('this far', areaData)
    if(!areaData) return plcy.value.premium
    let enrolled = getEnrolled({ enrollment: e, enrolled: props.enrolled })
    if(enrolled.length && enrolled.filter(a => !a.relation).length) enrolled = simRelations(enrolled);
    if(props.age && !enrolled.length) enrolled = [{ age: props.age, relation: 'self' }];
    let deps = [];
    let self;
    let spouse;
    if(!enrolled.length) return areaData.rates[props.age || 40];
    for(const e of enrolled){
      if(e.relation === 'self') self = e;
      else if(e.relation === 'spouse') spouse = e;
      else deps.push(e)
    }
    deps = deps.sort((a, b) => b - a).slice(0, 3)
    let rate = 0;
    if(self) rate += areaData.rates[self.age] || 0;
    if(spouse) rate += areaData.rates[spouse.age] || 0;
    for(const dep of deps){
      rate += areaData.rates[dep.age]
    }
    return rate
  })

  const actualAptc = computed(() => {
    if(!plcy.value) return 0;
    if(plcy.value.off_exchange) return 0;
    return Math.min(plcy.value.premium || 0, props.aptc) * (props.mult || 1)
  })
  const actualSubsidy = computed(() => {
    return (props.subsidy || 0) * (props.mult || 1)
  })
</script>

<style lang="scss" scoped>
  .__pf {
    width: 100%;
    overflow-x: scroll;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    text-align: right;

    tr {
      td {
        padding: 5px 8px;
        border-bottom: solid .2px #c5c5c5;
        font-size: .8rem;
      }
    }

    tr:last-child {
      td {
        border-bottom: none;
        font-size: .8rem;
        font-weight: 600;
      }
    }
  }

  .__nm {
    width: 90%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .__logo {
    height: 30px;
    width: 30px;
    overflow: hidden;
    border-radius: 15px;
  }

  .__name {
    display: grid;
    grid-template-columns: auto 1fr;
    width: 100%;
    align-items: center;

    div {
      &:last-child {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: .8rem;
        font-weight: 500;
      }
    }

  }
</style>
