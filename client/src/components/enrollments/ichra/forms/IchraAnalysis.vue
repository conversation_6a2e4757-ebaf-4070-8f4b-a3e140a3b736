<template>
  <div class="_fw">
    <table>
      <tr>
        <td>Premium Costs ({{ $capitalizeFirstLetter(metal || '') }} Tier)</td>
        <td><span class="text-blue">{{ dollarString(policy?.premium || ((premium || {})[metal] / 12), '$', 0) }}</span> /mo</td>
      </tr>
      <tr>
        <td>Plan Reimbursement</td>
        <td><span>{{ dollarString(reimbursement + (contributions.employer?.cafe || 0), '$', 0) }}</span> /mo</td>
      </tr>
      <tr>
        <td>Premium Tax Credit</td>
        <td><span class="text-grey-7">{{ dollarString(useAptc, '$', 0) }}</span> /mo</td>
      </tr>
    </table>
    <div class="_fw font-1r">

      <div v-if="form?.aptc?.income !== fulle?.householdIncome" class="q-py-md">
        ⚠️ It looks like your income was updated, you should <span class="tw-six text-blue cursor-pointer"
                                                                   @click="incomeDialog = true">refresh</span>
        your credit estimate
      </div>
    </div>

    <q-separator class="q-my-sm"></q-separator>
    <div class="row justify-end items-center q-py-sm cursor-pointer" @click="incomeDialog = true">
      <div class="q-px-sm">Household Income: <span class="text-s7 tw-six">{{
          dollarString(form?.aptc?.income || 0, '$', 0)
        }}</span></div>
      <div class="flex items-center">
        <q-checkbox size="xs" @update:model-value="incomeDialog = true"
                    :model-value="!!form?.aptc?.attest"></q-checkbox>
        <div class="font-3-4r">
          {{ form?.aptc?.attest ? `Updated ${$ago(form.aptc.attest)}` : 'Confirm Income' }}
        </div>
      </div>
    </div>

    <table>
      <tr v-if="useDeficit > 0">
        <td>Premium Surplus</td>
        <td><span class="text-primary">{{ dollarString(deficit, '$', 0) }}</span> /mo</td>
      </tr>
      <tr v-else>
        <td>Your Premium Share (after employer cont.)</td>
        <td><span class="text-secondary">{{ dollarString((useDeficit || 0) * -1, '$', 0) }}</span> /mo</td>
      </tr>
      <tr>
        <td>Out of Pocket (if {{ dollarString(form?.annualSpend, '$', 0) }} in medical bills)</td>
        <td><span class="text-secondary">{{ dollarString(useOop, '$', 0) }}</span> /mo</td>
      </tr>
    </table>


    <common-dialog v-model="incomeDialog" setting="small">
      <div class="bg-white _fw q-pa-md">
        <div class="_form_grid">
          <div class="_form_label">Income</div>
          <div class="q-pa-sm">
            <money-input class="_inp" @update:model-value="incomeAttest = false" v-model="income"></money-input>
          </div>
          <div class="_form_label">⚠️</div>
          <div class="q-pa-sm">
            <div class="q-pa-sm font-1r">What you list as your household income can affect your taxes and plan
              compliance.
            </div>
            <div class="__att">
              <q-checkbox v-model="incomeAttest"
                          label="I attest this is my estimated annual household income"></q-checkbox>
            </div>
          </div>
        </div>
        <q-slide-transition>
          <div class="row justify-end q-py-sm" v-if="incomeAttest">
            <q-btn flat @click="updateIncome">
              <span class="tw-six q-mr-sm">Update</span>
              <q-spinner v-if="incomeLoading" color="primary"></q-spinner>
              <q-icon v-else name="mdi-refresh" color="primary"></q-icon>
            </q-btn>
          </div>
        </q-slide-transition>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';

  import {$ago, $capitalizeFirstLetter, dollarString} from 'src/utils/global-methods';
  import {computed, ref, watch} from 'vue';
  import {useEnrollments} from 'stores/enrollments';
  import { getOop } from 'components/enrollments/ichra/utils';

  const enrollmentStore = useEnrollments();

  const props = defineProps({
    enrollment: { required: true },
    modelValue: { required: true },
    best: { required: true },
    premium: { required: true },
    deficit: { required: true },
    aptc: { required: true },
    oop: { required: true },
    reimbursement: { required: true },
    contributions: { required: true }
  })


  const fulle = computed(() => props.enrollment)
  const coverageId = computed(() => Object.keys(fulle.value.coverages || {}).filter(a => fulle.value.coverages[a].ichra)[0])

  const form = ref({})
  watch(() => props.modelValue, (nv) => {
   if(nv) form.value = { ...nv }
  }, { immediate: true })

  const incomeDialog = ref(false);
  const income = ref(0);
  const incomeLoading = ref(false);
  const incomeAttest = ref(false);

  const updateIncome = async () => {
    incomeLoading.value = true;
    const patchObj = { $set: { householdIncome: income.value, [`coverages.${coverageId.value}.aptc.income`]: income.value, [`coverages.${coverageId.value}.aptc.attest`]: new Date() } }
    enrollmentStore.patchInStore(fulle.value._id, patchObj)
    await enrollmentStore.patch(fulle.value._id, patchObj)
        .catch(err => {
          console.error(`Could not update income: ${err.message}`);
          return;
        });
    incomeLoading.value = false;
    incomeDialog.value = false;
  }

  watch(() => props.modelValue, (nv) => {
    if (nv && !income.value) {
      income.value = fulle.value?.householdIncome || nv?.aptc?.income || 0;
    }
  }, { immediate: true })

  const policy = computed(() => form.value?.fullPolicy);
  const metal = computed(() => policy.value?.metal?.toLowerCase() || props.best);

  const useAptc = computed(() => {
    if(policy.value?.off_exchange) return 0;
    return props.aptc;
  })

  const useDeficit = computed(() => {
    if(policy.value?.premium) return (props.reimbursement || 0) + (props.contributions?.employer?.cafe || 0) - policy.value.premium;
    else return props.deficit
  })

const useOop = computed(() => {
  if(policy.value) return (getOop({ policy: policy.value, spend: form.value?.annualSpend, type: form.value?.type || fulle.value?.type || 'family' }) || 0) / 12;
  else return ((props.oop || {})[props.best] || 0) / 12
})

</script>

<style lang="scss" scoped>
  .__att {
    background: #eee;
    padding: 10px;
    font-size: 1rem;
  }
  table {
    margin: 20px 10px;
    border-collapse: collapse;
    width: 100%;

    tr {
      border-bottom: solid .3px #999;
      text-align: left;

      td {
        font-size: .9rem;
        text-align: right;
        padding: 5px 10px;

        span {
          font-size: 1.1rem;
          font-weight: 600;
          color: var(--q-primary);
        }
      }

      td:first-child {
        font-weight: 600;
        color: #757575;
        font-size: .9rem;
        text-align: left;
      }
    }

    :last-child {
      border-bottom: none;
    }
  }
</style>
