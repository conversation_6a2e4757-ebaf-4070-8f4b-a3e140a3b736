<template>
  <q-page>
    <div class="row justify-center">
      <div class="_cent pd5 pw2">
        <div class="q-pa-sm flex items-center">
          <div class="tw-six font-1r">AI Settings</div>
          <q-btn @click="addDialog = true" dense flat icon="mdi-plus" color="primary"></q-btn>
        </div>
      </div>
    </div>

    <common-dialog @update:model-value="toggleDialog" :model-value="addDialog || !!editing" setting="full">
      <div class="_fw q-pa-md bg-white">
        <ai-context-form :model-value="drawer"></ai-context-form>
      </div>
    </common-dialog>
  </q-page>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import AiContextForm from 'components/ai/forms/AiContextForm.vue';

  import {onMounted, ref} from 'vue';
  import {useJunkDrawers} from 'stores/junk-drawers';

  const junkStore = useJunkDrawers();

  const addDialog = ref(false);
  const editing = ref()

  const toggleDialog = (val) => {
    if(!val) {
      addDialog.value = false;
      editing.value = undefined;
    }
  }

  const drawer = ref({})

  onMounted(async () => {
    const drawers = await junkStore.find({ query: { itemId: 'ai|context', $limit: 1 }})
    if(drawers.total) drawer.value = drawers.data[0];
  })

</script>

<style lang="scss" scoped>

</style>
