<template>
  <div class="_fw">
    <div class="row justify-center pd5 pw2">
      <div class="_cent">
        <div class="q-pa-sm tw-six">AI Bias Document</div>
        <q-separator class="q-my-sm"></q-separator>
        <q-splitter :horizontal="$q.screen.lt.md" v-model="splitter">
          <template v-slot:before>
            <div class="_fw __md_panel">
              <md-editor
                  :preview="false"
                  language="en-US"
                  v-model="form.body"
                  :toolbars-exclude="toolbarsExclude"
                  @focus="editorFocus = true"
                  @blur="$emit('blur')"
                  @update:model-value="dirty = true"
              ></md-editor>
            </div>
          </template>

          <template v-slot:separator>
            <q-avatar color="primary" text-color="white" size="30px" icon="drag_indicator"/>
          </template>

          <template v-slot:after>
            <div class="_fw __preview">
              <q-chip square color="ir-grey-5" dark class="tw-six">Preview</q-chip>
              <md-preview
                  :model-value="form.body"
              ></md-preview>
            </div>
          </template>
        </q-splitter>

        <div class="q-pt-lg row justify-end">
          <q-btn v-if="dirty" label="Save" icon-right="mdi-content-save" no-caps class="_p_btn tw-six" push
                 @click="save"></q-btn>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>

  import {computed, ref, watch} from 'vue';
  import {MdEditor, MdPreview} from 'md-editor-v3';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import 'md-editor-v3/lib/style.css';

  const junkStore = useJunkDrawers();

  const props = defineProps({
    modelValue: { required: false }
  })

  const editorFocus = ref(false);
  const splitter = ref(50);

  const toolbarsExclude = ['save', 'github', 'fullscreen', 'image', 'htmlPreview', 'sup', 'code', 'codeRow', 'katex', 'task', 'quote', 'strikeThrough', 'sub', 'table', 'mermaid', 'title', 'underline', 'pageFullScreen']

  const formFn = (defs) => {
    return {
      body: '',
      ...defs
    }
  }
  const form = ref(formFn())
  const dirty = ref(false);

  const bias = computed(() => props.modelValue?.data?.bias);
  watch(bias, (nv) => {
    if(nv) form.value = formFn(nv);
  }, {immediate: true})

  const save = () => {
    dirty.value = false;
    if(!props.modelValue?._id){
      junkStore.create({ drawer: 'ai', itemName: 'context', data: { bias: { body: form.value } } })
    } else junkStore.patch(props.modelValue._id, { $set: { ['data.bias.body']: form.value.body } }, { runJoin: { ai_context: true }})
  }


</script>

<style lang="scss" scoped>
  .__md_panel {
    width: 100%;
    overflow-x: scroll;
    padding: 0 !important;
  }
</style>
