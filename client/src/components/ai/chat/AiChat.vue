<template>
  <div class="q-pa-md  _fa">
    <div class="_fw __chat">
      <div>
        <div>
          <slot name="top"></slot>
        </div>
        <div>
          <!--                CHAT WINDOW __chat > div > div:nth-child(2)-->

          <div class="_fw" v-if="active">
            <q-separator class="q-my-sm"></q-separator>
            <div class="_fw">
              <q-btn dense flat size="sm" color="red" icon="mdi-close" @click="setActive('')"></q-btn>
            </div>
            <div class="__rev">
              <template v-for="(act, i) in bySession[active] || []" :key="`act-${i}`">

                <div :class="`__a text-${dark ? 'ir-bg1' : 'ir-text'}`" v-html="act.html">
                </div>
                <div class="row justify-end q-py-md">
                  <div>
                    <div :class="`font-3-4r tw-six text-${dark ? 'ir-bg1' : 'ir-text'}`">{{
                        $dateDisplay(act.createdAt)
                      }}
                    </div>
                    <div :class="`__q text-${dark ? 'ir-bg1' : 'ir-text'}`"
                         :style="{background: !dark ? 'var(--ir-bg2)' : 'rgba(99,99,99,.2)'}">
                      {{ act.question }}
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>

          <div v-else class="_fw q-py-sm">
            <div class="q-pa-sm" :class="`font-1r tw-five text-ir-${dark ? 'bg' : 'deep'}`">You might ask</div>
            <q-list :dark="dark">
              <q-item v-for="(prompt, i) in prompts || []" :key="`prompt-${i}`" clickable @click="question = prompt">
                <q-item-section>
                  <q-item-label :class="`tw-five font-1r text-ir-${dark ? 'bg2' : 'mid'}`">{{ prompt }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>

          <div class="__list">
            <div v-for="(k, i) in Object.keys(bySession).filter(a => a !== active)" :key="`qa2-${i}`"
                 @click="setActive(bySession[k][0].session)">
              <div :class="`_one_liner text-ir-${dark ? 'bg2' : 'deep'}`">{{ bySession[k][0].question }}</div>
              <div :class="`_one_liner text-ir-${dark ? 'light' : 'mid'}`">{{ bySession[k][0].answer }}</div>
              <div :class="`text-ir-${dark ? 'light' : 'mid'}`">{{
                  $dateDisplay(bySession[k][0].createdAt, '', '')
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <q-input id="AiChatInput" :dark="dark" :error="!!err" :error-message="err" @keyup.enter="send" filled
                 v-model="question" @update:modelValue="emit('update:text', $event)" autogrow :limit="500"
                 :placeholder="`Ask a question about this ${subjectLabel || 'page'}...`">
          <template v-slot:append>
            <q-btn v-if="!thinking" flat size="sm" :class="`bg-ir-${dark ? 'deep' : 'light'} text-ir-mid`" round
                   icon="mdi-arrow-right"
                   @click="send"></q-btn>
            <ai-logo :dark="dark" v-else opaque></ai-logo>
          </template>
        </q-input>
      </div>
    </div>

  </div>
</template>

<script setup>
  import AiLogo from 'src/utils/icons/AiLogo.vue';

  import {computed, nextTick, onMounted, ref, watch} from 'vue';
  import {$dateDisplay, $errNotify} from 'src/utils/global-methods';
  import {loginPerson} from 'stores/utils/login';
  import showdown from 'showdown';
  import {useAiChats} from 'stores/ai-chats';

  const { isAuthenticated, person } = loginPerson();
  const aiChatStore = useAiChats();

  const emit = defineEmits(['update:text']);
  const props = defineProps({
    openFocus: Boolean,
    dark: Boolean,
    prompts: Array,
    subjectLabel: String,
    subject: { required: true },
    chatName: { required: true },
    runChat: { required: true, type: Function }, /** (text, history, session) => Promise<chat_response> */
    text: String
  })

  // const isOutOfDate = computed(() => {
  //   if (!planUpdated.value) return true;
  //   const pu = new Date(planUpdated.value).getTime();
  //   return new Date(doc.value.sectionsUpdatedAt || pu).getTime() > pu
  // });
  const history = ref([]);
  const active = ref('');
  const bySession = computed(() => {
    const obj = {};
    for (const ch of history.value) {
      obj[ch.session] = [...obj[ch.session] || [], ch];
    }
    return obj;
  })

  const setActive = (session) => {
    active.value = session
  }
  const question = ref('');
  const response = ref();

  watch(() => props.text, (nv, ov) => {
    if ((nv || nv === '') && nv !== ov) {
      question.value = nv;
    }
  }, { immediate: true })

  const thinking = ref(false);

  const typeText = ref('');
  const typing = ref(false);
  const typeResponse = (idx, backlog = '') => {
    if (idx < typeText.value.length) {
      typing.value = true;
      let to = 35;
      const nextChar = typeText.value.substring(idx, idx + 1)
      if (backlog) {
        /** if not closing tag and length of backlog isn't excessive - continue backlogging */
        if (!((nextChar === '>' && backlog.charAt(backlog.length - 1) === '/') || backlog.length > 30)) return typeResponse(idx + 1, backlog + nextChar)
      } else if (nextChar === '<') {
        return typeResponse(idx + 1, nextChar);
      }
      history.value[0].html += backlog + nextChar;
      setTimeout(() => typeResponse(idx + 1), to)
    } else {
      typing.value = false;
      typeText.value = ''
    }
  }
  const err = ref()
  const send = async () => {
    if (!isAuthenticated.value) return err.value = 'Must login/register to utilize CommonAI Chat'
    err.value = ''
    if (thinking.value) return;
    if (!question.value) return;
    const tv = question.value.slice(0)
    if (tv.length < 15) return $errNotify('That question isn\'t detailed enough');
    thinking.value = true;
    const session = active.value || new Date().getTime().toString()
    history.value.unshift({ session, createdAt: new Date(), question: tv, html: '...' })
    setActive(session);
    question.value = '';
    emit('update:text', '')
    const res = await props.runChat(tv, history.value.slice(1), session)
        .catch(err => {
          console.error(`Error sending query: ${err.message}`)
          thinking.value = false;
        })
    thinking.value = false;
    if (res) {
      response.value = res;
      history.value[0].answer = response.value.text;
      const converter = new showdown.Converter();
      typeText.value = converter.makeHtml(response.value.text);
      history.value[0].html = '';
      typeResponse(0);
      history.value[0].annotations = response.value.annotations;
    } else err.value = 'Invalid response - please try again'
    question.value = '';
    emit('update:text', '')
  }

  const ai_chat = ref({ data: [] })

  const loadHistory = async () => {
    ai_chat.value = await aiChatStore.find({
      query: {
        $limit: 1,
        chatId: `${person.value._id}|${props.subject}|${props.chatName}`
      }
    })
        .catch(err => {
          console.error(`Error loading ai chat: ${err.message}`)
          return ai_chat.value;
        })
    const converter = new showdown.Converter()
    if (ai_chat.value.total && ai_chat.value.data[0].chats) {
      history.value = ai_chat.value.data[0].chats.map(a => {
        return {
          ...a,
          stale: true,
          html: converter.makeHtml(a.answer)
        }
      });
      setActive(undefined);
    }
  }

  onMounted(() => {
    if (props.openFocus) {
      nextTick(() => {
        const el = document.getElementById('AiChatInput');
        if (el) el.focus();
      })

    }
    setTimeout(() => {
      loadHistory()
    }, 1000)
  })
</script>

<style lang="scss" scoped>

  .__chat {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: minmax(0, 1fr) auto;
    grid-template-columns: 100%;

    > div {
      &:first-child {
        display: grid;
        grid-template-rows: auto minmax(0, 1fr); // Ensures inner scroll section can shrink
        overflow: hidden; // Prevents inner overflow

        > div {
          width: 100%;

          &:nth-child(2) {
            width: 100%;
            display: flex;
            flex-direction: column-reverse; /* New messages appear at the bottom */
            min-height: 0; // ✅ this is key for scroll inside grid
            overflow-y: scroll; // This should now work
            font-family: var(--alt-font);

          }

          .__q {
            padding: 12px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
          }

          .__a {
            padding: 12px;
            font-size: .9rem;
          }
        }
      }

      &:nth-child(2) {
        padding: 10px 0;
      }
    }
  }

  .__list {
    > div {
      padding: 10px;
      transition: all .3s;
      cursor: pointer;
      width: 100%;
      border-bottom: solid 1px var(--ir-mid);

      &:hover {
        transform: translate(0, -2px);
        background: rgba(0, 0, 0, .05);
      }

      > div {
        &:first-child {
          font-weight: 600;
          font-size: .9rem;
        }

        &:nth-child(2) {
          font-size: .8rem;
        }

        &:nth-child(3) {
          font-size: .8rem;
          font-weight: 600;
        }
      }
    }
  }

  .__rev {
    display: flex;
    flex-direction: column-reverse;
  }
</style>
