<template>
  <q-page class="__o flex flex-center">
    <q-spinner size="40px" color="primary"></q-spinner>
    <div class="font-1r text-weight-medium text-italic q-pl-md">Checking authentication...</div>
  </q-page>
</template>

<script setup>
  import {useRoute} from 'vue-router';
  import {onMounted} from 'vue';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';

  const route = useRoute();
  import {useAuth} from 'src/stores/auth';

  const authStore = useAuth();

  onMounted(async () => {

    const { access_token, oauth_redirect, oauth_url } = route.query;
    if(oauth_redirect){
      LocalStorage.setItem('oauth_redirect', oauth_redirect);
    }
    if(oauth_url){
      window.open(oauth_url)
    }
    if (access_token) {
      await authStore.authenticate({ strategy: 'jwt', accessToken: access_token });
      authStore.reAuthenticate();
    } else {
      SessionStorage.removeItem('client_ucan')
      SessionStorage.removeItem('ucan_aud')
      LocalStorage.removeItem('client_ucan')
      LocalStorage.removeItem('ucan_aud')
    }
    setTimeout(() => {
      const next = LocalStorage.getItem('referrerUrl') || LocalStorage.getItem('oauth_redirect') || window.location.origin;

      window.open(next, '_self')
    }, 1000);
  })
</script>

<style lang="scss" scoped>
  .__o {
    min-height: 90vh;
  }
</style>
