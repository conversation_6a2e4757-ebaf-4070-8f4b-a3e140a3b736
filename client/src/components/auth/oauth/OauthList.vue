<template>
  <div class="_fw">
    <div class="row">
      <div class="col-6">
        <div class="_col_start">
          <oauth-button
              v-for="(key, i) in Object.keys(items).slice(0, half)"
              :key="`key-${i}`"
              :platform="key"
              :redirect="redirect"
          ></oauth-button>
        </div>
      </div>
      <div class="col-6">
        <div class="_col_start">
          <oauth-button
              v-for="(key, i) in Object.keys(items).slice(half)"
              :key="`key-${i}`"
              :platform="key"
              :redirect="redirect"
          ></oauth-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

import OauthButton from './OauthButton.vue';
import {computed} from 'vue';

const props = defineProps({
  redirect: String
})

const items = computed(() => {
  return {
    'google': {},
    // 'facebook': {},
    // 'github': {},
    // 'microsoft': {},
    // 'twitter': {},
    'linkedin': {},
    // 'apple': {}
  };
});
const half = computed(() => Math.ceil(Object.keys(items.value).length / 2));
</script>

<style lang="scss" scoped>

</style>
