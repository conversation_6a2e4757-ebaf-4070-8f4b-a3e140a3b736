<template>
  <div class="_fw">
    <q-item>
      <q-item-section>
        <q-item-label v-if="!simple">
          <i>with:</i> {{ scheme }} - {{ $limitStr(hierPart, 20, '...') }}
        </q-item-label>
        <q-item-label>
          <i>can:</i>
          <span v-if="!isSuperuser">
          <span class="text-weight-medium" v-for="(segment, i) in segments" :key="`segment-${i}`">{{segment === '*' ? 'MANAGE' : segment}}{{ i < segments?.length - 1 ? ', ' : '' }}</span> - <b>{{ $limitStr(namespace, 25, '...') }}</b>
          </span>
          <span v-else>
            <span class="text-purple">&nbsp;SUPERUSER</span>
          </span>
        </q-item-label>
      </q-item-section>
      <q-item-section side>
        <div>
          <q-btn
              size="sm"
              dense
              flat
              :icon="dialog === 'edit' ? 'mdi-close' : 'mdi-pencil-box'"
              @click="dialog === 'edit' ? dialog = '' : dialog = 'edit'"
          ></q-btn>
          <q-btn
              size="sm"
              dense flat
              icon="mdi-delete"
              @click="dialog = 'remove'"
          ></q-btn>
        </div>
      </q-item-section>
    </q-item>
    <div class="row">
      <q-slide-transition>
        <div v-if="!!dialog" class="q-py-sm">
          <template v-if="dialog === 'edit'">
            <cap-form
                :ucan-path="ucanPath"
                :extra-caps="extraCaps"
                :subject="subject"
                :model-value="modelValue"
                @update:model-value="handleUpdate"
            >
              <template v-slot:extraNamespace="scope">
                <slot name="extraNamespace" v-bind="scope"></slot>
              </template>
            </cap-form>
          </template>
          <template v-if="dialog === 'remove'">
            <div class="text-weight-medium font-3-4r">Remove this UCAN?</div>
            <div class="row justify-end q-my-xs">
              <q-btn size="sm" flat icon="mdi-cancel" color="negative" label="No" @click="dialog = ''"></q-btn>
              <q-btn size="sm" flat icon="mdi-check" color="positive" label="Yes" @click="remove"></q-btn>
            </div>
          </template>
        </div>
      </q-slide-transition>
    </div>
  </div>
</template>

<script setup>
  import CapForm from '../forms/CapForm.vue';

  import {computed, ref} from 'vue';
  import {_get} from 'symbol-syntax-utils';
  import {defaultScheme, SUPERUSER} from 'src/utils/ucans';
  import {$limitStr} from 'src/utils/global-methods';

  const props = defineProps({
    ucanPath: { type: String, default: 'ucan' },
    modelValue: Object,
    subject: { required: false },
    subjectPath: String,
    extraCaps: Array,
    simple: Boolean
  })

  const emit = defineEmits(['update:model-value', 'remove'])

  const dialog = ref('');


  const handleUpdate = (ucan) => {
    // console.log('handle update in item', ucan, props.modelValue);
    if (ucan && JSON.stringify(ucan) !== JSON.stringify(props.modelValue)) {
      // console.log('handle update pass');
      emit('update:model-value', ucan);
      dialog.value = '';
    }
  };

  const remove = () => {
    emit('remove');
    dialog.value = '';
  };

  const scheme = computed(() => _get(props.modelValue, 'with.scheme', props.modelValue?.scheme || defaultScheme));
  const hierPart = computed(() => _get(props.modelValue, 'with.hierPart', props.modelValue?.hierPart));
  const namespace = computed(() => _get(props.modelValue, 'can.namespace', props.modelValue?.namespace));
  const segments = computed(() => _get(props.modelValue, 'can.segments', props.modelValue?.segments || []));
  const isSuperuser = computed(() => _get(props.modelValue, 'can') === SUPERUSER);
</script>

<style lang="scss" scoped>

</style>
