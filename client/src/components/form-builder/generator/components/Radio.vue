<template>
  <div id="Radio" v-bind="divAttrs">
    <div class="q-pa-md" style="font-size: .9rem; font-weight: 300">
      {{ inputAttrs.label || 'Multiple Choice' }}
    </div>
    <div :style="inputAttrs.divStyle">
      <div
        class="q-mr-sm"
        style="display: flex; align-items: center"
        v-for="(option, i) in options"
        :key="`radio-option-${i}`"
      >
        <q-radio
          :label="option"
          :name="path"
          :model-value="modelValue"
          :val="option"
          @update:model-value="handleInput"
          v-bind="inputAttrs"
          v-on="listeners"
        >
          <template v-for="slot in slots" v-slot:[slot]="slotProps">
            <slot :name="slot" :key_name="path" v-bind="slotProps"></slot>
          </template>
        </q-radio>
        <q-btn
          class="q-mx-sm"
          round
          flat
          icon="mdi-delete"
          size="sm"
          color="red"
          v-if="inputAttrs.editing"
          @click="$emit('rmv', i)"
        />
      </div>
    </div>
    <q-btn
      v-if="inputAttrs.editing"
      size="sm"
      round
      icon="mdi-plus"
      class="bg-light-blue"
      @click="$emit('new')"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, useAttrs } from 'vue';
import { _get } from 'symbol-syntax-utils';

defineOptions({
  name: 'Radio',
  inheritAttrs: false
});

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: ''
  },
  path: {
    type: String,
    required: true
  },
  slots: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:model-value', 'rmv', 'new']);
const attrs = useAttrs();

// Default options
const options = ref([
  'Option 1',
  'Option 2'
]);

// Computed properties for attrs
const inputAttrs = computed(() => {
  const attrsObj = attrs.attrs || {};

  // Handle rules
  if (attrsObj.rules) {
    const rules = [...(attrsObj.rules || [])];

    // Add required rule if needed
    if (attrsObj.required) {
      const label = attrsObj.label || 'This field';
      const requiredRule = val => val !== '' || `${label} is required`;

      // Only add if not already present
      if (!rules.map(item => item.toString()).includes(requiredRule.toString())) {
        rules.push(requiredRule);
      }
    }

    return {
      ...attrsObj,
      label: attrsObj.label || 'Multiple Choice',
      rules
    };
  }

  return {
    ...attrsObj,
    label: attrsObj.label || 'Multiple Choice'
  };
});

// Computed property for div attributes
const divAttrs = computed(() => {
  const divAttrsObj = attrs['div-attrs'] || {};
  return {
    ...divAttrsObj,
    class: divAttrsObj.class || 'col-12 col-sm-6'
  };
});

// Computed property for listeners (excluding update:model-value)
const listeners = computed(() => {
  const allListeners = {};

  // Copy all listeners except update:model-value
  Object.keys(attrs).forEach(key => {
    if (key.startsWith('on') && key !== 'onUpdate:modelValue') {
      // Convert from onEvent to event format
      const eventName = key.slice(2).toLowerCase();
      allListeners[eventName] = attrs[key];
    }
  });

  return allListeners;
});

// Watch for options in attrs
watch(() => _get(attrs, 'attrs.options'), (newOptions) => {
  if (newOptions && newOptions.length) {
    options.value = newOptions;
  }
}, { immediate: true, deep: true });

// Handle input changes
const handleInput = (val) => {
  if (_get(attrs, 'attrs.multiple')) {
    if (Array.isArray(props.modelValue) && props.modelValue.length) {
      const newVal = [...props.modelValue];
      const idx = newVal.indexOf(val);

      if (idx === -1) {
        newVal.push(val);
      } else {
        newVal.splice(idx, 1);
      }

      emit('update:model-value', newVal);
    } else {
      emit('update:model-value', [val]);
    }
  } else {
    emit('update:model-value', val);
  }
};
</script>

<style scoped lang="scss">
</style>
