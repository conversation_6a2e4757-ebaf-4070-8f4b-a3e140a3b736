<template>
  <div id="MultipleChoice" v-bind="divAttrs">
    <div class="q-pa-md" style="font-size: .9rem; font-weight: 300">
      {{ inputAttrs.label || 'Multiple Choice' }}
    </div>
    <div :style="inputAttrs.divStyle || {}">
      <div
        class="q-mr-sm"
        style="display: flex; align-items: center"
        v-for="(opt, i) in options"
        :key="`choice-option-${i}`"
      >
        <q-checkbox
          @update:model-value="handleInput(opt)"
          v-bind="{
            ...inputAttrs,
            label: opt,
            name: path,
            modelValue: modelValue,
            toggleIndeterminate: false,
            indeterminateValue: '*',
            trueValue: inputAttrs.multiple ? modelValue && modelValue.includes(opt) : opt,
            val: opt
          }"
          v-on="listeners"
        >
          <template v-for="slot in slots" v-slot:[slot]="slotProps">
            <slot :name="slot" :key_name="path" v-bind="slotProps"></slot>
          </template>
        </q-checkbox>
        <q-btn
          class="q-mx-sm"
          round
          flat
          icon="mdi-delete"
          size="sm"
          color="red"
          v-if="inputAttrs.editing"
          @click="$emit('rmv', i)"
        />
      </div>
    </div>
    <q-btn
      v-if="inputAttrs.editing"
      size="sm"
      round
      icon="mdi-plus"
      class="bg-light-blue"
      @click="$emit('new')"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, useAttrs } from 'vue';
import { _get } from 'symbol-syntax-utils';

const emit = defineEmits(['update:model-value', 'rmv', 'new']);
const attrs = useAttrs();

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: null
  },
  path: {
    type: String,
    default: ''
  },
  slots: {
    type: Array,
    default: () => []
  }
});

// Default options
const options = ref([
  'Option 1',
  'Option 2'
]);

// Computed properties for attrs
const inputAttrs = computed(() => {
  const attrsObj = attrs.attrs || {};

  // Handle rules
  if (attrsObj.rules) {
    const rules = [...(attrsObj.rules || [])];

    // Add required rule if needed
    if (attrsObj.required) {
      const label = attrsObj.label || 'This field';
      const requiredRule = val => val !== '' || `${label} is required`;

      // Only add if not already present
      if (!rules.map(item => item.toString()).includes(requiredRule.toString())) {
        rules.push(requiredRule);
      }
    }

    return {
      ...attrsObj,
      label: attrsObj.label || 'Multiple Choice',
      rules
    };
  }

  return {
    ...attrsObj,
    label: attrsObj.label || 'Multiple Choice'
  };
});

// Computed property for div attributes
const divAttrs = computed(() => {
  const divAttrsObj = attrs['div-attrs'] || {};
  return {
    ...divAttrsObj,
    class: divAttrsObj.class || 'col-12 col-sm-6'
  };
});

// Computed property for listeners (excluding update:model-value)
const listeners = computed(() => {
  const allListeners = {};

  // Copy all listeners except update:model-value
  Object.keys(attrs).forEach(key => {
    if (key.startsWith('on') && key !== 'onUpdate:modelValue') {
      // Convert from onEvent to event format
      const eventName = key.slice(2).toLowerCase();
      allListeners[eventName] = attrs[key];
    }
  });

  return allListeners;
});

const attrOptions = computed(() => _get(attrs, 'attrs.options', []));

// Watch for options in attrs
watch(attrOptions, (nv) => {
  console.log('attr option change', nv);
  if (nv && nv.length) {
    options.value = [...nv];
  }
}, { immediate: true, deep: true });

// Get the color for a checkbox based on its state
const getCheckboxColor = (option) => {
  const attrsObj = attrs.attrs || {};

  if (!attrsObj) return 'grey-6';

  if (attrsObj.multiple) {
    return props.modelValue && Array.isArray(props.modelValue) && props.modelValue.includes(option)
      ? (attrsObj.color || 'primary')
      : 'grey-6';
  } else {
    return props.modelValue === option
      ? (attrsObj.color || 'primary')
      : 'grey-6';
  }
};

// Handle input changes
const handleInput = (val) => {
  const attrsObj = attrs.attrs || {};

  if (attrsObj.multiple) {
    if (Array.isArray(props.modelValue) && props.modelValue.length) {
      const newVal = [...props.modelValue];
      const idx = newVal.indexOf(val);

      if (idx === -1) {
        newVal.push(val);
      } else {
        newVal.splice(idx, 1);
      }

      emit('update:model-value', newVal);
    } else {
      emit('update:model-value', [val]);
    }
  } else {
    emit('update:model-value', val);
  }
};
</script>

<style scoped lang="scss">
</style>
