<template>
  <div style="height: 100%; width: 100%; position: relative">
    <div class="q-pa-md text-xs text-mb-xs text-weight-medium">Choose Field Type</div>
    <div class="col-12 q-gutter-lg">
      <q-card square flat class="q-pa-sm">
        <q-list style="width: 100%; max-width: 90vw;" separator>
          <q-item clickable @click="emit('welcome')">
            <q-item-section avatar>
              <q-icon name="mdi-hand-back-right"/>
            </q-item-section>
            <q-item-section>
              <q-item-label class="font-1r text-mb-sm tw-five">Welcome Screen</q-item-label>
            </q-item-section>
          </q-item>
          <q-item v-for="(field, i) in Object.keys(formFields)" :key="`field-${i}`" clickable @click="chooseField(formFields[field])" style="position: relative; padding-top: 30px;">
            <div :class="`t-l q-px-sm font-7-8r text-mb-xxs text-weight-medium text-${formFields[field].color}`">{{formFields[field].label}}</div>
            <q-item-section avatar>
              <q-icon :name="formFields[field].icon" :color="formFields[field].color"/>
            </q-item-section>
            <div style="width: 350px; overflow-x: scroll">
            <div class="row q-pt-md" :style="{ width: '300px', ...formFields[field].demoStyle, overflowY: 'scroll' }">
              <form-generator
                style="width: 100%"
                :primary-color="formColor"
                :secondary-color="buttonColor"
                :model-value="formFields[field].value"
                :path="formData[formFields[field].path]"
                :host="host"
                :fields="[{
                  fieldType: formFields[field].fieldType,
                  attrs: {...formFields[field].attrs, host: host, demo: true},
                  'div-attrs': formFields[field]['div-attrs']
                }]"
                :slots="formFields[field].slots">
<!--                <template v-for="(slot, i) in formOptions[field.slots]"-->
<!--                          v-slot:[slot.name]="scope">-->
<!--                  <div :key="`slot-${i}`" v-html="sanitize(slot.html)" v-bind="scope"></div>-->
<!--                </template>-->
              </form-generator>
            </div>
            </div>
          </q-item>
          <q-item clickable @click="emit('finish')">
            <q-item-section avatar>
              <q-icon name="mdi-exit-to-app" color="dark"/>
            </q-item-section>
            <q-item-section>
              <q-item-label class="font-1r  tw-five">Finish Screen</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card>
    </div>
  </div>
</template>

<script setup>
  import FormGenerator from '../generator/FormGenerator.vue';

  import { formFields } from '../utils/form-fields';
  import {$infoNotify} from 'src/utils/global-methods';
  import {ref} from 'vue';

  const emit = defineEmits(['finish', 'update:model-value', 'welcome']);

  const props = defineProps({
    formColor: String,
    buttonColor: String,
    host: Object
  })

  const formData = ref({})

  const chooseField = (field) => {
    if(!field.disable()) {
      field.color = props.formColor
      field.attrs.color = props.buttonColor;
      emit('update:model-value', field);
    } else $infoNotify(field.disableMessage || 'Field not enabled');
  }

</script>
