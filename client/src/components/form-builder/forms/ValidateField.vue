<template>
  <div>
    <q-select
      :hide-bottom-space="hideBottomSpace"
      :dense="dense"
      :label="label"
      :options="vList"
      v-model="form.check"
      emit-value
      option-value="key"
      option-label="label"
      @update:model-value="checkInput"
      :hint="hint"
    >
      <template v-slot:selected-item>
        <q-chip dark square :label="activeOption.label" removable @remove="form = {}"></q-chip>
      </template>
      <template v-slot:option="scope">
        <q-item clickable @click="scope.toggleOption(scope.opt)">
          <q-item-section>
            <q-item-label class="text-xxs text-mb-xxs text-weight-medium">{{scope.opt.label}}</q-item-label>
            <q-item-label caption>{{scope.opt.description}}</q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>
    <q-slide-transition>
      <div :class="dense ? '' : 'q-py-md'" v-if="form.check && activeOption.args">
        <q-chip class="q-ma-sm" v-for="(opt, idx) in optionsIn || []" :key="`opt-${idx}`" :label="opt" clickable @click="form.arg = opt; checkInput(opt)"></q-chip>
        <q-input input-class="text-xs text-mb-xs text-weight-medium" :label="argumentLabel" v-model="form.arg" :hint="argumentHint" :dense="dense" @update:model-value="checkInput"></q-input>
      </div>
    </q-slide-transition>
    <q-slide-transition>
      <div v-if="form.check && !noError">
        <q-input dense label="Custom Error Message" v-model="form.error" @update:model-value="checkInput"></q-input>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import * as validators from 'src/stores/validate/validators'
  import {ref, watch, computed} from 'vue';

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    optionsIn: Array,
    dense: { type: Boolean, default: true },
    noError: Boolean,
    label: { type: String, default: 'Validate Field' },
    argumentHint: { type: String, default: 'value to test against' },
    argumentLabel: { type: String, default: 'Enter Argument' },
    hint: { type: String, default: 'Choose Validator' },
    hideBottomSpace: Boolean,
    modelValue: Object,
    field: Object,
    validatorsWhitelist: Array
  })

  const form = ref({})

  watch(() => props.modelValue, (nv) => {
    if(nv) form.value = nv;
    else form.value = {}
  }, { immediate: true })

  const activeOption = computed(() => {
    for(const k in validators){
      if(validators[k].key === form.value.check) return validators[k];
    }
    return {}
  })

  const vList = computed(() => {
    if(Array.isArray(props.validatorsWhitelist)) {
      return props.validatorsWhitelist.map(a => validators[a]);
    } else return Object.values(validators);
  })

  const checkInput = () => {
    const val = Object.assign({}, form.value);
    const check = val.check;
    if(check){
      const args = activeOption.value.args;
      if(!args || (args && val.arg)){
        emit('update:modelValue', val);
      }
    }
  }

</script>

<style scoped>

</style>
