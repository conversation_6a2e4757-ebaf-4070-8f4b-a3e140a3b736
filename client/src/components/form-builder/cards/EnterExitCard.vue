<template>
  <q-card flat class="q-pt-md q-px-md q-pb-xl" style="height: 100%">
    <div v-if="image" class="row justify-center q-my-sm">
      <q-card style="height: 200px; width: 200px" flat>
        <q-img contain :src="image"></q-img>
      </q-card>
    </div>
    <div v-if="title" :class="`q-my-md text-center text-sm text-mb-md text-weight-medium text-${modelValue.primaryColor || 'ir-text'}`">{{title}}</div>
    <q-separator :color="modelValue.primaryColor || 'ir-text'" v-if="message" class="q-my-md"></q-separator>
    <div class="row justify-center" v-if="videos && videos.length">
      <q-card style="max-width: 400px; width: 95vw">
        <multi-video-viewer :urls="videos.map(a => a.url)"></multi-video-viewer>
      </q-card>

    </div>

    <div v-if="message" class="q-my-md text-xs text-centerhat  text-mb-sm text-weight-light text-dark" v-html="sanitize(message)"></div>

    <div class="q-my-sm q-pa-sm" v-if="files && files.length">
      <div class="text-xxs text-mb-xxs text-weight-light">Download Files</div>
    <div class="row no-wrap" style="width: 100%; overflow-x: scroll">
      <q-card class="q-ma-xs" flat v-for="(file, i) in files" :key="`file-${i}`">
        <file-preview :file="file" name-on></file-preview>
      </q-card>
    </div>
    </div>

    <div class="row justify-center q-mt-xl q-mb-md" style="width: 100%" v-if="!exit">
      <q-btn :color="modelValue.primaryColor ? modelValue.primaryColor : 'primary'" label="Start" push size="lg" @click="emit('start')"/>
    </div>
  </q-card>
</template>

<script setup>
  import FilePreview from 'components/common/uploads/pages/FilePreview.vue';
  import MultiVideoViewer from 'components/common/uploads/video/MultiVideoViewer.vue';

  import {computed, ref, watch} from 'vue';
  import {useUploads} from 'stores/uploads';
  import { sanitize } from 'src/utils/sanitize';

  const uploadStore = useUploads()

  const emit = defineEmits(['start'])
  const props = defineProps({
    exit: Boolean,
    modelValue: Object
  })

  const videos = ref([])

  const loadVideos = async () => {
    const path = props.exit ? 'finishVideos' : 'welcomeVideos';
    const list = (props.modelValue || {})[path] || [];
    await uploadStore.find({
      query: {
        _id: { $in: list.map(a => a.uploadId) }
      }
    }).then(res => {
      console.log('video response');
      videos.value = res.data
    })
      .catch(err => console.log('error loading videos', err));
  }

  const formVideos = computed(() => {
    const path = props.exit ? 'finishVideos' : 'welcomeVideos';
    return (props.modelValue || {})[path] || []
  })

  watch(formVideos, (nv, ov) => {
    if(nv && (!ov || nv.length !== ov.length)){
      loadVideos();
    }
  }, { immediate: true })

  const title = computed(() => {
    const f = props.modelValue || {}
    if(props.exit) return f['finishTitle'];
    return f['welcomeTitle'] || f['name'];
  })

  const message = computed(() => {
    const f = props.modelValue || {}
    if(props.exit) return f['finishMessage'];
    return f['welcomeMessage'] || f['name'];
  })

  const files = computed(() => {
    const f = props.modelValue || {}
    if(props.exit) return f['finishFiles'];
    return f['welcomeFiles'];
  })

  const image = computed(() => {
    const f = props.modelValue || {}
    if(props.exit) return f['finishImage'];
    return f['welcomeImage'];
  })

</script>
