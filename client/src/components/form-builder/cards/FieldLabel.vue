<template>
  <div class="__icon_btn row items-center q-mb-md">
    <template v-if="modelValue === 'welcome'">
      <q-btn color="pink" dense>
        <div class="row items-center">
          <q-icon name="mdi-hand"/>
        </div>
      </q-btn>
      <div class="q-ml-md text-sm text-mb-sm text-weight-medium text-pink">
        Welcome
      </div>
    </template>
    <template v-else-if="modelValue === 'finish'">
      <q-btn color="dark" dense>
        <div class="row items-center">
          <q-icon name="mdi-exit-to-app"/>
        </div>
      </q-btn>
      <div class="q-ml-md text-sm text-mb-sm text-weight-medium text-dark">
        Finish Screen
      </div>
    </template>
    <template v-else>
      <q-btn :color="_get(modelValue, 'color', 'primary')" dense>
        <div class="row items-center">
          <div class="q-mr-sm">{{ index + 1 }}</div>
          <q-icon :name="_get(modelValue, 'icon')"/>
        </div>
      </q-btn>
      <div :class="`q-ml-md text-sm text-mb-sm text-weight-medium text-${_get(modelValue, 'color', 'primary')}`">
        {{ _get(modelValue, 'label') }}
      </div>
    </template>
  </div>
</template>

<script setup>
  import { _get} from 'symbol-syntax-utils';

  const props = defineProps({
    modelValue: [String, Object],
    index: { type: Number, default: 0 }
  })
</script>

<style scoped>

</style>
