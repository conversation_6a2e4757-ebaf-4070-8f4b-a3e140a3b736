<template>
  <div class="form_slider q-px-sm q-pb-md fill_size" style="padding-top: 40px">
  <div class="row items-center">
    <q-chip color="transparent" v-if="!welcome" clickable @click="welcome = true;tab = 0;" class="tw-five">
      <q-icon name="mdi-refresh"></q-icon>
      <span>Start Over</span>
    </q-chip>
    <q-space></q-space>
    <div v-if="avatar">
      <q-img style="height: 65px; width: 65px" :src="avatar"></q-img>
    </div>
  </div>

    <q-slide-transition>
      <template v-if="welcome">
        <enter-exit-card @start="welcome = false" :model-value="form"></enter-exit-card>
      </template>
    </q-slide-transition>
    <q-slide-transition>
      <div v-if="!welcome && !finish">
        <template v-if="!form.fields?.length">
          <div style="width: 100%" class="flex flex-center">
            <div class="text-sm text-mb-sm text-weight-light">
              This form is empty...
            </div>
          </div>
        </template>
        <template v-if="displayIn === 'slider'">
          <div v-for="(field, i) in form.fields || []" :key="`field-${i}`"
               :style="tab === i ? { width: '100%'} : {display: 'none'}" class="flex flex-center">
            <q-slide-transition>
              <template v-if="tab === i">
                <div @keyup.enter="advance" :class="`q-pa-${$q.screen.name}`" style="width: 100%; position: relative">
                  <div class="__icon_btn q-py-md row items-center">
                    <q-btn :color="field.color" dense>
                      <div class="row items-center">
                        <div class="q-mr-sm">{{ i + 1 }}</div>
                        <q-icon :name="field.icon"/>
                      </div>
                    </q-btn>
                    <div :class="`q-ml-md text-sm text-mb-sm text-weight-medium text-${field.color}`">
                      {{ field.label }}
                    </div>
                  </div>
                  <form-generator
                      :primary-color="form.primaryColor"
                      :secondary-color="form.secondaryColor"
                      @advance="advance"
                      :path="field.id"
                      :fields="[{...field}]"
                      :model-value="response.formData"
                      @update:model-value="handleResponse(i, $event)"></form-generator>
                </div>
              </template>
            </q-slide-transition>
          </div>
        </template>
        <template v-else>
          <div class="q-pa-sm">
            <template v-for="(field, i) in form.fields || []" :key="`field-${i}`">
              <div  class="__c q-pa-md q-my-md">

                <div @keyup.enter="advance" :class="`q-pa-${$q.screen.name}`" style="width: 100%; position: relative">
                  <field-label
                      :model-value="field"
                      :index="i"
                  ></field-label>

                  <form-generator
                      :primary-color="form.primaryColor"
                      :secondary-color="form.secondaryColor"
                      @advance="advance"
                      :appHost="appHost"
                      :path="field.label"
                      :fields="[field]"
                      :model-value="response.formData"
                      @update:model-value="handleResponse(i, $event)"
                  ></form-generator>

                </div>

              </div>
            </template>
          </div>
        </template>
      </div>
    </q-slide-transition>
    <q-slide-transition>
      <template v-if="finish">
        <enter-exit-card exit :model-value="form"></enter-exit-card>
      </template>
    </q-slide-transition>
    <div class="row items-center q-my-lg" style="height: 100%">
      <div class="col-12 col-md-6 q-pa-sm">
        <div class="row justify-center">
          <template v-if="displayIn === 'slider'">
            <div style="position: relative; width: 50%" v-if="!finish && !welcome">
              <div class="__counter text-xxxs text-mb-xxxs text-grey-6 text-uppercase text-weight-bold">{{ tab + 1 }} of
                {{ form.fields?.length || 0 }}
              </div>
              <q-linear-progress style="width: 100%" :color="form.secondaryColor || 'secondary'" rounded size="7px" :value="(tab + 1) / (form.fields?.length || 1)"/>
            </div>
          </template>
          <template v-else>
            <div class="font-3-4r text-ir-mid tw-six">{{$possiblyPlural('Field', form.fields?.length)}}</div>
          </template>
        </div>
      </div>
      <div class="col-12 col-md-6 q-pa-sm">
        <div class="row justify-center">
          <template v-if="!finish && !welcome">
            <q-btn
                flat
                v-if="tab > 0"
                class="q-mr-sm tw-six"
                no-caps
                label="Back"
                icon="mdi-chevron-left"
                @click="goBack"
                :color="form.secondaryColor || 'primary'" dense/>
            <template v-if="tab < (form.fields?.length || 1) -1 && displayIn === 'slider'">
              <q-btn
                  label="Next"
                  icon-right="mdi-chevron-right"
                  class="tw-six"
                  flat
                  no-caps
                  @click="advance"
                  :color="form.secondaryColor || 'primary'"/>
            </template>
            <template v-else>
              <q-btn
                  push
                  label="Submit"
                  icon="mdi-check"
                  :color="form.secondaryColor || 'primary'"
                  @click="submitForm(true)" size="sm"/>
            </template>
          </template>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
  import EnterExitCard from './EnterExitCard.vue';
  import FieldLabel from './FieldLabel.vue';
  import FormGenerator from '../generator/FormGenerator.vue';

  import {computed} from 'vue';

  import {formViewer} from 'components/form-builder/utils/form-viewer';
  import {$possiblyPlural} from 'src/utils/global-methods';

  const emit = defineEmits(['tab', 'finish'])
  const props = defineProps({
    formIn: Object,
    responseIn: Object,
    displayIn: {
      type: String,
      default: 'slider'
    },
    previewMode: Boolean,
    apiKey: String,
    changeCounter: { required: false },
    tabIn: Number,
    appHost: Object,
    welcomeIn: {
      type: [Boolean, String],
      default: '*'
    },
    finishIn: {
      type: [Boolean, String],
    }
  })

  const options = computed(() => {
    const obj = {};
    for(const k in props){
      obj[k] = props[k]
    }
    return obj;
  })

  const {
    setTab,
    advance,
    goBack,
    handleResponse,
    submitForm,
    form,
    tab,
    response,
    avatar,
    welcome,
    finish
  } = formViewer(options, { emit })


</script>

<style scoped lang="scss">
  .form_slider {
    width: 100%;
    height: 100%;
    position: relative;
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: auto 1fr auto auto;
  }

  .__icon_btn {
    z-index: 2;
    //transform: translate(0, -100%);
  }

  .__counter {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -100%);
  }
  .__c {
    position: relative;
    padding: 8px;
    margin: 6px 0;
    border-radius: 12px;
    background: white;
    box-shadow: 0 2px 8px var(--ir-light);
  }
</style>
