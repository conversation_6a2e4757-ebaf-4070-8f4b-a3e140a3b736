<template>
  <q-page>
    <div class="row justify-center">
      <div class="_cent pw2 pd5" v-if="person._id">
        <div class="q-pa-md flex items-center">
          <div class="text-sm tw-six">Form Builder</div>
          <q-btn dense flat icon="mdi-plus" color="primary" @click="open()"></q-btn>
        </div>

        <div class="q-pa-sm w500 mw100">
          <q-input dense filled v-model="search.text">
            <template v-slot:append>
              <q-icon name="mdi-magnify" />
            </template>
          </q-input>
        </div>
        <div class="q-py-lg row">
          <div class="col-12 col-md-4 q-pa-sm" v-for="(fb, i) in f$.data" :key="`fb-${i}`">
            <div class="__c _hov" @click="open(fb._id)">
            <form-card :model-value="fb"></form-card>
            </div>
          </div>
        </div>

        <pagination-row v-bind="{ pageRecordCount, h$:f$, pagination, limit}"></pagination-row>
      </div>
    </div>

  </q-page>
</template>

<script setup>
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';
  import FormCard from 'components/form-builder/cards/FormCard.vue';

  import {computed, ref, watch} from 'vue';
  import {trackContext} from 'layouts/utils/track-context';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';
  import {HFind} from 'src/utils/hFind';
  import {useFbs} from 'stores/fbs';
  import {HQuery} from 'src/utils/hQuery';
  import { useRouter } from 'vue-router';
  import {useOrgs} from 'stores/orgs';

  const router = useRouter();
  const fbStore = useFbs();
  const orgStore = useOrgs();

  const { login, person } = loginPerson();

  const open = (id) => {
    const rt = { name: 'form-builder' };
    if(id) rt.params = { formId: id };
    const { href } = router.resolve(rt);
    window.open(href, '_blank');
  }

  const { orgId, hostId, hostStore } = trackContext();

  const org = ref({});
  const host = ref({});



  watch(orgId, () => {

    if(!org.value?._id) {
      setTimeout(async () => {
        if (hostId.value) {
          let h = hostStore.getFromStore(hostId.value).value;
          if (!h) h = await hostStore.get(hostId.value);
          if (h) host.value = h;
        }
        if (orgId.value) {
          let o = orgStore.getFromStore(orgId.value).value;
          if (!o) o = await orgStore.get(orgId.value);
          if (o) org.value = org;
        }
      }, 250)
    }

  }, { immediate: true })

  const cap_subjects = computed(() => {
    const arr = [];
    if(host.value._id) arr.push(host.value._id);
    else if(org.value._id) arr.push(org.value._id);
    return arr;
  })

  const caps = computed(() => {
    const arr = [['fbs', 'WRITE']]
    if(host.value._id) {
      arr.push([`hosts:${host.value._id}`, 'hostAdmin'])
      arr.push([`orgs:${host.value._id}`, 'orgAdmin'])
    }
    else if(org.value._id) arr.push([`orgs:${org.value._id}`, 'orgAdmin'])
    return arr;
  })
  const {canEdit} = clientCanU({
    subject: computed(() => host.value || org.value),
    or: true,
    caps,
    login,
    cap_subjects
  })

  const { search, searchQ } = HQuery({})

  const limit = ref(10)
  const { h$:f$, pageRecordCount, pagination } = HFind({
    store: fbStore,
    limit,
    params: computed(() => {
      const query = {
        $sort: { createdAt: -1 },
        ...searchQ.value
      }
      if(canEdit.value.ok) {
        if (host.value?._id) query.owner = host.value._id
        if (org.value?._id) query.owner = org.value._id
      } else query.$or = [{ owner: person.value._id }, { canEdit: { $in: [person.value._id]}}]
      return {
        query
      }
    })
  })


</script>

<style>
  .__c {
    border-radius: 10px;
    box-shadow: 2px 2px 8px var(--ir-light);
    cursor:pointer;
  }
</style>
