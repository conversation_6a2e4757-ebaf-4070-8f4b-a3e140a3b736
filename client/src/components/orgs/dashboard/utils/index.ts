import {computed, ComputedRef, ref, Ref, watch} from 'vue';
import {HFind} from 'src/utils/hFind.js';
import {getCurrentPlanYear} from 'components/plans/utils/index.js';
import {usePlans} from 'stores/plans.js';
import {LocalStorage} from 'symbol-auth-client';

export const orgPlans = (org:Ref<any>|ComputedRef<any>) => {
    const planStore:any = usePlans()

    const yearSelect = ref();


    const { h$:p$ } = HFind({
        store: planStore as any,
        pause: computed(() => !org.value),
        params: computed(() => {
            return {
                query: {
                    planYearStart: { $gte: new Date(`01-01-${yearSelect.value}`).getTime() },
                    org: org.value?._id,
                    // active: true
                }
            }
        })
    })

    const planSelect = ref(undefined);

    const activePlan = computed(() => {
        const id = LocalStorage.getItem('plan_id');
        if(planSelect.value) return planSelect.value;
        else if(id) return planStore.findInStore({ query: { _id: id }})?.data[0];
        else if(p$.data) return p$.data[0];
        else return { name: 'No Plans Found' }
    })
    const activeYear = computed(() => yearSelect.value || getCurrentPlanYear(activePlan.value));

    watch(activeYear, (nv) => {
        if(nv) yearSelect.value = nv;
    }, { immediate: true })


    return {
        activePlan,
        activeYear,
        planSelect,
        yearSelect,
        p$,
        planStore
    }
}
