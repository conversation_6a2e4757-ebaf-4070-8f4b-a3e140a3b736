<template>
  <div class="_fw">
    <plan-year-enrollments v-if="plan" :plan="plan" :year="year"></plan-year-enrollments>
  </div>
</template>

<script setup>
  import PlanYearEnrollments from 'components/plans/enrollments/cards/PlanYearEnrollments.vue';

  import {computed, ref} from 'vue';
  import {orgPlans} from 'components/orgs/dashboard/utils';

  const props = defineProps({
    org: { required: true },
    plan: { required: true },
    year: { required: true }
  })

  const fullOrg = computed(() => props.org || {});

</script>

<style lang="scss" scoped>

</style>
