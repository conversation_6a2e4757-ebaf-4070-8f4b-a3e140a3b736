import {computed, ref, watch, nextTick, ComputedRef, Ref} from 'vue';
import {_get} from 'symbol-syntax-utils';
import {useCoverages} from 'stores/coverages';
import {useClaims} from 'stores/claims';
import {AnyRef} from 'src/utils/types'

export const getAdjTotal = (claim: any) => {
    if (!claim) return 0
    const adjTotal = claim.adj?.total;
    const balance = claim.balance || 0;
    if (adjTotal || adjTotal === 0) return Math.min(adjTotal, balance);
    else return balance
}

export const getPayToday = (obj: any) => {
    let total = 0;
    const ids = Object.keys(obj || {});
    for (let i = 0; i < ids.length; i++) {
        total += obj[ids[i]] || 0;
    }
    return total;
}
export const lineItems = (visit: AnyRef, claim?: AnyRef, options?: { emitById: (...val: any) => void }) => {
    const claimStore: any = useClaims();

    const claims: Ref<any> = ref({data: []});
    const byId: Ref<any> = ref({});
    const balance = computed(() => {
        if (claim?.value?._id) return claim.value.balance;
        else return visit.value?.balance || 0;
    })

    const hasBeenAdj = computed(() => {
        let isIt: any = false;
        const arr = claims.value || []
        for (let i = 0; i < arr.length; i++) {
            isIt = arr[i].adj;
            if (!isIt?.adjAt) {
                break;
            }
        }
        return isIt;
    })

    const amount = computed(() => {
        if (claim?.value?._id) return claim.value.amount;
        else {
            let amt = 0;
            for (let i = 0; i < claims.value?.length; i++) {
                amt += claims.value[i].amount || 0;
            }
            return amt;
        }
    })

    const total: ComputedRef<any> = computed(() => {
        return getPayToday(byId.value)
    })
    const setById = async () => {
        if (claim?.value?._id) {
            byId.value[claim.value._id] = getAdjTotal(claim.value);
            claims.value = [claim.value];
        } else if (visit.value?._id) {
            const ids = visit.value.claims;
            const query = {_id: {$in: ids}, $limit: ids.length}
            let c = claimStore.findInStore({query})
            if (c.total !== ids.length || !Object.keys(c.data[0]).includes('networks')) c = await claimStore.find({
                query,
                runJoin: {claim_prices: true}
            })
            for (let i = 0; i < c.data.length; i++) {
                byId.value[c.data[i]._id] = getAdjTotal(c.data[i]);
            }
            if (c) claims.value = c;
        }
        if (options?.emitById) {
            nextTick(() => options.emitById(byId.value, total.value))
        }
    }

    watch(balance, (nv, ov) => {
        if ((nv || nv === 0) && nv !== ov) {
            setById()
        }
    }, {immediate: true})
    return {
        hasBeenAdj,
        total,
        amount,
        balance,
        claims,
        byId,
        setById
    }
}

type DiscountType = 'flat' | 'percent';
type DiscountObj = {
    coins_discount: number,
    coins_discount_type: DiscountType,
    ded_discount: number,
    ded_discount_type: DiscountType
}
const calcDiscount = (amount: number, root: 'ded' | 'coins', obj: Partial<DiscountObj>) => {
    const num = obj[`${root}_discount`] || 0;
    const type = obj[`${root}_discount_type`];
    let amt = num;
    if (type === 'percent') amt = (amount * (num / 100));
    return amt;
}

type SplitsOptions = {
    patientId: AnyRef<string>,
    coverage?: AnyRef,
    enrollment: AnyRef,
    preventive: AnyRef<boolean>,
    balance: AnyRef<number>,
    claims?: AnyRef<Array<any>>
}
export const paySplits = ({patientId, coverage, enrollment, preventive, balance, claims}: SplitsOptions) => {

    const coverageStore = useCoverages();

    const balances: Ref<any> = ref({ded: {}, oop: {}});

    const selected: Ref<any> = ref(undefined);
    const useCoverage = computed(() => selected.value || coverage?.value || {})

    const paid: ComputedRef<any> = computed(() => {
        const stats = _get(enrollment.value, `patientClaims.${patientId.value}.${useCoverage.value._id}`);
        const byCoverage = _get(enrollment.value, `coverageClaims.${useCoverage.value._id}`)
        const single: any = stats || {paid: {amount: 0}}
        const family: any = byCoverage || {paid: {amount: 0}}
        return {
            single: {
                ...single,
                oop: (single.paid?.ded || 0) + (single.paid?.coins || 0),
                pendingOop: (single.pending?.ded || 0) + (single.pending?.coins || 0)
            },
            family: {
                ...family,
                oop: (family.paid?.ded || 0) + (family.paid?.coins || 0),
                pendingOop: (family.pending?.ded || 0) + (family.pending?.coins || 0)
            }
        }
    })

    const splits = computed(() => {
        const amt = balance.value || 0
        if (preventive.value) return {person: 0, plan: amt, ded: 0, coins: 0}
        const {type} = enrollment.value || {};
        const isSingle = type === 'single'
        const ded = balances.value.ded.single || 0;
        const dedFamily = isSingle ? ded : balances.value.ded.family || 0;
        //this is what amount is left to pay after deductible is applied
        const dedRemainder = amt - Math.max(0, Math.min(ded, dedFamily));
        const dedTotal = amt - dedRemainder;
        if (dedRemainder <= 0) return {person: amt, plan: 0, ded: amt, coins: 0}
        const oop = balances.value.oop.single || 0;
        const oopFamily = isSingle ? oop : balances.value.oop.family || 0;
        // separate eligible oop
        /** (amount to pay beyond deductible) - (remaining oop max (the most you could have to pay)) - (deductible applied (reducing oop))*/
        const exceededOop = Math.max(0, dedRemainder - (Math.min(oop, oopFamily) - (Math.max(0, dedTotal))));
        const remainingOop = Math.max(0, dedRemainder - exceededOop);
        let coinsurance_amount = 0
        let copay_amount = 0;
        for(const claim of claims){
            const claim_amount = claim.balance || claim.balance === 0 ? claim.balance : claim.amount;
            const factor = claim_amount / amt;
            coinsurance_amount += factor * (claim._fastjoin?.coinsurance_amount || 0);
            copay_amount += factor * (claim._fastjoin?.copay_amount || 0);
        }
        /** calculate the weighted coinsurance and copay amounts by category */

        const coins = remainingOop * (coinsurance_amount/100)
        const newRemainingOop = Math.max(0, remainingOop - coins);
        const copay = Math.max(exceededOop - newRemainingOop, copay_amount);
        const personPays = dedTotal + coins + copay;
        const planPays = amt - personPays;
        return {
            person: personPays,
            plan: planPays,
            ded: dedTotal,
            coins,
            copay
        }
    })

    const setSplits = () => {
        const {family, single} = useCoverage.value.deductible || {};
        const singleDed = (single || 0) * 100 - (paid.value.single.paid?.ded || 0)
        const familyDed = (family || 0) * 100 - (paid.value.family.paid?.ded || 0)
        const remainingDed = {
            single: singleDed,
            family: familyDed,
            single_pending: singleDed - (paid.value.single.pending?.ded || 0),
            family_pending: familyDed - (paid.value.family.pending?.ded || 0)
        }
        const {single: sc, family: fc} = useCoverage.value.moop || {};
        const singleOop = (sc || 0) * 100 - (remainingDed.single + (paid.value.single.paid?.coins))
        const familyOop = (fc || 0) * 100 - (remainingDed.family + (paid.value.family.paid?.coins))
        const remainingOop = {
            single: singleOop,
            family: familyOop,
            single_pending: singleOop - (paid.value.single.pending?.ded || 0),
            family_pending: singleOop - (paid.value.family.pending?.ded || 0),
        }
        balances.value = {ded: remainingDed, oop: remainingOop}
    }

    const discounts = ref({
        coins_discount: 0,
        ded_discount: 0,
        total: 0
    })
    const setDiscounts = () => {
        const networkIds: any = [];
        const arr: any = [...claims?.value || []]
        for (let i = 0; i < arr.length; i++) {
            const nwks = arr[i]._pipeline?.networks;
            if (nwks?.length) {
                for (let n = 0; n < nwks.length; n++) {
                    networkIds.push(nwks[n]?._id)
                }
            }
        }

        console.log('about to set discounts', networkIds, selected.value, coverage?.value);
        const nwks = (selected.value || coverage?.value).networks || {}
        for (let i = 0; i < networkIds.length; i++) {
            const obj = nwks[networkIds[i]];
            console.log('nwks loop', networkIds[i], obj)
            if (obj) {
                const coins = calcDiscount(splits.value.coins, 'coins', obj);
                const ded = calcDiscount(splits.value.ded, 'ded', obj);
                console.log('coins', coins, 'ded', ded);
                discounts.value.coins_discount = Math.max(discounts.value.coins_discount, coins);
                discounts.value.ded_discount = Math.max(discounts.value.ded_discount, ded);
            }
        }

        discounts.value.total = discounts.value.coins_discount + discounts.value.ded_discount;
    }

    watch(enrollment, (nv) => {
        if (nv && !useCoverage.value) {
            if (nv.lastClaimCoverage) selected.value = coverageStore.get(nv.lastClaimCoverage);
        }
    }, {immediate: true})

    watch(useCoverage, (nv, ov) => {
        console.log('see coverage change', nv, selected.value);
        if (nv && nv._id !== ov?._id) {
            setSplits();
            nextTick(() => {
                setDiscounts();
            })
        }
    }, {immediate: true})

    return {
        discounts,
        setSplits,
        paid,
        balances,
        splits,
        coverage: useCoverage,
        selected
    }
}
