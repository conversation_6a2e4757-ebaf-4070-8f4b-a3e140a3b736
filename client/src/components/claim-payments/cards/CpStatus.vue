<template>
  <q-chip v-bind="{color: 'transparent', ...$attrs}">
    <q-avatar :size="size" :color="statuses[modelValue]?.color"></q-avatar>
    <span v-if="!hideLabel">{{statuses[modelValue]?.label || ''}}</span>
  </q-chip>
</template>

<script setup>
  const props = defineProps({
    modelValue: String,
    hideLabel: Boolean,
    size: { default: '10px' }
  })

  const statuses = {
    'request': { label: 'Request', color: 'light-blue' },
    'offer': { label: 'Offer', color: 'purple-3' },
    'pending': { label: 'Pending', color: 'yellow-8' },
    'complete': { label: 'Complete', color: 'green' },
    'cancelled': { label: 'Cancelled', color: 'ir-grey-6' }
  }
</script>

<style lang="scss" scoped>

</style>
