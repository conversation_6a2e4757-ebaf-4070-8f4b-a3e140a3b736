<template>
  <div class="_fw">
    <slot name="input" v-bind="{ search }">
      <q-input class="_fw mw500" filled dense v-model="search.text">
        <template v-slot:prepend>
          <q-icon name="mdi-search"></q-icon>
        </template>
      </q-input>
    </slot>
  </div>
  <div class="__gr">
    <div v-for="(r, i) in r$.data" :key="`r-${i}`" class="__c" @click="emit('select', r)">
      <ref-card square :model-value="r"></ref-card>
    </div>
  </div>
</template>

<script setup>
  import RefCard from 'components/refs/cards/RefCard.vue';

  import {HFind} from 'src/utils/hFind';
  import {useRefs} from 'stores/refs';
  import {computed} from 'vue';
  import {HQuery} from 'src/utils/hQuery';

  const refStore = useRefs();

  const emit = defineEmits(['select'])
  const props = defineProps({
    params: Object,
    limit: Number
  })

  const { search, searchQ } = HQuery({})
  const limit = computed(() => props.limit)
  const { h$:r$ } = HFind({
    store: refStore,
    limit,
    pause: computed(() => !props.params?.query),
    params: computed(() => {
      return {
        runJoin: { ref_person: true },
        ...props.params,
        query: {
          ...props.params?.query,
          ...searchQ.value
        }
      }
    })
  })
</script>

<style lang="scss" scoped>
  .__gr {
    grid-gap: 15px;
    width: 100%;
    padding: 20px 0;
    display: grid;
    grid-template-columns: repeat(auto-fill, auto)
  }
  .__c {
    cursor: pointer;
  }
</style>
