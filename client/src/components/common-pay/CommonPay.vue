<template>
  <q-page>
    <div class="row justify-center pd15">
      <div class="_cent">
        <div class="row">
          <div class="col-12 col-md-6 q-pa-md">
            <div class="alt-font text-weight-bold text-md _l1">When building a healthcare network</div>
            <div class="text-weight-bolder text-xxl">Cash is King</div>
            <div class="row">
              <div class="col-12 col-md-10">
                <div class="alt-font text-sm">It's actually more like: Friction is evil - but cash solves that. Rapid
                  payouts, transparent rates, no bureaucracy to wrestle with. Both sides like it - patients get
                  discounts, providers get paid.
                </div>

                <div class="q-py-sm row">
                  <q-btn no-caps class="text-weight-bold" color="black" icon-right="mdi-chevron-right" label="Sign Up"
                         @click="signUp()"></q-btn>
                </div>
              </div>
            </div>


          </div>
          <div class="col-12 col-md-6 q-pa-md">
            <div class="row justify-center">
              <q-card class="__crd">
                <q-img class="_fa" fit="cover" :src="card"></q-img>
              </q-card>
            </div>
          </div>
        </div>

      </div>
    </div>

    <div class="_fw bg-p1 pd8 relative-position _oh">
      <div class="__blob"></div>
      <div class="__blob2"></div>

      <div class="row justify-center">
        <div class="_cent z1">
          <div class="row q-py-xl">
            <div class="col-12 col-md-6 q-pa-md">
              <div class="font-1r text-weight-bolder alt-font">CommonPay Advantage For</div>
              <div class="text-xxl text-weight-bolder text-p12">Group Plans</div>
              <div class="text-sm alt-font text-weight-medium">Insurance networks - they're kind of like department store sales with
                everything 25% off... that was marked up 100% to start with.
              </div>
              <div class="text-sm alt-font text-weight-bolder q-py-md text-p12">You can do better with CommonPay</div>
              <q-list>
                <q-item v-for="(b, i) in benefits" :key="`b-${i}`">
                  <q-item-section avatar>
                    <q-checkbox :model-value="true"></q-checkbox>
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-weight-medium font-1-1-4r">{{ b }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>

              <div class="row q-px-md q-py-lg">
                <q-btn color="primary" no-caps class="alt-font text-weight-bolder" label="Start Saving Big"
                       @click="signUp('group')"></q-btn>
              </div>
            </div>
            <div class="col-12 col-md-6 q-pa-md">
              <div class="__demo">
                <div class="font-3-4r">Provider ID: _C323fk131dkdf13j</div>
                <div class="font-1r text-weight-bold">Clifford OBGYN</div>
                <div class="q-py-sm font-3-4r text-weight-bold text-ir-grey-8">Open Bills</div>
                <div class="__table row justify-center">
                  <table class="alt-font">
                    <tr class="__hr text-right">
                      <td v-if="$q.screen.gt.sm">Transaction ID</td>
                      <td>Service Date</td>
                      <td>Billed</td>
                      <td>Cash Discount</td>
                      <td>Cash Total</td>
                      <td>Approval</td>
                    </tr>
                    <tr class="text-right">
                      <td v-if="$q.screen.gt.sm">
                        _12k13l...
                      </td>
                      <td>
                        {{ today(10) }}
                      </td>
                      <td>$835</td>
                      <td class="text-red">($412)</td>
                      <td>$423</td>
                      <td>
                        <q-checkbox :model-value="true"></q-checkbox>
                      </td>
                    </tr>
                    <tr class="text-right">
                      <td v-if="$q.screen.gt.sm">
                        _4ser13...
                      </td>
                      <td>
                        {{ today(3) }}
                      </td>
                      <td>$610</td>
                      <td class="text-red">($288)</td>
                      <td>$322</td>
                      <td>
                        <q-checkbox :model-value="true"></q-checkbox>
                      </td>
                    </tr>
                  </table>
                </div>

                <div class="row items-center">
                  <div class="col-12 col-md-6 q-pa-md">
                    <div class="row justify-center">
                      <div>
                        <div class="text-weight-bold font-1r text-right">
                          <div>Total Due: $745.00</div>
                          <div class="text-primary">- Provider Balance: $120.00</div>
                          <div>Pay in Full: $625</div>
                        </div>
                        <div class="q-py-md row justify-end">
                          <q-btn color="primary" glossy class="text-weight-bolder alt-font" label="Transfer Funds"
                                 icon-right="mdi-chevron-right"></q-btn>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-6 q-pa-md">
                    <div class="__vert">
                      <div class="__label font-1-2r">Vendor Card</div>
                      <div class="text-center text-weight-bold font-1-1-4r q-py-md">Clifford OBGYN</div>
                      <div class="q-pt-lg">
                        <div class="text-ir-grey-8 text-weight-bold">Available Balance</div>
                        <div class="text-weight-bolder alt-font font-1-1-2r">$120.00</div>
                      </div>
                      <div class="absolute-bottom row q-pa-lg">
                        <div class="font-3-4r">Auto Renew</div>
                        <q-space></q-space>
                        <div class="font-3-4r text-weight-bold">Never</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>

    </div>


    <div class="row justify-center pd12 bg-p9 text-white relative-position _oh">
      <div class="__blob3"></div>
      <div class="__blob4"></div>


      <div class="_cent z1">
        <div class="row items-center">

          <div class="col-12 col-md-6 q-pa-lg">
            <div class="font-1r text-weight-bolder alt-font">CommonPay Advantage For</div>
            <div class="text-xxl text-weight-bolder">Medical Providers</div>
            <div class="text-sm alt-font text-weight-medium">Insurance dehumanizes the interactions between doctor and patient. Discover
              the ease and joy of direct cash pay from groups - and take significantly more control over your process.
            </div>
            <div class="text-sm alt-font text-weight-bolder q-py-md text-p1">You can do better with CommonPay</div>
            <q-list>
              <q-item v-for="(b, i) in pBenefits" :key="`b-${i}`">
                <q-item-section avatar>
                  <q-checkbox :model-value="true"></q-checkbox>
                </q-item-section>
                <q-item-section>
                  <q-item-label class="text-weight-medium font-1-1-4r">{{ b }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>

            <div class="row q-px-md q-py-lg">
              <q-btn @click="signUp('provider')" color="white" text-color="p9" no-caps
                     class="alt-font text-weight-bolder" label="Cash Out Today"></q-btn>
            </div>
          </div>

          <div class="col-12 col-md-6 q-pa-md">
            <div class="__demo text-black">
              <div class="font-1r text-weight-bold">Welcome, Dr. Clifford!</div>
              <div class="q-py-sm _fw">
                <div class="row justify-center">

                  <div>
                    <div class="row">
                      <div class="col-12 col-md-8">
                        <div class="q-py-sm text-weight-bold alt-font">Let's get you paid</div>
                      </div>
                      <div class="col-12 col-md-4">
                        <div>
                          <div>
                            <q-icon name="mdi-bell"></q-icon>
                            = remind
                          </div>
                          <div>
                            <q-icon name="mdi-cash-check"></q-icon>
                            = pre-funded
                          </div>
                        </div>
                      </div>
                    </div>


                    <div class="_fw __table">
                      <table>
                        <tr class="__hr">
                          <td v-for="(k, i) in Object.keys(providerItems)" :key="`k-${i}`">
                            {{ k }}&nbsp;<q-icon name="mdi-menu-down"></q-icon>
                          </td>
                          <td>Action</td>
                        </tr>
                        <tr v-for="(i) in 3" :key="`row-${i}`" class="__dr">
                          <td v-for="(k, idx) in Object.keys(providerItems)" :key="`key-${i}-${idx}`">
                            {{ providerItems[k][i - 1] }}
                          </td>
                          <td class="font-1-1-4r" v-if="i === 2">
                            <q-icon name="mdi-cash-check"></q-icon>
                          </td>
                          <td class="font-1-1-4r" v-else>
                            <q-icon name="mdi-bell"></q-icon>
                          </td>
                        </tr>
                      </table>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>

        </div>
      </div>
    </div>

    <div class="row justify-center pd8 relative-position _oh">

      <div class="__blob5"></div>
      <div class="__blob6"></div>

      <div class="__se z1">
        <div class="text-weight-bold text-xl text-center">Pricing</div>

        <div class="row justify-center">
          <div class="q-pa-md">

            <div class="text-md text-weight-medium text-p6">Groups</div>
            <div class="q-pt-lg row justify-center">
              <table class="__ptable">
                <thead>
                <tr class="text-left alt-font">
                  <td>Participant Count</td>
                  <td>Per Person/Month</td>
                </tr>
                </thead>
                <tbody>
                <tr class="__altr">
                  <td>{{ '<50' }}</td>
                  <td>{{ dollarString(prods['commonPay'].pricing.amt(1), '$', 0) }}</td>

                </tr>
                <tr>
                  <td>50-100</td>
                  <td>{{ dollarString(prods['commonPay'].pricing.amt(50), '$', 0) }}</td>

                </tr>
                <tr class="__altr">
                  <td>100-500</td>
                  <td>{{ dollarString(prods['commonPay'].pricing.amt(100), '$', 0) }}</td>

                </tr>
                <tr>
                  <td>500+</td>
                  <td>{{ dollarString(prods['commonPay'].pricing.amt(501), '$', 0) }}</td>
                </tr>
                </tbody>

              </table>

            </div>
            <div class="text-center q-pb-lg">
              <div class="font-1r q-py-md _fw">*Fees for physical and replacement cards may apply</div>
            </div>

            <div class="text-md text-weight-medium alt-font text-p6">Providers</div>
            <div class="text-xs">Standard credit card processing rates apply for cards. Groups can also pay by bank
              transfer which can nearly eliminate processing fees.
            </div>

          </div>

        </div>

      </div>
    </div>

    <div id="CommonPaySignUp" class="row justify-center pd12">
      <div class="_sent q-pa-md">
        <div class="text-xxl text-weight-bold">Get me started</div>
        <div class="text-sm">Get that blood pumping with some cash payments</div>
        <contact-base :defs="{ visitorType }">
          <template v-slot:top="scope">
            <div class="q-pb-md _fw">
              <q-tabs align="left" :model-value="scope.form.visitorType"
                      @update:model-value="scope.modelForm('visitorType', $event), setType($event)">
                <q-tab name="group" label="I'm with a Group"></q-tab>
                <q-tab name="provider" label="I'm a Medical Provider"></q-tab>
                <q-tab name="other" label="I'm Somethin' Else"></q-tab>
              </q-tabs>

              <div class="q-py-sm _fw">
                <q-input
                    v-bind="{
                  inputClass: 'q-px-sm _fw',
                  filled: true,
                  placeholder: 'Company Name',
                  modelValue: scope.form.orgName
                }"
                    @update:model-value="scope.modelForm('orgName', $event)"
                ></q-input>
              </div>
            </div>
          </template>
        </contact-base>
      </div>
    </div>


  </q-page>
</template>

<script setup>
  import card1 from 'src/assets/card1.svg';
  import card2 from 'src/assets/card2.svg';
  import {computed, onMounted, ref} from 'vue';
  import {date, Screen} from 'quasar';
  import ContactBase from 'src/components/common/contact/ContactBase.vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {dollarString} from 'src/utils/global-methods';
  import {prods} from 'src/components/products';

  const today = (days = 0) => date.formatDate(date.subtractFromDate(new Date(), { days }), 'MM/DD/YY');

  const active = ref(false);
  const card = computed(() => {
    return active.value ? card1 : card2;
  })

  const ees = ref(50);
  const price = computed(() => prods.value['commonPay'].pricing.amt(ees.value));

  const benefits = ref(['Deep cash pay discounts', 'Direct relationships with providers', 'Network price benchmarking']);
  const pBenefits = ref(['Determine value based fees', 'Fully settle in days and hours', 'Build a private pay customer base']);

  const setCard = () => {
    setTimeout(() => {
      active.value = !active.value;
      setCard();
    }, 7000);
  }

  const providerItems = computed(() => {
    const obj = {
      'Group': ['Commoncare', 'Worktown', 'Phizbo'],
      'Code': ['57421', '58140', '99202'],
      'Balance': ['$212', '$943', '$150'],
    };
    if (Screen.gt.sm) obj['Patient'] = ['Eva Rose', 'Chloe Fae', 'Ellie Sofia'];
    obj['Due'] = [today(2), today(-2), today(-3)]
    return obj;
  })

  const visitorType = ref('group');
  const setType = (val) => {
    if (val) LocalStorage.setItem('visitorType', val);
  }

  const signUp = (type) => {
    if (type) {
      LocalStorage.setItem('visitorType', type);
      visitorType.value = type;
    }
    const el = document.getElementById('CommonPaySignUp');
    const top = el.offsetTop;
    window.scrollTo({
      top,
      behavior: 'smooth'
    })
  }

  onMounted(() => {
    setCard();
    const t = LocalStorage.getItem('visitorType');
    if (t) visitorType.value = t;
  })
</script>

<style lang="scss" scoped>

  .__demo {
    padding: 40px 30px;
    border-radius: 20px;
    box-shadow: 0 10px 12 -2px rgba(0, 0, 0, .7);
    background: white;
  }

  .__hr {
    text-align: right;

    td {
      padding: 2px 4px;
      border-radius: 2px;
      background: #efefef;
      font-weight: 500;
      text-align: right;
    }
  }

  .__dr {
    text-align: right;

    td {
      padding: 2px 4px;
      border-bottom: solid .5px black;
    }
  }

  .__table {
    width: 100%;
    overflow-x: scroll;
  }

  .__vert {
    position: relative;
    border-radius: 12px;
    width: 250px;
    max-width: 100%;
    height: 250px;
    max-height: 90vw;
    box-shadow: 0 0 3px rgba(0, 0, 0, .5);
    padding: 30px 20px;

    .__label {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translate(-50%, 0);
      background: black;
      border-radius: 0 0 5px 5px;
      padding: 1px 5px;
      color: white;
      font-weight: 700;
    }
  }

  .__se {
    width: 700px;
    max-width: 100%;
    border-radius: 20px;
    background: rgba(255,255,255,.8);
    padding: 40px 30px;
  }

  .__ptable {
    text-align: center;
    font-size: 1.4rem;
    font-weight: 700;

    td {
      padding: 5px 10px;
    }

    thead {

    }

    tbody {
      td {
        text-align: right;
        border-radius: 5px;
      }
    }

    .__altr {
      background: var(--q-p0);
    }
  }

  .__crd {
    background: transparent;
    max-width: 100%;
    width: 483px;
    height: 300px;
    max-height: 57vw;
    animation: rotate 20s infinite;
    border-radius: 20px;
    box-shadow: 0 25px 55px -10px rgba(0, 0, 0, .5) !important;
  }

  @keyframes rotate {
    0% {
      transform: none
    }
    25% {
      transform: rotateX(-10deg) rotateY(32deg) rotateZ(0deg)
    }
    37.5% {
      transform: rotateX(-12deg) rotateY(10deg) rotateZ(0deg)
    }
    50% {
      transform: rotateX(-20deg) rotateY(-25deg) rotateZ(0deg)
    }
    62.5% {
      transform: rotateX(5deg) rotateY(-5deg) rotateZ(0deg)
    }
    75% {
      transform: rotateX(25deg) rotateY(15deg) rotateZ(0deg)
    }
    87.5% {
      transform: rotateX(15deg) rotateY(5deg) rotateZ(0deg)
    }
    100% {
      transform: none
    }
  }

  .__blob5 {
    position: absolute;
    z-index: 0;
    width: 1600px;
    max-width: 110vw;
    height: 1150px;
    max-height: 85vw;
    border-radius: 50%;
    opacity: .6;
    background: radial-gradient(var(--q-p3) -30%, transparent 50%);
    animation: roam 20s infinite;
    top: -20%;
    left: -10%;
    transform: translate(5%, 0);
  }

  .__blob6 {
    position: absolute;
    z-index: 0;
    width: 1600px;
    max-width: 110vw;
    height: 1150px;
    max-height: 85vw;
    border-radius: 50%;
    opacity: .6;
    background: radial-gradient(var(--q-p3) -30%, transparent 50%);
    animation: roam 20s infinite;
    top: -10%;
    left: 50%;
    transform: translate(5%, 0);
  }
  .__blob3 {
    position: absolute;
    z-index: 0;
    width: 1500px;
    max-width: 130vw;
    height: 1150px;
    max-height: 90vw;
    border-radius: 50%;
    opacity: .6;
    background: radial-gradient(var(--q-primary) -30%, transparent 50%);
    animation: roam 20s infinite;
    bottom: -20%;
    left: -20%;
    transform: translate(5%, 0);
  }
  .__blob4 {
    position: absolute;
    z-index: 0;
    width: 1500px;
    max-width: 130vw;
    height: 1150px;
    max-height: 90vw;
    border-radius: 50%;
    opacity: .6;
    background: radial-gradient(var(--q-primary) -30%, transparent 50%);
    animation: roam 20s infinite;
    bottom: -20%;
    left: 60%;
    transform: translate(5%, 0);
  }
  .__blob {
    position: absolute;
    z-index: 0;
    width: 1900px;
    max-width: 180vw;
    height: 1650px;
    max-height: 120vw;
    border-radius: 50%;
    opacity: .6;
    background: radial-gradient(var(--q-primary) -30%, transparent 50%);
    animation: roam 20s infinite;
    bottom: -20%;
    left: -10%;
    transform: translate(5%, 0);
  }
  .__blob2 {
    position: absolute;
    z-index: 0;
    width: 900px;
    max-width: 90vw;
    height: 750px;
    max-height: 70vw;
    border-radius: 50%;
    opacity: .6;
    background: radial-gradient(var(--q-primary) -30%, transparent 50%);
    animation: roam 20s infinite;
    bottom: -20%;
    left: 60%;
    transform: translate(5%, 0);
  }

  @media screen and (max-width: 1023px) {
    .__demo {
      padding: 30px 20px;
    }
  }
</style>
