<template>
  <div class="_fw">
    <div class="_fw">
      <div class="__ul pw2 q-pt-lg">
        <upload-ui
            @update:display="display.push($event)"
            :div-attrs="{ class: 'pd15' }"
            :div-style="{ fontSize: 'var(--text-md)', fontWeight: 600, height: '100%', width: '100%' }"
            title="Drop or select bills 📄"
            text-color="white"
            :upload="upload"
            multiple
            bg="transparent"
            allow-types="image,pdf"
        >
          <div class="row justify-center">
            <div class="_cent pw2">
              <div class="row">
                <div class="col-12 col-md-7 pw2">
                  <div class="row">
                    <div class="text-xl tw-five text-p12">
                      <span class="text-secondary">Don't pay</span> that medical bill until you see what it <span
                        class="text-italic">should</span> cost
                    </div>
                  </div>
                  <q-separator class="q-my-md" color="p3"></q-separator>

                  <div class="row">
                    <div class="text-xs">Free, in a few seconds, no contact info required</div>
                  </div>

                </div>
                <div class="col-12 col-md-5 q-pa-md">
                  <div class="__drop_zone">
                    <div class="_fa flex flex-center">
                      <div>
                        <div class="row items-center q-pb-sm">
                          <q-icon size="40px" name="mdi-upload"></q-icon>
                          <div class="q-mx-sm text-sm tw-eight">Drop or upload files</div>
                        </div>
                        <div class="row justify-center items-center">
                          <div class="__lock">
                            <q-icon class="_i_i" name="mdi-lock"></q-icon>
                            Your data is encrypted in transit and at rest.
                            We do not keep or share these files
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </upload-ui>


      </div>
    </div>

    <div class="row justify-center">
      <div class="_xsent">
        <div v-if="display.length" class="q-pb-md text-sm">One bill at a time - can be multiple pages/documents, but
          we
          treat all pages as one bill.
        </div>
        <div v-for="(item, i) in rawFiles" :key="`item-${i}`" class="__raw">
          <div>
            <div class="__file">
              <file-type-handler
                  :div-attrs="{ style: 'height: 100%; width: 100%' }"
                  :imgStyle="{ height: '100%', width: '100%', objectFit: 'cover' }"
                  :url="display[i]"
                  :file="item.raw"
                  v-if="item.raw?.type"
              ></file-type-handler>
            </div>
          </div>
          <div class="font-1r tw-five">{{ item.raw?.name }}</div>
          <q-btn @click="removeFile(i)" dense flat icon="mdi-close" color="red"></q-btn>
        </div>

        <div class="row justify-center q-pt-md q-pb-xl" v-if="rawFiles.length">
          <q-btn @click="run" size="xl" push color="primary" glossy no-caps class="tw-six" label="Analyze My Bill"
                 icon-right="mdi-chevron-right"></q-btn>
        </div>
      </div>
    </div>

    <div class="row justify-center __strip">
      <div class="_cent pw2">
        <div class="row items-center">
          <div class="col-12 col-md-4">
            <div class="row justify-center">
              <div class="col-10 col-md-8">
                <div class="text-xs tw-six text-center">Your bill is AI analyzed and compared with our pricing data
                  from:
                </div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-8 q-py-sm">
            <div class="row q-py-sm __in">
              <div class="col-6 col-md-3">
                <div>12,000+</div>
                <div>Hospitals & Surgery Centers</div>
              </div>
              <div class="col-6 col-md-3">
                <div>26M</div>
                <div>Published cash prices</div>
              </div>
              <div class="col-6 col-md-3">
                <div>319</div>
                <div>Private Insurance Carriers</div>
              </div>
              <div class="col-6 col-md-3">
                <div>29</div>
                <div>CMS episode models</div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
    <div class="row justify-center pw2 relative-position ">
      <div class="__blb"></div>
      <div class="__blb2"></div>
      <div class="__blb3"></div>
      <div class="_cent pd8 pw2 z2 relative-position">

        <div class="_fw q-py-sm z2">
          <div class="__stats">
            <div>
              <div class="__1">3/4</div>
              <div>bills we can reduce</div>
            </div>
            <div>
              <div class="__2">38%</div>
              <div>average savings</div>
            </div>
            <!--            <div>-->
            <!--              <div class="__3">$0</div>-->
            <!--              <div>cost to find out</div>-->
            <!--            </div>-->
            <!--            <div>-->
            <!--              <div class="__4">16</div>-->
            <!--              <div>seconds to get a result</div>-->
            <!--            </div>-->
            <!--            <div>-->
            <!--              <div class="__5">6</div>-->
            <!--              <div>days avg resolution</div>-->
            <!--            </div>-->
            <div v-if="$q.screen.gt.sm">
              <div class="__6">$0</div>
              <div>Payment required</div>
            </div>
          </div>


        </div>


        <div class="pd8 _fw">
          <div class="text-center text-lg tw-six">We reprice your bill in seconds - then you can:</div>

          <div class="row justify-center q-py-md">
            <div class="_xsent">
              <div class="row justify-center">
                <div class="__or">
                  <div>
                    <div>
                      <div class="__dot"></div>
                    </div>
                    <div>Use the data to go negotiate the savings IRL - or</div>
                  </div>
                  <div>
                    <div>
                      <div class="__dot"></div>
                    </div>
                    <div>Let our team of pros handle it - you only pay when you save</div>

                  </div>
                </div>
              </div>

            </div>
          </div>

        </div>

        <div class="row q-py-lg items-center relative-position">
          <div class="__fblob"></div>
          <div class="__fblob2"></div>
          <div class="col-12 col-md-6 pw2 relative-position z2">
            <div class="row justify-center">
              <div class="text-sm">

                <div class="__fees">
                  <div class="tw-five text-xs">
                    <span class="tw-six text-md">Our fees</span>&nbsp;(if you decide to have us do the work):
                  </div>
                  <div class="_fw q-py-md">
                    <div class="text-xs tw-five">The lesser of:</div>
                    <div class="text-sm alt-font tw-five">25% of the savings</div>
                    <div class="text-sm alt-font tw-five">10% of the savings</div>
                  </div>
                  <div class="text-sm tw-six __lesser">No savings? No fee.</div>
                  <div class="text-xs q-pt-sm">See some examples</div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6 pw2 relative-position z2">
            <div class="row justify-center">
              <div class="__pt">

                <pricing-table color="primary"></pricing-table>

              </div>
            </div>
          </div>
        </div>


      </div>
    </div>


    <div class="q-py-xl"></div>

    <q-dialog maximized v-model="loading">
      <div class="_fa flex flex-center __d">
        <div class="w300 mw100 q-pa-lg">
          <div class="row justify-center">
            <ai-logo opaque dark size="50px"></ai-logo>
          </div>
          <div class="text-center text-xs text-white q-py-sm tw-six">
            {{ stage }}
          </div>
          <div class="row justify-center q-py-sm">
            <div class="__progress">
              <div :style="`width: ${progress}%`"></div>
            </div>
          </div>
        </div>
      </div>
    </q-dialog>

    <turnstile-popup v-if="!isAuthenticated && !notabot" v-model:interactive="showTurnstile" v-model="notabot"></turnstile-popup>
  </div>
</template>

<script setup>
  import UploadUi from 'components/common/uploads/components/UploadUi.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import PricingTable from 'pages/landing/bill-eraser/PricingTable.vue';
  import TurnstilePopup from 'src/utils/turnstile/TurnstilePopup.vue';

  import {ref} from 'vue';
  import {axiosFeathers, restCore} from 'components/common/uploads/services/utils';
  import FileTypeHandler from 'components/common/uploads/file-types/fileTypeHandler.vue';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {useRouter} from 'vue-router';
  import {storeEraserFiles} from 'components/bill-collective/utils/eraser-db';
  import {loginPerson} from 'stores/utils/login';
  import {$infoNotify} from 'src/utils/global-methods';
  const { isAuthenticated } = loginPerson()
  const notabot = ref(false);
  const showTurnstile = ref(true);

  const router = useRouter();
  const display = ref([]);
  const rawFiles = ref([]);
  const removeFile = (i) => {
    rawFiles.value.splice(i, 1);
    display.value.splice(i, 1);
  }
  const upload = async (files) => {
    if (Array.isArray(files)) {
      const names = rawFiles.value.map(a => a.raw.name || a.raw.size);
      for (let i = 0; i < files.length; i++) {
        if (!names.includes(files[i].raw.name || files[i].raw.size)) rawFiles.value.push(files[i]);
      }
    } else rawFiles.value.push(files);
  }
  const loading = ref(false);
  const progress = ref(0);
  const stages = ['Uploading Bill', 'Reading Lines', 'Searching Prices', 'Comparing Bundles', 'Expanding Range', 'Removing Outliers', 'Scrubbing Results']
  const stage = ref(stages[0]);
  const setStage = () => {
    const idx = Math.min(stages.length - 1, Math.floor(progress.value / 10))
    stage.value = stages[idx];
  }
  const startProgress = () => {
    setTimeout(() => {
      if (progress.value < 101) {
        progress.value++;
        setStage();
        startProgress();
      }
    }, rawFiles.value.length * 180);
  }
  const run = async () => {
    if(!notabot.value && !isAuthenticated.value) return $infoNotify('Verify you are human')
    const planId = LocalStorage.getItem('plan_id');
    const ref = LocalStorage.getItem('ref_id');
    const payload = new FormData();
    for (let i = 0; i < rawFiles.value.length; i++) {
      if (rawFiles.value[i].raw) payload.append('files', rawFiles.value[i].raw);
    }
    loading.value = true;
    startProgress()
    const be = await axiosFeathers().post('/bill-erasers', payload, {
      params: {
        core: restCore(),
        runJoin: { bill_eraser: { planId, ref } }
      }
    })
        .catch(err => console.log(`Error getting estimates: ${err.message}`))

    loading.value = false;
    progress.value = 0;
    if (be.data) {
      LocalStorage.setItem('bill_eraser_session', be.data.session._id);
      const toDataUrl = (file) => {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.onerror = () => reject(reader.error);
          reader.readAsDataURL(file);
        });
      }
      const arr = [];
      const fileKeys = ['lastModified', 'lastModifiedDate', 'name', 'size', 'type']
      for(let i = 0; i < rawFiles.value.length; i++) {
        const url = await toDataUrl(rawFiles.value[i].raw);
        const meta = { originalname: rawFiles.value[i].originalname || rawFiles.value[i].name };
        for(const k of fileKeys) {
          meta[k] = rawFiles.value[i][k];
        }
        arr.push({ url, meta })
      }
      storeEraserFiles(arr.slice(0, 10))
      SessionStorage.setItem(`bill_eraser:${be.data.session._id}`, new Date().getTime());
      router.push({ name: 'bill-eraser-session', params: { session:be.data.session._id } })
    }
  }

</script>

<style lang="scss" scoped>

  .__ul {
    height: 100%;
    width: 100%;
    //background: var(--q-p2);
    background: linear-gradient(180deg, var(--q-p0) -20%, white 90%);
    transform: none;
    transition: all .3s ease;
    cursor: pointer !important;
    color: var(--q-p12);


    .__lock {
      font-weight: 500;
      transition: all .3s ease;
      padding: 0 5px;
      font-size: var(--text-xxs);
    }

    .__drop_zone {
      margin: 15px 0;
      width: 100%;
      border-radius: min(20px, 4vw);
      color: var(--q-primary);
      transition: all .3s ease;
      overflow: hidden;
      background-size: 200%;
      background-position: center;
      background-image: linear-gradient(135deg, var(--q-s2), var(--q-p2), var(--q-a2));

      > div {
        background: rgba(255, 255, 255, .7);
        padding: max(30px, 3vw);
        border-radius: inherit;
        transition: all .3s ease;
      }
    }

    &:hover {
      //background: linear-gradient(180deg, var(--q-primary), var(--q-p0));
      .__drop_zone {
        color: var(--q-p6);
        background-position: left;

        > div {
          background: rgba(255, 255, 255, .8);

        }
      }

    }

    @media screen and (max-width: 1023px) {
      .__drop_zone {
        color: var(--q-primary);
        background-position: left;

        > div {
          background: rgba(255, 255, 255, .8);

        }
      }
    }
  }

  .__strip {
    color: rgba(0, 0, 0, .6);
    padding: 40px 0;
    background: linear-gradient(95deg, var(--q-p0) 40%, var(--q-a0), var(--q-s0) 80%);

    .__in {
      > div {
        padding: 10px;

        > div {
          font-weight: 600;
          font-size: var(--text-xxs);
          text-align: center;

          &:first-child {
            font-size: var(--text-md);

          }
        }
      }
    }
  }


  .__raw {
    width: 100%;
    display: grid;
    align-items: center;
    grid-template-columns: 110px 1fr auto;
    padding: 20px;
    border-radius: 10px;
    background: white;
    margin: 10px 0;

    > div {
      padding: 0 5px;
    }
  }


  .__file {
    height: 100px;
    width: 80px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 1px 1px 4px #c5c5c5;
  }


  .__stats {
    padding: 50px 0;
    width: 100%;
    display: grid;
    grid-template-columns: 33.3% 33.3% 33.3%;
    grid-template-rows: auto auto;
    align-items: center;
    justify-items: center;
    justify-content: center;
    text-align: center;
    grid-column-gap: 10px;
    grid-row-gap: 10px;

    > div {
      background: white;
      border-radius: 10px;
      width: 100%;
      height: 100%;
      padding: min(5vh, 7vw) 20px;
      //display: flex;
      //align-items: center;
      //justify-content: center;

      div {
        //padding: 10px;
        &:first-child {
          font-size: var(--text-xxl);
          color: var(--q-p1);
          font-weight: 600;
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          font-family: var(--alt-font);
        }

        &:nth-child(2) {
          font-size: var(--text-sm);
          font-weight: 500;
        }
      }
    }

    .__1 {
      background: linear-gradient(135deg, var(--q-p3) 40%, var(--q-a3));
    }

    .__2 {
      background: linear-gradient(135deg, var(--q-p3), var(--q-a3));
    }

    .__3 {
      background: linear-gradient(135deg, var(--q-p3), var(--q-a3) 50%);
    }

    .__4 {
      background: linear-gradient(135deg, var(--q-s3) 50%, var(--q-p3), var(--q-a3));
    }

    .__5 {
      background: linear-gradient(135deg, var(--q-p3), var(--q-a3));
    }

    .__6 {
      background: linear-gradient(135deg, var(--q-p3), var(--q-a3) 50%);
    }
  }

  @media screen and (max-width: 800px) {
    .__stats {
      grid-template-columns: 50fr 50fr;
    }
  }

  .__process {
    width: 100%;
    //border-radius: 12px;
    //box-shadow: 2px 2px 12px #c5c5c5;
    padding: 60px 0;
    //margin-top: 30px;
  }

  .__blb {
    pointer-events: none;
    z-index: 0;
    position: absolute;
    top: -10%;
    left: 0;
    width: min(800px, 90vw);
    height: min(600px, 70vw);
    border-radius: 50%;
    opacity: .5;
    background: radial-gradient(var(--q-p2) 5%, transparent 50%);
  }

  .__blb2 {
    pointer-events: none;
    z-index: 0;
    position: absolute;
    top: 10%;
    left: -10%;
    width: min(900px, 100vw);
    height: min(800px, 80vw);
    border-radius: 50%;
    opacity: .6;
    background: radial-gradient(var(--q-s2) 5%, transparent 50%);
  }

  .__blb3 {
    pointer-events: none;
    z-index: 0;
    position: absolute;
    top: -30%;
    right: -10%;
    width: min(1100px, 120vw);
    height: min(1500px, 150vw);
    border-radius: 50%;
    opacity: .4;
    background: radial-gradient(var(--q-a2) 5%, transparent 50%);
  }

  .__progress {
    width: 100%;
    max-width: 300px;
    border-radius: 50px;
    height: 10px;
    border: solid 2px white;

    div {
      height: 100%;
      transition: all .2s;
      background: white;
    }
  }

  .__d {
    backdrop-filter: blur(10px);
  }

  .__pt {
    width: 800px;
    max-width: 100%;
  }

  .__dot {
    height: 15px;
    width: 15px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--q-p3) 30%, var(--q-a3), var(--q-s3) 80%);
  }

  .__or {
    font-size: var(--text-sm);
    font-weight: 500;

    > div {
      border-radius: 9px;
      //background: white;
      margin: 10px 0;
      display: grid;
      grid-template-columns: auto 1fr;
      align-items: center;

      > div {
        padding: 5px 13px;
      }
    }
  }

  .__fees {
    padding: 50px 20px;
    background: linear-gradient(180deg, white 50%, transparent);
    border-radius: 10px;
  }

  .__lesser {
    font-weight: 900;
    background: linear-gradient(90deg, var(--q-primary), var(--q-accent), var(--q-secondary));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }


  .__fblob {
    z-index: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: min(900px, 100vw);
    height: min(800px, 80vw);
    border-radius: 50%;
    opacity: .4;
    background: radial-gradient(var(--q-p2) 5%, transparent 50%);
  }

</style>
