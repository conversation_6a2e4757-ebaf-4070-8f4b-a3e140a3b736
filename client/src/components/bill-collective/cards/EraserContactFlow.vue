<template>
  <div class="_fw">
    <div class="text-xs tw-six">Est Savings: <span
        class="text-sm text-primary">{{ dollarString(savings / 100, '$', 0) }}</span></div>
    <div class="text-xs">This estimate provides automated information about your bill vs the pricing we see for the same procedures - to
      realize these savings, negotiations have to be made with your provider. We can do that for you.
    </div>

    <div class="q-py-md">
      <div class="font-1r tw-six q-py-md">Next Steps</div>

      <div class="__steps">
        <div class="__line"></div>
        <div class="__step" v-for="(step, i) in steps" :key="`step-${i}`">
          <div class="__bar">
            <div class="__avatar_wrap cursor-pointer" @click="toggleActive(i)">
              <div :class="`__avatar ${active === i || done[i] ? '__on' : ''}`">
                <q-icon color="white" size="18px" :name="step.icon"></q-icon>
              </div>
            </div>
          </div>
          <div class="__content">
            <div class="__head" @click="toggleActive(i)">
              <div :class="`__sub ${active === i ? '__subon': ''}`">{{ step.subtitle }}</div>
              <div class="__title">{{ step.title }}</div>
            </div>
            <div :class="`__body ${active === i ? '' : '__off'}`">
              <component :is="step.component" v-bind="step.attrs"></component>
            </div>
          </div>
        </div>
      </div>

    </div>

    <div class="q-py-md">
      <div class="font-1r tw-six">Why we do this best</div>
      <div class="_fw row items-center">
        <div class="__rem" v-for="(h, i) in highlights" :key="`h-${i}`">
          <div>
            <div>
              <q-icon class="text-xl __gr" :name="h[0]"></q-icon>
            </div>
            <div class="text-xxs tw-five">
              {{ h[1] }}
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
  import ReviewTerms from 'components/bill-collective/cards/ReviewTerms.vue';
  import LimitedHipaa from 'components/bill-collective/utils/LimitedHipaa.vue';
  import ProfileSection from 'components/bill-collective/cards/ProfileSection.vue';

  import {dollarString} from 'src/utils/global-methods';
  import {computed, ref} from 'vue';

  const props = defineProps({
    savings: Number,
    total: Number,
    session: String
  })

  const active = ref(-1);
  const done = ref({ 0: false, 1: false, 2: false })

  const toggleActive = (i) => {
    if(active.value === i) active.value = -1;
    else active.value = i;
  }

  const steps = computed(() => [
    {
      icon: 'mdi-file-document',
      subtitle: 'Review our terms',
      title: 'Fair, simple, and transparent',
      component: ReviewTerms,
      attrs: {
        savings: props.savings,
        total: props.total,
      }
    },
    {
      icon: 'mdi-signature',
      subtitle: 'Sign Documents',
      title: 'So we can work on your behalf',
      component: LimitedHipaa,
      attrs: {
        session: props.session
      }
    },
    {
      icon: 'mdi-message',
      subtitle: 'Complete your profile',
      title: 'So we communicate how you like',
      component: ProfileSection,
      attrs: {
        session: props.session
      }
    }
  ])

  const highlights = [
    ['mdi-license', 'Our physician-led and legal-backed team has the tools to understand the system and push in the right spots'],
    ['mdi-brain', 'We identify overlap and waste - which is common due to the complexity of medical billing.'],
    ['mdi-hospital-building', 'We have relationships with thousands of providers and know their process for negotiation and financial aid'],
    ['mdi-target', 'We use surgical precision in erasing extra cost - without undue pressure on doctors, who are ~20% of the cost.']
  ]
</script>

<style lang="scss" scoped>

  .__rem {
    height: 100%;
    width: 25%;
    padding: 5px;

    > div {
      display: grid;
      grid-template-columns: 100%;
      grid-template-rows: auto auto;
      border-radius: 10px;
      background: linear-gradient(135deg, var(--q-p0), var(--q-a0), var(--q-s0));
      padding: 25px;
      height: 100%;

      > div {
        &:first-child {
          display: flex;
          padding-bottom: 6px;
        }
      }
    }
  }

  @media screen and (max-width: 1023px) {
    .__rem {
      width: 100%;
    }
  }

  .__gr {
    background: linear-gradient(135deg, var(--q-primary) 30%, var(--q-accent), var(--q-secondary));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .__steps {
    position: relative;
    width: 100%;

    .__line {
      position: absolute;
      top: 10px;
      left: 18.5px;
      width: 3px;
      bottom: 10px;
      background: linear-gradient(180deg, var(--q-p2), var(--q-a2), var(--q-s2));
      z-index: 1;
    }

    .__step {
      width: 100%;
      display: grid;
      grid-template-columns: 40px auto;
      grid-template-rows: auto auto auto;

      .__bar {
        .__avatar_wrap {
          display: grid;
          grid-template-columns: 100%;
          justify-content: center;
          justify-items: center;
          padding: 3px 0;
        }

        .__avatar {
          transition: all .3s;
          height: 28px;
          width: 28px;
          border-radius: 50%;
          box-shadow: 0 0 0 3px white;
          background: linear-gradient(135deg, var(--q-p2), var(--q-a2), var(--q-s2));
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .__on {
          background: linear-gradient(135deg, var(--q-primary), var(--q-accent), var(--q-secondary));
          height: 30px;
          width: 30px;
        }
      }

      .__content {
        width: 100%;
        overflow-x: scroll;
        padding: 3px 5px;
        display: grid;
        grid-template-columns: 100%;
        grid-template-rows: auto auto;

        .__head {
          cursor: pointer;
          border-radius: 10px;
          background: transparent;
          transition: all .3s;
          padding: 5px 10px;

          &:hover {
            background: #eee;
          }

          .__sub {
            font-size: .8rem;
            font-weight: 600;
            color: #999;
            text-transform: uppercase;
          }

          .__subon {
            font-weight: 900;
            background: linear-gradient(90deg, var(--q-primary), var(--q-accent), var(--q-secondary));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          .__title {
            font-size: 1rem;
            font-weight: 500;
          }

        }

        .__body {
          transition: all .3s;
          padding: 20px 0;
          height: 100%;
          width: 100%;
          overflow-y: scroll;
        }

        .__off {
          padding: 0;
          max-height: 0;
          overflow: hidden;
        }
      }

      &:last-child {
        .__bar {
          background: white;
        }
      }

    }

    > div {
      position: relative;
      z-index: 2;
    }
  }

</style>
