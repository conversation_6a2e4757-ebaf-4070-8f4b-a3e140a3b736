<template>
  <div class="_fw">
    <div class="q-pa-sm tw-six font-7-8r">Hospital Price Files</div>
    <div v-if="err" class="q-pa-md text-italic text-s7 font-1r">{{ err }}</div>
    <div class="q-py-sm font-1-1-4r tw-five alt-font flex items-center">
      Hospital county/area
      <q-chip color="ir-grey-2" clickable>
        <span>{{ county || 'Select County' }}</span>
        <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>
        <q-menu>
          <div class="w300 mw100 q-pa-sm bg-white">
            <q-list separator>
              <q-item clickable
                      v-for="(c, i) in Object.keys(jd?.data?.counties || {}).sort((a, b) => a.localeCompare(b))"
                      :key="`co-${i}`" @click="setCounty(c)">
                <q-item-section>
                  <q-item-label>{{ c }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-menu>
      </q-chip>
      <q-chip v-if="county && msas?.length > 1" color="ir-grey-2" clickable>
        <span>{{ msa || 'Select Area' }}</span>
        <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>
        <q-menu>
          <div class="w300 mw100 q-pa-sm bg-white">
            <q-list separator>
              <q-item v-for="(m, i) in msas" :key="`m-${i}`" clickable @click="setMsa(m)">
                <q-item-section>
                  <q-item-label>{{ m }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-menu>
      </q-chip>
    </div>
    <pt-map
        :hospitals="hospitals"
    ></pt-map>

    <div class="row justify-end q-pa-md">
      <q-btn dense color="primary" glossy push no-caps class="tw-six" label="Load Hospital Pricing" @click="searchHospitals()"></q-btn>
    </div>
  </div>
</template>

<script setup>

  import PtMap from 'components/bill-collective/cards/PtMap.vue';
  import {computed, ref, watch} from 'vue';
  import {getStateName} from 'components/common/geo/data/states';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import {usePriceEstimates} from 'stores/price-estimates';


  const priceEstimateStore = usePriceEstimates();

  const props = defineProps({
    state: String,
    city: String,
    codes: Array
  })

  const ptf = ref({ total: 0, data: [] })
  const ptProcedure = ref(undefined);
  const junkStore = useJunkDrawers();

  const hospitals = computed(() => (ptf.value.data || [])[0]?.hospitals || []);
  const msa = ref(undefined);
  const err = ref('');
  const msas = ref([]);
  const dirty = ref(false);
  const loading = ref(false);
  const jd = ref(undefined);
  const county = ref(undefined);

  const setCounty = (val) => {
    county.value = val;
    const msaList = jd.value?.data?.msas[val];
    if (!msaList) {
      msas.value = [];
      err.value = `No hospitals found in ${val}`
    } else {
      setMsa(msaList[0]);
      msas.value = msaList;
    }
  }
  const setJd = async () => {
    const jData = await junkStore.find({ query: { $limit: 1, itemId: `states|${props.state.toLowerCase()}` } })
    const sd = jData.data[0]
    if (sd) {
      jd.value = sd;
      if (props.city) {
        const obj = sd.data.cities[props.city];
        if (obj?.county) setCounty(obj.county);
      }
    }
  }
  const searchHospitals = async () => {
    err.value = '';
    if (props.codes?.length && props.state && msa.value) {
      loading.value = true;
      ptf.value = await priceEstimateStore.find({
        query: {},
        runJoin: {
          'pt_search': {
            configs: [{
              code: ptProcedure.value?.code || props.codes[0],
              state: getStateName(props.state),
              msa: msa.value
            }]
          }
        }
      })
          .catch(err => {
            console.error(`Error getting price files: ${err.message}`);
          })
      loading.value = false;
    } else {
      if (!jd.value) setJd()
      err.value = 'Select a valid state, hospital region, and procedure'
    }
  }
  const setMsa = (val) => {
    msa.value = val;
    dirty.value = true;
  }

  watch(() => props.state, (nv, ov) => {
    if (nv && nv !== ov) {
      setJd()
    }
  }, { immediate: true });
</script>

<style lang="scss" scoped>

</style>
