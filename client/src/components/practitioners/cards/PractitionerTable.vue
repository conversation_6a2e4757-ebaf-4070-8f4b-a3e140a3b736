<template>
  <table class="_fw">
    <thead>
    <tr>
      <td v-if="!noCode">NPI</td>
      <td>Name</td>
      <td>
        <slot name="head" :data="useList"></slot>
      </td>
    </tr>
    </thead>
    <tbody>
    <tr v-for="(item, i) in useList || []" :key="`item-${i}`">
      <td v-if="!noCode">{{ item.npi || 'N/A' }}</td>
      <td>{{ item?.name_prefix || '' }} {{ item.firstName || '' }} {{ item.lastName || '' }} <span
          class="font-3-4r">{{ item?.credential || '' }}</span>
      </td>
      <td>
        <slot name="side" :item="item" :index="i"></slot>
      </td>
    </tr>
    </tbody>
  </table>
</template>

<script setup>
  import {computed} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {usePractitioners} from 'stores/practitioners';

  const store = usePractitioners();
  const props = defineProps({
    modelValue: { required: true },
    noCode: Boolean,
    removable: Boolean
  })

  const idList = computed(() => {
    if (Array.isArray(props.modelValue)) return props.modelValue;
    else return Object.keys(props.modelValue || {});
  })

  const pause = computed(() => !idList.value?.length || typeof idList.value[0] !== 'string');
  const { h$: p$ } = HFind({
    store,
    pause,
    limit: computed(() => idList.value?.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: idList.value }
        }
      }
    })
  })

  const useList = computed(() => {
    if (pause.value) return idList.value;
    else return p$.data
  })
</script>

<style lang="scss" scoped>
  table {
    margin: 10px 0;
    border-collapse: collapse;

    thead {
      td {
        padding: 3px 10px;
        font-size: .75rem;
        font-weight: 600;
        color: #999;
        border-bottom: solid .2px #999;
      }
    }

    tbody {
      td {
        border-bottom: solid .2px #999;
        padding: 5px 10px;
        font-weight: 500;
      }

      tr:last-child {
        td {
          border-bottom: none;
        }
      }
    }
  }
</style>
