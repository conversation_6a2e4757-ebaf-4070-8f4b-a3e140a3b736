<template>
  <q-page>
    <div class="row justify-center pd8 __pp">
      <div class="_cent">
        <div class="row">
          <div class="col-12 col-md-6 q-pt-xl q-px-lg bg-white">
            <product-details :product="product"></product-details>
<!--            <div class="pd5"  v-if="$q.screen.gt.sm">-->
<!--              <div class="font-2r text-weight-bold q-pa-md">More questions? We'll help you tailor this plan to your-->
<!--                needs.-->
<!--              </div>-->
<!--              <contact-base></contact-base>-->
<!--            </div>-->
          </div>
          <div class="col-12 col-md-6 __wrap">
            <div class="__form">
              <plan-generator
                  :product="product"
                  :model-value="form"
                  @update:model-value="setForm($event, product.type !== 'product')"

              ></plan-generator>
            </div>

<!--            <div v-if="$q.screen.lt.md" class="q-px-lg bg-white">-->
<!--              <div class="pd5">-->
<!--                <div class="font-2r text-weight-bold q-pa-md">More questions? We'll help you tailor this plan to your-->
<!--                  needs.-->
<!--                </div>-->
<!--                <contact-base></contact-base>-->
<!--              </div>-->
<!--            </div>-->
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import PlanGenerator from 'src/components/utils/PlanGenerator.vue';
  import ProductDetails from 'src/components/utils/ProductDetails.vue';

  import {getByKey, defaultForm} from './index';
  import {computed, watch, ref, onMounted} from 'vue';
  import {useRoute} from 'vue-router';
  import {LocalStorage} from 'symbol-auth-client';

  const route = useRoute();
  const routeName = ref('');


  const product = computed(() => {
    const n = routeName.value;
    return getByKey(n);
  })


  const form = ref(defaultForm());

  const routeParams = computed(() => {
    return route.params.name;
  });

  const setForm = (val, hold) => {
    // if(!hold) LocalStorage.setItem('docRequest', defaultForm(val));
    form.value = defaultForm(val);
  }

  watch(routeParams, (nv) => {
    routeName.value = nv;
  }, { immediate: true })

  onMounted(() => {
    setTimeout(() => {
      const pd = LocalStorage.getItem('docRequest');
      if (pd) form.value = defaultForm(pd);
    }, 50)
  })
</script>

<style lang="scss" scoped>
  .__pp {
    min-height: 95vh;
    background: linear-gradient(90deg, white 50%, var(--q-p1) 50%);
  }

  .__wrap {
    padding: 2vw;
  }


  .__form {
    width: 100%;
    border-radius: 20px;
    //border: solid 1px var(--q-primary);
    padding: 60px 2vw 20px 2vw;
    background: white;
  }

  @media screen and (max-width: 1023px) {
    .__pp {
      background: linear-gradient(180deg, white 50%, var(--q-p1) 50%);;
    }
  }
</style>
