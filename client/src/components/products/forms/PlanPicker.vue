<template>
  <div>
    <q-item
        v-bind="{
          dense: true,
          clickable: true,
          ...itemAttrs
        }"
    >
      <q-item-section avatar v-if="!multiple">
      <slot name="avatar">
        <q-avatar size="20px" :color="product.color"></q-avatar>
      </slot>
      </q-item-section>
      <q-item-section>
        <q-item-label>
          <span v-if="!multiple && !!modelValue">&nbsp;{{ product.shortTitle }}</span>
          <span v-else class="text-weight-bold text-italic">&nbsp;Select Plan{{multiple ? 's' : ''}}</span>
        </q-item-label>
      </q-item-section>
      <q-item-section side>
        <q-icon name="mdi-menu-down"></q-icon>
      </q-item-section>


      <q-menu>
        <q-card class="__menu q-pa-md">
          <div class="font-3-4r text-weight-bold">Choose Plan</div>
<!--          <q-input v-model="searchInput" class="q-my-sm">-->
<!--            <template v-slot:prepend>-->
<!--              <q-icon name="mdi-magnify"></q-icon>-->
<!--            </template>-->
<!--          </q-input>-->
          <q-list separator dense>
            <q-item v-for="(prod, i) in keys" :key="`prod-${i}`" clickable @click="select(prod)">
              <q-item-section avatar>
                <q-avatar size="20px" :color="prods[prod].color"></q-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label class="alt-font text-weight-bold">{{ prods[prod].title }}</q-item-label>
                <q-item-label caption>{{ prods[prod].subtitle }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-icon color="positive" name="mdi-check" v-if="isSelected(prod)"></q-icon>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card>
      </q-menu>
    </q-item>

    <q-list dense separator class="q-py-sm" v-if="multiple && modelValue?.length">
      <q-item v-for="(item, i) in modelValue" :key="`item-${i}`">
        <q-item-section avatar>
          <q-avatar size="20px" :color="prods[item].color"></q-avatar>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{prods[item].title}}</q-item-label>
          <q-item-label caption>{{prods[item].subtitle}}</q-item-label>
        </q-item-section>
        <q-item-section side>
          <q-btn dense flat icon="mdi-close" color="red" @click="select(item)"></q-btn>
        </q-item-section>
      </q-item>
    </q-list>
  </div>
</template>

<script setup>
  import {prods} from '../index';
  import {computed, ref} from 'vue'
  import {dollarString} from 'src/utils/global-methods';

  const props = defineProps({
    modelValue: [String, Array],
    multiple: Boolean,
    itemAttrs: Object,
    removable: Boolean,
    showPrice: { type: Boolean, default: true },
    fteCount: { type: Number },
    type: String
  })
  const emit = defineEmits(['update:model-value'])

  const searchInput = ref('');

  const keys = computed(() => {
    const keys = Object.keys(prods.value);
    if(!props.type) return keys;
    else return keys.filter(a => prods.value[a].type === props.type);
  })

  const product = computed(() => prods.value[props.modelValue] || {});

  const isSelected = (key) => {
    if (Array.isArray(props.modelValue)) return (props.modelValue || []).includes(key);
    else return props.modelValue === key;
  }
  const select = (key) => {
    if (!props.multiple) emit('update:model-value', key);
    else {
      const idx = (props.modelValue || []).indexOf(key);
      console.log('idx?', idx);
      if (idx > -1){
        const arr = JSON.parse(JSON.stringify(props.modelValue));
        arr.splice(idx, 1);
        emit('update:model-value', arr);
      }
      else emit('update:model-value', [...(props.modelValue || []), key])
    }
  }

</script>

<style lang="scss" scoped>
  .__menu {
    width: 300px;
    max-width: 90vw;
  }
</style>
