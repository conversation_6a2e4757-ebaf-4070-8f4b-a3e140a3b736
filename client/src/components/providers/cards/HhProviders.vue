<template>
  <div class="q-py-sm">
    <div class="font-7-8r tw-five text-grey-6 q-py-md">Doctors love our process 💕. We help them work directly
      with patients and groups, facilitate fast cash payments, and allow them to benefit from outcomes instead
      of
      transactions
    </div>
    <care-provider
        v-model:providers="form.providers"
        v-model:practitioners="form.practitioners"
        @update:practitioners="autoSave('practitioners')"
        @update:providers="autoSave('providers')"
    ></care-provider>
  </div>
</template>

<script setup>
import CareProvider from 'components/care/cards/CareProvider.vue';

import {idGet} from 'src/utils/id-get';
import {computed} from 'vue';
import {useHouseholds} from 'stores/households';
import {usePpls} from 'stores/ppls';

const hhStore = useHouseholds();
const pplStore = usePpls();

const props = defineProps({
  person: { required: false },
  household: { required: false }
})

const { item:fullPerson } = idGet({
  store: pplStore,
  value: computed(() => props.person)
})

const { item:hh } = idGet({
  store: hhStore,
  value: computed(() => props.household || fullPerson.value?.household)
})
</script>

<style lang="scss" scoped>

</style>
