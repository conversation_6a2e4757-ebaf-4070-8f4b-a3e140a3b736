export const npiClasses = [
    'Chiropractor',
    'Dentist',
    'Denturist',
    'Dental Hygienist',
    'Dental Therapist',
    'Advanced Practice Dental Therapist',
    'Oral Medicinist',
    'Dental Assistant',
    'Dental Laboratory Technician',
    'Optometrist',
    'Technician/Technologist',
    'Independent Medical Examiner',
    'Integrative Medicine',
    'Phlebology',
    'Neuromusculoskeletal Medicine, Sports Medicine',
    'Neuromusculoskeletal Medicine & OMM',
    'Oral & Maxillofacial Surgery',
    'Transplant Surgery',
    'Electrodiagnostic Medicine',
    'Allergy & Immunology',
    'Anesthesiology',
    'Dermatology',
    'Emergency Medicine',
    'Family Medicine',
    'Internal Medicine',
    'Medical Genetics',
    'Neurological Surgery',
    'Nuclear Medicine',
    'Obstetrics & Gynecology',
    'Ophthalmology',
    'Orthopaedic Surgery',
    'Otolaryngology',
    'Pathology',
    'Pediatrics',
    'Physical Medicine & Rehabilitation',
    'Plastic Surgery',
    'Preventive Medicine',
    'Psychiatry & Neurology',
    'Radiology',
    'Surgery',
    'Urology',
    'Colon & Rectal Surgery',
    'General Practice',
    'Thoracic Surgery (Cardiothoracic Vascular Surgery)',
    'Hospitalist',
    'Clinical Pharmacology',
    'Pain Medicine',
    'Legal Medicine',
    'Assistant, Podiatric',
    'Podiatrist',
    'Physician Assistant',
    'Nurse Practitioner',
    'Clinical Nurse Specialist',
    'Nurse Anesthetist, Certified Registered',
    'Advanced Practice Midwife',
    'Anesthesiologist Assistant'
]

const searchTypes = {
    'dentist': ['Dentist',
        'Denturist',
        'Dental Hygienist',
        'Dental Therapist',
        'Advanced Practice Dental Therapist',
        'Oral Medicinist',
        'Dental Assistant',
        'Dental Laboratory Technician',
        'Oral & Maxillofacial Surgery'
    ],
    'doctor': [
        'Chiropractor',
        'Optometrist',
        'Technician/Technologist',
        'Independent Medical Examiner',
        'Integrative Medicine',
        'Phlebology',
        'Neuromusculoskeletal Medicine, Sports Medicine',
        'Neuromusculoskeletal Medicine & OMM',
        'Oral & Maxillofacial Surgery',
        'Transplant Surgery',
        'Electrodiagnostic Medicine',
        'Allergy & Immunology',
        'Anesthesiology',
        'Dermatology',
        'Emergency Medicine',
        'Family Medicine',
        'Internal Medicine',
        'Medical Genetics',
        'Neurological Surgery',
        'Nuclear Medicine',
        'Obstetrics & Gynecology',
        'Ophthalmology',
        'Orthopaedic Surgery',
        'Otolaryngology',
        'Pathology',
        'Pediatrics',
        'Physical Medicine & Rehabilitation',
        'Plastic Surgery',
        'Preventive Medicine',
        'Psychiatry & Neurology',
        'Radiology',
        'Surgery',
        'Urology',
        'Colon & Rectal Surgery',
        'General Practice',
        'Thoracic Surgery (Cardiothoracic Vascular Surgery)',
        'Hospitalist',
        'Clinical Pharmacology',
        'Pain Medicine'
    ],
    'physiotherapist': [
        'Neuromusculoskeletal Medicine, Sports Medicine',
        'Neuromusculoskeletal Medicine & OMM',
        'Physical Medicine & Rehabilitation',

    ],
    'pharmacy': [
        'Clinical Pharmacology',
        'Pain Medicine'
    ],
    'other': [
        'Legal Medicine',
        'Assistant, Podiatric',
        'Podiatrist',
        'Physician Assistant',
        'Nurse Practitioner',
        'Clinical Nurse Specialist',
        'Nurse Anesthetist, Certified Registered',
        'Advanced Practice Midwife',
        'Anesthesiologist Assistant'
    ]
}





