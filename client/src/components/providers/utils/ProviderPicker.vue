<template>
  <div class="flex items-center">
    <provider-chip @click="emit('update:searching', !searching)"
                   v-bind="{ clickable: true, emptyLabel: 'Select Provider', modelValue: multiple ? undefined : modelValue, ...$attrs}">
      <template v-slot:right>
        <slot name="right">
        <q-icon class="q-ml-sm" name="mdi-menu-down"></q-icon>
        </slot>
      </template>
      <template v-slot:menu>
        <q-menu>
          <div class="w350 mw100 q-pa-md">
            <q-input v-model="search.text">
              <template v-slot:prepend>
                <q-icon name="mdi-magnify"></q-icon>
              </template>
            </q-input>
            <q-list separator>
              <provider-item simple v-for="(pr, i) in p$.data" :key="`pr-${i}`" :model-value="pr" clickable
                             @click="selectProvider(pr)"></provider-item>
            </q-list>
          </div>
        </q-menu>
      </template>
    </provider-chip>
    <template v-if="multiple">
      <provider-chip v-for="(p, i) in mv$.data" :key="`pmv-${i}`" :model-value="p" removable>
        <template v-slot:right>
          <q-btn dense flat size="xs" color="red" name="mdi-close" @click="selectProvider(p)"/>
        </template>
      </provider-chip>
    </template>
  </div>
</template>

<script setup>
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';

  import {useProviders} from 'stores/providers';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {computed} from 'vue';

  const providerStore = useProviders();

  const { search, searchQ } = HQuery({})

  const emit = defineEmits(['update:model-value', 'update:searching'])
  const props = defineProps({
    modelValue: { required: true },
    multiple: Boolean,
    searching: Boolean,
    emitValue: Boolean,
  })

  const { h$: p$ } = HFind({
    store: providerStore,
    params: computed(() => {
      const query = {
        ...searchQ.value
      };
      if (props.modelValue) {
        if (Array.isArray(props.modelValue)) {
          query._id = { $nin: props.modelValue.map(a => a._id) };
        } else query._id = { $ne: props.modelValue._id };
      }
      return {
        query
      }
    })
  })

  const { h$: mv$ } = HFind({
    store: providerStore,
    params: computed(() => {
      const query = {};
      if (props.modelValue) {
        if (Array.isArray(props.modelValue)) {
          query._id = { $in: props.modelValue.map(a => a._id || a) };
        } else query._id = { $eq: props.modelValue._id || props.modelValue };
      }
      return {
        query
      }
    })
  })

  const selectProvider = (pr) => {
    emit('update:model-value', pr);
    if (props.multiple) {
      const list = [...props.modelValue || []];
      const idx = list.map(a => a._id || a).indexOf(pr._id);
      if (idx > -1) list.splice(idx, 1);
      else list.push(props.emitValue ? pr._id : pr);
      emit('update:model-value', list)
    } else emit('update:model-value', props.emitValue ? pr._id : pr)
    emit('update:searching', false)
  }
</script>

<style lang="scss" scoped>

</style>
