<template>
  <div class="_fw">
    <div class="row">
      <q-tabs v-model="tab" no-caps indicator-color="primary">
        <q-tab name="enter">
          <span class="font-3-4r tw-six">Enter Price</span>
        </q-tab>
        <q-tab name="upload">
          <span class="font-3-4r tw-six">Upload Prices</span>
        </q-tab>
      </q-tabs>
    </div>
    <q-tab-panels v-model="tab" class="_panel" animated transition-next="jump-up" transition-prev="jump-down">
      <q-tab-panel class="_panel" name="enter">

        <div class="_form_grid q-py-md _f_g_r">
          <div class="_form_label">Type</div>
          <div class="q-pa-sm">
            <q-radio v-model="form.type" val="procedures" label="Procedure"></q-radio>
            <q-radio v-model="form.type" val="meds" label="Drug"></q-radio>
            <q-radio v-model="form.type" val="other" label="Other"></q-radio>
          </div>
          <template v-if="form.type">
            <div class="_form_label">{{ types[form.type] || 'Choose Type' }}</div>
            <div class="q-pa-sm">
              <template v-if="form.type === 'procedures'">
                <procedure-autocomplete v-if="!form.subject" @update:model-value="setSubject"></procedure-autocomplete>
                <procedure-item v-else :model-value="form.subject"></procedure-item>
              </template>
              <template v-if="form.type === 'meds'">
                <meds-list ndc v-if="!form.subject" @ndc="setSubject"></meds-list>
                <med-item v-else :model-value="form.subject"></med-item>
              </template>
            </div>
          </template>
        </div>
        <q-slide-transition>
          <div class="_fw" v-if="form.subject">
            <div class="_form_grid _f_g_r">
              <div class="_form_label">Name</div>
              <div class="q-pa-sm">
                <q-input @update:model-value="autoSave('name')" dense filled v-model="form.name"></q-input>
              </div>
              <div class="_form_label">Description</div>
              <div class="q-pa-sm">
                <q-input @update:model-value="autoSave('description')" dense filled autogrow
                         v-model="form.description"></q-input>
              </div>
              <div class="_form_label">Price</div>
              <div class="q-pa-sm">
                <money-input dense filled :model-value="(form.price || 0) / 100" @update:model-value="setPrice($event)"
                             prefix="$"
                             :decimal="2"></money-input>
              </div>
            </div>
            <div v-if="!form._id" class="row justify-end q-py-md">
              <q-btn class="_a_btn" no-caps label="Save Price" icon-right="mdi-content-save" @click="save"></q-btn>
            </div>
          </div>
        </q-slide-transition>


      </q-tab-panel>
      <q-tab-panel class="_panel" name="upload">

        <div class="_fw q-py-md">
          <csv-upload
              v-bind="{
          response,
         headers: 'type,price,name,code,rxcui,ndc,description',
         required: requiredFields,
         exampleData
          }"
              @ready="uploadList"
              @clear="response = {}"
          >
            <template v-slot:response="scope">
              <div class="_fw text-center font-1r">
                <p>New prices created - {{ scope.response.added }}</p>
                <p>Existing prices updated from this list - {{ scope.response.existing?.total }}</p>
                <p>Data errors - <span class="text-red text-weight-bold">{{ scope.response.errors?.length || 0 }}</span>
                  &nbsp;(see
                  below)</p>
              </div>
            </template>
          </csv-upload>
        </div>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import ProcedureAutocomplete from 'components/care/procedures/lists/ProcedureAutocomplete.vue';
  import ProcedureItem from 'components/care/procedures/cards/ProcedureItem.vue';
  import MedItem from 'components/care/meds/cards/MedItem.vue';
  import CsvUpload from 'components/common/uploads/csv/CsvUpload.vue';
  import MedsList from 'components/care/meds/lists/MedsList.vue';

  import {computed, ref} from 'vue';
  import {usePrices} from 'stores/prices';
  import {HForm, HSave} from 'src/utils/hForm';
  import {axiosFeathers, restCore} from 'components/common/uploads/services/utils';


  const priceStore = usePrices();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: Object,
    bundleId: { required: true }
  })

  const tab = ref('enter');

  const types = {
    'procedures': 'Procedure',
    'meds': 'Drug',
    'other': 'Item'
  }

  const formFn = (defs) => {
    return {
      type: 'procedures',
      ...defs
    }
  }

  const subject = ref({})
  const { form, save } = HForm({
    store: priceStore,
    formFn,
    value: computed(() => props.modelValue),
    beforeFn: (val) => {
      if (!val.bundle) val.bundle = props.bundleId;
      return val;
    },
    afterFn: (val) => {
      tab.value = 'enter'
      emit('update:model-value', val)
    }
  })
  const { autoSave } = HSave({ form, store: priceStore, save, pause: computed(() => !form.value._id) })
  const setPrice = (val) => {
    form.value.price = val * 100;
  }
  const setSubject = (val) => {
    form.value.subject = val._id;
    subject.value = val;
    form.value.name = val.layName || val.name;
    form.value.description = val.layDescription || val.description;
    form.value.code = val.rxcui || val.code
    form.value.ndcs = val.ndcs || []
    form.value.rxcui = val.rxcui
  }

  const response = ref({})

  const uploadList = async (val, meta) => {
    const res = await axiosFeathers().patch(`/bundles/${props.bundleId}`, val, {
      params: {
        runJoin: { addPrices: meta },
        core: restCore()
      }
    });
    response.value = res.data?._fastjoin?.addPrices;
  };

  const exampleData = computed(() => {
    return [
      {
        header: 'Type',
        required: true,
        ex: '"p" for procedure | "m" for meds | "o" for other'
      },
      {
        header: 'Unit Price',
        required: true,
        ex: '10.00 (for ten dollars)'
      },
      {
        header: 'Name',
        required: true,
        ex: 'Treat metacarpal fracture',
      },
      {
        header: 'Code',
        required: false,
        ex: 'Procedure code'
      },
      {
        header: 'RXCUI',
        required: false,
        ex: 'RXCUI (for Rx)'
      },
      {
        header: 'NDC',
        required: false,
        ex: 'NDC (for Rx)'
      },
      {
        header: 'Description',
        required: false,
        ex: 'Surgery to stabilize the wrist'
      }
    ];
  })

  const requiredFields = ref([
    {
      field: 'type',
      v: ['notEmpty'],
      required: true
    },
    {
      field: 'amount',
      name: 'Unit Price',
      v: ['notEmpty'],
      required: true
    },
    {
      field: 'name',
      name: 'Name',
      v: ['notEmpty'],
      required: true
    }
  ]);

</script>

<style lang="scss" scoped>
  .__t {
    width: 100%;
    padding: 6px;
    font-size: .75rem;
    font-weight: 600;
    color: #999;
  }
</style>
