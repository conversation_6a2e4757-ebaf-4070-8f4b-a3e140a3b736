<template>
  <div class="_fw">
    <div class="row" v-if="pb?._id">
      <div :class="`col-12 col-md-${inline ? 12 : 6} q-pa-sm`">
        <div class="__c">
          <div class="__t">Service Details</div>
          <div class="_form_grid _f_g_r">
            <div class="_form_label">Name</div>
            <div class="q-pa-sm">
              <span class="tw-six font-1r">{{ pb.name }}</span>
            </div>
            <div class="_form_label">Description</div>
            <div class="q-pa-sm">
              <span class="font-7-8r">{{ pb.description }}</span>
            </div>
            <div class="_form_label">Provider</div>
            <div class="q-pa-sm">
              <provider-chip :model-value="pb.provider"></provider-chip>
            </div>
            <div class="_form_label">Availability</div>
            <div class="q-pa-sm">
              <q-chip color="primary" v-if="pb.public" dark class="tw-six" label="Public"></q-chip>
              <q-chip color="secondary" v-else dark class="tw-six" label="Private"></q-chip>
            </div>
          </div>
        </div>
      </div>
      <div :class="`col-12 col-md-${inline ? 12 : 6} q-pa-sm`">
        <div class="__c">
          <div class="__t">Prices</div>
          <book-prices :bundle="pb"></book-prices>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import BookPrices from 'components/providers/bundles/forms/BookPrices.vue';
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';

  import {useBundles} from 'stores/bundles';
  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useRoute} from 'vue-router';
  import {dollarString} from 'src/utils/global-methods';

  const store = useBundles();
  const route = useRoute();

  const props = defineProps({
    modelValue: { required: false },
    inline: Boolean
  })

  const { item: pb } = idGet({
    store,
    value: computed(() => props.modelValue || route.params.bookId)
  })

</script>

<style lang="scss" scoped>
  .__c {
    padding: 30px min(15px, 2vw);
    border-radius: 20px;
    box-shadow: 0 2px 18px -6px #999;
    background: white;
    margin: 10px 0;
    position: relative;

    .__t {
      position: absolute;
      top: 0;
      left: 2.5%;
      width: 95%;
      transform: translate(0, -30%);
      border-radius: 6px;
      background: linear-gradient(180deg, var(--q-p1), white);
      color: var(--q-p7);
      font-weight: 600;
      padding: 6px 8px;
      font-size: 1rem;
    }
  }
</style>
