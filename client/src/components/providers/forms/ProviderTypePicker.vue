<template>
  <div class="_fw">
    <q-tab-panels animated transition-next="slide-down" transition-prev="slide-up" class="_panel" :model-value="!!modelValue">
      <q-tab-panel class="_panel" :name="false">

        <q-list separator>
          <q-item v-for="t in Object.keys(types)" :key="`t-${t}`" clickable @click="$emit('update:model-value', t)">
            <q-item-section avatar>
              <q-avatar color="white">
                <div class="flex flex-center _fa">
                  <span>{{ types[t].icon }}</span>
                </div>
              </q-avatar>
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ types[t].label }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>

      </q-tab-panel>
      <q-tab-panel class="_panel" :name="true">
<!--        <q-chip outline removable icon-remove="mdi-close" @remove="$emit('update:model-value', undefined)">-->
<!--          <q-avatar color="white">-->
<!--            <div class="flex flex-center _fa">-->
<!--              <span>{{ types[modelValue].icon }}</span>-->
<!--            </div>-->
<!--          </q-avatar>-->
<!--          <span class="q-ml-xs">{{types[modelValue].label}}</span>-->
<!--        </q-chip>-->
        <provider-specialty-picker :type="modelValue ? [modelValue] : []" :model-value="specialty" @update:model-value="emit('update:specialty', $event)">
        </provider-specialty-picker>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import ProviderSpecialtyPicker from 'components/providers/forms/ProviderSpecialtyPicker.vue';

  import {ref} from 'vue';
  import {providerTypes} from 'components/providers/utils/types';

  const emit = defineEmits(['update:model-value', 'update:specialty']);
  const props = defineProps({
    modelValue: String,
    specialty: String
  })

  const types = ref(providerTypes)

</script>

<style lang="scss" scoped>

</style>
