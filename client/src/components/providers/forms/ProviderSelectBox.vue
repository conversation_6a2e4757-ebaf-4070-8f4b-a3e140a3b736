<template>
  <div class="_fw">
    <provider-chip @click="setTab(tab === 'chip' ? 'list' : 'chip')" empty-label="Choose Provider" :model-value="provider" :chip-attrs="{ clickable: true, color: 'transparent'}">
      <template v-slot:menu>
        <q-icon class="q-ml-sm" v-bind="tab === 'chip' ? { name: 'mdi-menu-down' } : { name: 'mdi-close', color:'red'}"></q-icon>
      </template>
    </provider-chip>
    <q-tab-panels class="_panel" v-model="tab" animated transition-next="jump-up" transition-prev="jump-down">
      <q-tab-panel class="_panel" name="chip">

      </q-tab-panel>
      <q-tab-panel class="_panel" name="list">
        <q-list separator>
          <q-item clickable @click="tab = 'form'">
            <q-item-section avatar>
              <q-icon name="mdi-plus" color="primary"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label>Add New</q-item-label>
            </q-item-section>
          </q-item>
          <provider-item
              simple
              v-for="(p, i) in p$.data" :key="`p-${i}`"
              :model-value="p"
              @update:model-value="emitUp(p)">
            <template v-slot:side>
              <q-item-section side v-if="provider?._id === p._id">
                <q-icon name="mdi-check" color="green"></q-icon>
              </q-item-section>
            </template>
          </provider-item>
        </q-list>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="form">
        <div class="row q-pt-sm">
          <q-btn dense flat size="sm" color="primary" @click="tab = 'list'">
            <q-icon size="25px" color="primary" name="mdi-chevron-left"></q-icon>
          </q-btn>
        </div>
        <provider-search  stack :model-value="provider" @update:model-value="emitUp"></provider-search>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';
  import ProviderSearch from 'components/providers/forms/ProviderSearch.vue';

  import {HFind} from 'src/utils/hFind';
  import {useProviders} from 'stores/providers';
  import {computed, ref} from 'vue';

  import {idGet} from 'src/utils/id-get';
  const providerStore = useProviders()

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    query: Object,
    modelValue: { required: false }
  })

  const tab = ref('chip');

  const { item:provider } = idGet({
    store: providerStore,
    value: computed(() => props.modelValue)
  })

  const { h$:p$ } = HFind({
    store: providerStore,
    params: computed(() => {
      return {
        query: { ...props.query }
      }
    })
  })

  const setTab = (val) => {
    if(val === 'list'){
      if(!p$.total) tab.value = 'form';
      else tab.value = 'list'
    } else tab.value = val;
  }

  const emitUp = (val) => {
    emit('update:model-value', val);
    tab.value = 'chip';
  }
</script>

<style lang="scss" scoped>

</style>
