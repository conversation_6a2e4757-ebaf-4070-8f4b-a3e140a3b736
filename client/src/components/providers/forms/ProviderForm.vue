<template>
  <div class="_fw">
    <div class="_form_grid _f_g_r q-pt-md">
      <div class="_form_label">Legal Name</div>
      <div class="q-pa-sm">
        <q-input dense filled v-model="form.legalName"></q-input>
      </div>
      <div class="_form_label">Trade Name</div>
      <div class="q-pa-sm">
        <q-input dense filled v-model="form.name"></q-input>
      </div>

    </div>

    <q-slide-transition>
      <div class="q-my-md" v-if="!pause && p$.total">
        <q-list separator>
          <q-item-label header>Likely Matches</q-item-label>
          <provider-item @click="form = p" simple v-for="(p, i) in p$.data" :key="`p-${i}`" :model-value="p">
            <template v-slot:caption="scope">
              <q-item-label caption>{{(scope.opt.locations || [])[0]?.city || 'No City Listed'}}, {{(scope.opt.locations || [])[0]?.region || 'No State Listed'}}</q-item-label>
            </template>
          </provider-item>
        </q-list>
      </div>
    </q-slide-transition>
    <div class="_form_grid _f_g_r">
      <div class="_form_label">Phone</div>
      <div class="q-pa-sm">
        <phone-input :input-attrs="{ dense: true, filled: true }" v-model="form.phone"></phone-input>
      </div>
      <div class="_form_label">Locations</div>
      <div class="q-pa-sm">
        <multi-address dense filled v-model="form.locations"></multi-address>
      </div>

      <div class="_form_label">Primary Class</div>
      <div class="q-pa-sm">
        <provider-type-chip picker v-model="form.primaryType"></provider-type-chip>
      </div>
      <div class="_form_label">Addl Classes</div>
      <div class="q-pa-sm">
        <provider-type-chip picker multiple v-model="form.otherTypes"></provider-type-chip>
      </div>

      <template v-if="typeList.length">
        <div class="_form_label">Specialities</div>
        <div class="q-pa-sm">
          <provider-specialty-picker multiple v-model="form.specialties"></provider-specialty-picker>
        </div>
      </template>

      <div class="_form_label">Tags (Searchable)</div>
      <div class="q-pa-sm">
        <tag-picker v-model="form.tags" :store="pStore"></tag-picker>
      </div>
    </div>

    <div class="q-pt-md row justify-end">
      <q-btn class="_s_btn" no-caps push label="Save" icon="mdi-content-save" @click="save"></q-btn>
    </div>

  </div>
</template>

<script setup>
  import PhoneInput from 'components/common/phone/PhoneInput.vue';
  import MultiAddress from 'components/common/address/tomtom/MultiAddress.vue';
  import ProviderTypeChip from 'components/providers/cards/ProviderTypeChip.vue';
  import ProviderSpecialtyPicker from 'components/providers/forms/ProviderSpecialtyPicker.vue';
  import TagPicker from 'components/common/pickers/TagPicker.vue';

  import {useProviders} from 'stores/providers';
  import {HForm} from 'src/utils/hForm';
  import {idGet} from 'src/utils/id-get';
  import {computed, watch} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';

  const pStore = useProviders();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false },
    org: { required: false },
  })

  const { item: provider } = idGet({
    store: pStore,
    value: computed(() => props.modelValue)
  })

  const { form, save } = HForm({
    store: pStore,
    value: provider,
    beforeFn: (item) => {
      if (!item.org && props.org) item.org = props.org?._id || props.org
      if(!item.typeDescription) item.typeDescription = ''
      return item
    },
    afterFn: (val) => {
      emit('update:model-value', val);
    }
  })

  const typeList = computed(() => {
    const { primaryType, otherTypes } = form.value || {}
    const list = [...otherTypes || []];
    if (primaryType) list.push(primaryType);
    return list;
  })

  const search = computed(() => {
    return {
      text: form.value.name || form.value.legalName
    }
  });
  const { searchQ } = HQuery({ or: true, keys: ['name', 'legalName'], search })

  const pause = computed(() => !!form.value?._id || !(search.value.text?.length > 2))
  const { h$:p$ } = HFind({
    store: pStore,
    pause,
    params: computed(() => {
      return {
        _search: {
          text: search.value.text,
        },
        query: { $limit: 5, ...searchQ.value, org: { $exists: false } }
      }
    })
  })

  watch(() => props.org, (nv) => {
    if (nv?._id) {
      const arr = ['name', 'legalName', 'avatar', 'email', 'phone', 'address']
      for (let i = 0; i < arr.length; i++) {
        const k = arr[i]
        if (!form.value[k]) form.value[k] = nv[k];
      }
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
