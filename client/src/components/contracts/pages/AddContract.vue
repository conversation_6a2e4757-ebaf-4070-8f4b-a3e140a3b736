<template>
  <div class="_fw">
    <div class="q-pa-sm">
      <div class="tw-six font-1r">Select Public Template</div>
    </div>
    <q-input dense filled class="mw400 q-mt-md" v-model="search.text" placeholder="Find a template...">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>

    <div class="row q-py-md">
      <div class="col-12 col-md-3 q-pa-sm" v-for="(c, i) in c$.data" :key="`c-${i}`">
        <div class="__c" @click="setEditing(c)">
          <contract-card :model-value="c"></contract-card>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
  import ContractCard from 'components/contracts/cards/ContractCard.vue';

  import {computed, ref} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {useContracts} from 'stores/contracts';
  import {useRouter} from 'vue-router';
  import {LocalStorage} from 'symbol-auth-client';

  const contractStore = useContracts();
  const router = useRouter();

  const { search, searchQ } = HQuery({ keys: ['name', 'description'] })
  const { h$: c$ } = HFind({
    store: contractStore,
    params: computed(() => {
      const query = { ...searchQ.value, public: true }
      return {
        query
      }
    })
  })

  const orgId = computed(() => LocalStorage.getItem('org_id'))

  const setEditing = (value) => {
    router.push({
      name: 'edit-contract',
      params: { contractId: value._id, type: 'copy' },
      query: { ownerId: orgId.value, ownerService: 'orgs' }
    })
  }
</script>

<style lang="scss" scoped>
  .__c {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 2px 8px -2px #999;
    padding: 30px 20px;
    cursor: pointer;
    transition: all .2s;

    &:hover {
      box-shadow: 0 4px 16px -8px #999;
    }
  }
</style>
