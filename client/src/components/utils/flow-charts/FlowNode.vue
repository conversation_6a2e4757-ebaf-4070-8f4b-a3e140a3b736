<template>
  <div class="_fw _fh flex flex-center cursor-pointer">
    <div v-if="parent" class="text-weight-medium text-center" v-html="node?.q || ''"></div>
    <div v-else class="text-weight-bold text-center" v-html="node?.label || ''"></div>
  </div>
</template>

<script setup>

  import {computed} from 'vue';
  import { getNode } from './index';

  const props = defineProps({
    modelValue: { required: true },
    parent: { type: Boolean },
    chart: Object
  })

  const node = computed(() => {
    if(typeof props.modelValue === 'string'){
      return getNode(props.modelValue, props.chart)
    } else return props.modelValue;
  })
</script>

<style lang="scss" scoped>

</style>
