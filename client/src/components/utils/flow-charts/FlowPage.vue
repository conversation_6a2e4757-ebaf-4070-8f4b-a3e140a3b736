<template>
  <q-page class="pd5 q-px-md">
    <div class="row justify-center">

      <div class="_cent">
        <div class="row items-center">
          <div>
            <h5>Flow Charts</h5>
          </div>
          <q-chip text-color="white" clickable class="q-mx-md" color="primary" icon-right="mdi-plus" label="Add New"
                  @click="dialog = true"></q-chip>
        </div>

        <div class="row q-gutter-md">
          <div class="col-12 col-md-4" v-for="(item, i) in items || []" :key="`flow-${i}`">
            <div class="_fw _c1">
              <flow-card :model-value="item"></flow-card>
            </div>
          </div>
        </div>

      </div>
    </div>

    <common-dialog setting="full" v-model="dialog">
      <div class="q-pa-md">
        <flow-form></flow-form>
      </div>
    </common-dialog>

  </q-page>
</template>

<script setup>
  import FlowForm from './FlowForm.vue';
  import {HFind} from 'src/utils/hFind';
  import {useFlowCharts} from 'stores/flow-charts';
  import {ref, computed} from 'vue';
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import FlowCard from 'src/components/utils/flow-charts/FlowCard.vue';

  const store = useFlowCharts();

  const dialog = ref(false);

  const { h$ } = HFind({
    store,
    params: ref({})
  })

  const items = computed(() => h$.data);
</script>

<style lang="scss" scoped>
</style>
