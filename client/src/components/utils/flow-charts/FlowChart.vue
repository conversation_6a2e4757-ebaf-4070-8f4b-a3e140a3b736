<template>
  <div class="_fw">
    <div class="__arrow_up row justify-center q-mt-md">
      <q-btn v-if="layer?.parent" color="primary" dense flat round @click="cl = layer.parent" icon="mdi-chevron-up"></q-btn>
    </div>
    <div class="row justify-center items-center no-wrap q-pt-md">
      <div class="col-grow">
        <div class="__b">
          <div class="font-1-1-4r text-weight-bold" v-html="activeNode?.label || ''"></div>
          <q-separator class="q-my-sm"></q-separator>
          <div class="font-1r" v-html="activeNode?.text || ''"></div>
        </div>
      </div>
    </div>

    <div class="relative-position">
      <div class="row justify-center">

      </div>
      <div class="row justify-center">
        <div class="__child col-12 col-md-6">
          <div class="__arrow_left" v-if="layer?.parent">
            <q-btn color="primary" dense flat round @click="cl = layer.parent" icon="mdi-chevron-left"></q-btn>
          </div>
          <div
              :class="`__n ${(active === layer?.id) || !cl ? '__s' : ''}`"
              @pointerenter="hover = layer?.id || ''"
              @pointerleave="hover = ''"
              @click="cl = layer.id"
          >
            <flow-node :chart="flowChart" :parent="true" :model-value="layer"></flow-node>
          </div>
        </div>
      </div>
    </div>
    <div class="relative-position">
      <div class="__parent row justify-center">
        <div
            :class="`__child col-${12/(children.length)}`"
            v-for="(childKey, i) in children"
            :key="`child-${i}`"
            @click="cl = childKey"
        >
          <div
              :class="`__n ${active === childKey ? '__s' : ''}`"
              @pointerenter="hover = childKey"
              @pointerleave="hover = ''"
          >
            <flow-node :chart="flowChart" :model-value="layer.children[childKey]"></flow-node>
          </div>
          <div class="__arrow" v-if="hasChildren(childKey)">
            <q-btn color="primary" dense flat round @click="cl = childKey" icon="mdi-chevron-down"></q-btn>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>

  import {computed, ref} from 'vue';
  import FlowNode from 'src/components/utils/flow-charts/FlowNode.vue';
  import {idGet} from 'src/utils/id-get';
  import { useFlowCharts } from 'src/stores/flow-charts';
  const store = useFlowCharts();
  import { getNode } from './index';
  import {_get} from 'symbol-syntax-utils';

  const props = defineProps({
    modelValue: Object
  })

  const hover = ref('');

  const cl = ref('');


  const { item: flowChart } = idGet({
    store,
    value: computed(() => props.modelValue)
  })

  const activeNode = computed(() => {
    return cl.value ? getNode(cl.value, flowChart.value) : _get(flowChart.value, ['nodes', 0])
  })

  const layer = computed(() => {
    if (childrenCheck(activeNode.value)) return activeNode.value;
    else return getNode(activeNode.value?.parent, flowChart.value);
  })

  const children = computed(() => Object.keys(layer.value?.children || {}) || [])

  const active = computed(() => {
    return hover.value ? hover.value : cl.value;
  })

  const childrenCheck = (node) => {
    const children = node?.children || {};
    return !!(children && Object.keys(children).length);
  }
  const hasChildren = (childKey) => {
    const children = layer.value.children || {};
    const node = children[childKey];
    return childrenCheck(node);
  }

</script>

<style lang="scss" scoped>
  .__n {
    padding: 20px;
    border-radius: 10px;
    height: 100%;
    //border: solid 2px var(--q-primary);
    background: white;
    box-shadow: none;
    transition: all .5s
  }
  .__b {
    padding: 30px;
    border-radius: 15px;
    background: white;
    box-shadow: none;
  }

  .__parent .__child {
    position: relative;
    padding-top: 30px;
    transition: .5s;
  }

  .__child {
    margin-top: 30px;
    padding: 30px 5px 0 5px;
    position: relative;
    cursor: pointer;
  }

  /*We will use ::before and ::after to draw the connectors*/
  .__child::before, .__child::after {
    content: '';
    position: absolute;
    top: 0;
    right: 50%;
    border-top: 1px solid var(--q-primary);
    width: 50%;
    height: 30px;
  }

  .__child::after {
    right: auto;
    left: 50%;
    border-left: 1px solid var(--q-primary);
  }

  /*We need to remove left-right connectors from elements without
any siblings*/
  .__child:only-child::after, .__child:only-child::before {
    display: none;
  }

  /*Remove space from the top of single children*/
  .__child:only-child {
    padding-top: 0;
  }

  /*Remove left connector from first child and
right connector from last child*/
  .__child:first-child::before, .__child:last-child::after {
    border: 0 none;
  }

  /*Adding back the vertical connector to the last nodes*/
  .__child:last-child::before {
    border-right: 1px solid var(--q-primary);
    border-radius: 0 5px 0 0;
    -webkit-border-radius: 0 5px 0 0;
    -moz-border-radius: 0 5px 0 0;
  }

  .__child:first-child::after {
    border-radius: 5px 0 0 0;
    -webkit-border-radius: 5px 0 0 0;
    -moz-border-radius: 5px 0 0 0;
  }

  /*Time to add downward connectors from parents*/
  .__parent::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    border-left: 1px solid var(--q-primary);
    width: 0;
    height: 30px;
  }

  .__s {
    box-shadow: 0 10px 20px -8px rgba(0, 0, 0, .7);
  }

  .__a {
    border: solid 3px var(--q-primary);
  }

  .__arrow {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 40px);
    animation: bounce 3s infinite;
  }

  .__arrow_left {
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translate(-50%, );
    animation: bounce_left 3s infinite;
  }
  .__arrow_up {
    animation: bounce_up 3s infinite;
  }
  @keyframes bounce_up {
    0% {
      transform: translate(0, 0)
    }
    50% {
      transform: translate(0, -20%)
    }
    100% {
      transform: translate(0, 0)
    }
  }
  @keyframes bounce_left {
    0% {
      transform: translate(-10px, -50%)
    }
    50% {
      transform: translate(-20px, -50%)
    }
    100% {
      transform: translate(-10px, -50%)
    }
  }

  @keyframes bounce {
    0% {
      transform: translate(-50%, 30px)
    }
    50% {
      transform: translate(-50%, 40px)
    }
    100% {
      transform: translate(-50%, 30px)
    }
  }
</style>
