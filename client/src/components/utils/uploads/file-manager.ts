import {computed, ComputedRef, Reactive} from 'vue';
import {useUploads} from 'stores/uploads';
import {HFind} from 'src/utils/hFind';
import {_get} from 'symbol-syntax-utils';
import {idGet} from 'src/utils/id-get';
import {AnyRef} from 'src/utils/types';

type ManageFindArgs = {
    sources: Reactive<{ data: Array<any>} & any>,
    paths: Array<string>,
    sourceIdPath:string|undefined,
    uploadIdPath:string|undefined // Assumes the upload _id is at some_path.uploadId
}
export const manageFindUploads = ({ sources, paths, sourceIdPath = '_id', uploadIdPath = 'uploadId' }:ManageFindArgs) => {
    const uploadStore:any = useUploads();

    /** return a list of upload ids and an object with the key is the upload id and the value is the source id - this makes it easier to attach the upload record to the source by id. A big boost in ease of implementation */
    const ids = computed(() => {
        if(!Array.isArray(sources.data)) return { list: [], obj: {}, inStore: [] }
        const list:any = [];
        const obj:any = {};
        const inStore:any = [];
        for(let i = 0; i < sources.data.length; i++){
            const source = sources.data[i];
            for(const p of paths){
                const id:any = _get(source, `${p}${uploadIdPath ? `.${uploadIdPath}` : ''}`);
                if(id) {
                    list.push(id)
                    obj[id] = source[sourceIdPath]
                    const isIn = uploadStore.getFromStore(id).value;
                    if(isIn) {
                        inStore.push(isIn)
                        continue;
                    }
                    const isJoined:any = _get(source, `_fastjoin.files.${p}`)
                    if(isJoined?._id) inStore.push(isJoined);
                }
            }
        }
        return {list, obj, inStore}
    })

    const { h$:u$ } = HFind({
        store: uploadStore,
        limit: computed(() => ids.value.list.length - ids.value.inStore.length),
        pause: computed(() => ids.value.inStore.length >= ids.value.list.length),
        params: computed(() => {
            return {
                query: {
                    _id: { $in: ids.value.list, $nin: ids.value.inStore.map(a => a._id) }
                }
            }
        })
    })

    const uploads = computed(() => [...u$.data, ...ids.value.inStore]);

    /** Return 2 objects of the upload record results - one keyed by the upload id and one keyed by the source id */
    const byId = computed(() => {
        const bySource = {};
        const byUpload = {};
        for(let i = 0; i < uploads.value.length; i++){
            byUpload[uploads.value[i]._id] = uploads.value[i];
            bySource[ids.value.obj[uploads.value[i]._id]] = uploads.value[i];
        }
        return { byUpload, bySource }
    })

    return {
        uploadStore,
        uploads,
        byId
    }
}

export const manageGetUploads = ({ source, path, uploadIdPath = 'uploadId' }:{ source: AnyRef<any>, path: string, uploadIdPath?:string }) => {
    const uploadStore:any = useUploads();
    const { item } = idGet({
        store: uploadStore,
        value: computed(() => {
            const joined = _get(source.value, `_fastjoin.files.${path}`);
            if(joined) return joined;
            const val = _get(source.value, `${path}`)
            if(val?.uploadId) return val.uploadId;
            return val;
        })
    })

    return {
        item,
        uploadStore
    }
}
