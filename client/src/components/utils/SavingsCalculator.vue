<template>
  <div id="SavingsCalculator" class="_fw">


    <div class="row">

      <div :class="`col-12 col-md-${split ? '6' : '12'} q-pa-${split ? 'md' : 'sm'} alt-font`" :style="colStyle">
        <div class="font-1-1-4r text-weight-bolder q-py-lg main-font">Cost/Benefit Comparison</div>
        <div class="font-3-4r">Group FTEs</div>
        <div class="row q-pb-md">
          <money-input
              prefix=""
              v-model="form.fteCount"
              v-bind="{
              ...inputs,
              label: '',
              type: 'number',
              style: 'width: 120px',
              class: 'q-mb-sm',
              inputClass: 'font-1-1-4r text-weight-bold _fw'
            }"
          ></money-input>
        </div>

        <div>Do you currently have a group plan?</div>
        <div class="row">
          <q-radio size="sm" v-model="form.existingPlan" :val="true" label="Yes"
                   @update:model-value="toggleCurrent"></q-radio>
          <q-radio size="sm" v-model="form.existingPlan" :val="false" label="No"
                   @update:model-value="toggleCurrent"></q-radio>
        </div>
        <q-slide-transition>
          <div class="_fw" v-if="form.existingPlan">
            <h5>Current Plan Details</h5>
            <div class="q-py-sm">
              <metric-slider
                  title="Current participant contributions per emp/month"
                  v-bind="{
                    ...sliders,
                    color: 'ir-deep-purple-4',
                    max: 2000,
                    step: 25,
                    labelFn: (v) => dollarString(v, '$', 0)
                  }"
                  v-model="form.currentParticipantContribution"
              ></metric-slider>
            </div>
            <div class="q-py-sm">
              <metric-slider
                  title="Current group contributions per emp/month"
                  v-bind="{
                    ...sliders,
                    color: 'ir-deep-purple-10',
                    max: 2000,
                    step: 25,
                    labelFn: (v) => dollarString(v, '$', 0)
                  }"
                  v-model="form.currentGroupContribution"
              ></metric-slider>
            </div>


          </div>
        </q-slide-transition>

        <div class="q-py-sm"></div>

        <q-expansion-item
            expand-icon="mdi-menu-down"
        >
          <template v-slot:header>
            <div class="h5">Plan Spend <span class="font-1r text-weight-light">(settings)</span></div>
          </template>

          <div class="q-py-sm">
            <metric-slider
                v-bind="{
                ...sliders,
                color: 'ir-light-blue-4',
                max: 2000,
                step: 25,
                title: 'Avg medical spend/Month per participant (in plan or otherwise)',
                labelFn: (v) => dollarString(v, '$', 0),
                caption: '2023 national avg is $673*'
              }"
                v-model="form.avgSpend"
            ></metric-slider>
          </div>

          <div class="q-py-sm">
            <metric-slider
                v-bind="{
              ...sliders,
              max: 2000,
              step: 25,
              color:'ir-blue-10',
              title: 'Desired group contribution per emp/month',
              labelFn: (v) => dollarString(v, '$', 0)
            }"
                v-model="form.estGroupContribution"
            ></metric-slider>
          </div>
          <div class="q-py-sm">
            <metric-slider
                v-bind="{
              ...sliders,
              max: 2000,
              step: 25,
              color:'ir-blue-7',
              title: 'Desired participant contribution (pay reduction) per month',
              labelFn: (v) => dollarString(v, '$', 0)
            }"
                v-model="form.estParticipantContribution"
            ></metric-slider>
          </div>
        </q-expansion-item>

        <q-separator class="q-my-sm"></q-separator>

        <q-expansion-item
            expand-icon="mdi-menu-down"
        >
          <template v-slot:header>
            <div class="h5">Commissions & Fees <span class="font-1r text-weight-light">(settings)</span></div>
          </template>
          <div class="q-py-sm">
            <metric-slider
                v-bind="{
              ...sliders,
              color: 'ir-red-10',
              max: 10,
              markers: true,
              title: 'Current Broker Commission',
              labelFn: (v) => `${v}%`,
              caption: 'of plan spend'
            }"
                v-model="form.currentCommission"
            ></metric-slider>
          </div>

          <div class="q-py-sm">
            <metric-slider
                v-bind="{
              ...sliders,
              max: 10,
              markers: true,
              color: 'ir-red-4',
              title: 'Desired Broker Commission**',
              labelFn: (v) => `${v}%`,
              caption: 'of plan spend'
            }"
                v-model="form.estCommission"
            ></metric-slider>
          </div>

          <div class="q-py-sm">
            <metric-slider
                v-bind="{
              ...sliders,
              max: 60,
              step: 5,
              markers: true,
              color: 'ir-orange-7',
              title: 'Current TPA Fees',
              labelFn: (v) => dollarString(v, '$', 0),
              caption: '/participant per month'
            }"
                v-model="form.currentTPA"
            ></metric-slider>
          </div>

          <div class="q-py-sm">
            <metric-slider
                v-bind="{
              ...sliders,
              max: 60,
              step: 5,
              markers: true,
              color: 'ir-orange-4',
              title: 'Desired TPA Fees**',
              labelFn: (v) => dollarString(v, '$', 0),
              caption: '/participant per month'
            }"
                v-model="form.estTPA"
            ></metric-slider>
          </div>
        </q-expansion-item>

        <q-separator class="q-my-sm"></q-separator>


        <div class="flex items-center q-py-md">
          <q-img :src="icon" style="height: 30px; width: 30px;" fit="contain"></q-img>
          <div style="padding-left: 1px;">
            <div class="h5">&nbsp;Add Ons</div>
          </div>
        </div>


        <div v-for="(k, i) in Object.keys(addOns)" :key="`key-${i}`" class="_fw">
          <div class="row">
            <div class="col-6 q-pa-sm">
              <div class="text-weight-bold">{{ k }}</div>

              <div class="__caption flex items-center">
                <div class="text-weight-bold text-primary">
                  {{ addOns[k].priceDisplay }}&nbsp;
                </div>
                <div>{{ addOns[k].priceCaption }}</div>
              </div>

            </div>
            <div class="col-6 q-pa-sm">
              <div>
                <q-radio size="sm" v-model="form[addOns[k].key]" :val="true" label="Yes, Please"></q-radio>
              </div>
              <div>
                <q-radio color="secondary" size="sm" v-model="form[addOns[k].key]" :val="false"
                         label="No, I'll handle it"></q-radio>
              </div>
            </div>
          </div>

          <q-separator class="q-my-xs"></q-separator>
        </div>

        <div class="text-weight-bold q-py-sm">Plan Types</div>
        <div class="_fw q-px-md q-py-sm">
          <div class="row" v-for="(prod, i) in keys.filter(a => prods[a].type === 'product')" :key="`prod-${i}`">
            <div class="col-6 q-px-sm q-py-xs">
              <div class="text-weight-bold">{{ prods[prod].title }}</div>
              <div class="text-weight-bold text-ir-grey-5">
                <span class="text-primary">{{
                    dollarString(prods[prod].pricing.amt(), '$', 2)
                  }}</span>&nbsp;<span>{{ prods[prod].pricing.caption }}</span>
              </div>
            </div>
            <div class="col-6 q-pa-sm">
              <div class="row justify-center">
                <q-checkbox :model-value="(form.products || []).includes(prod)"
                            @update:model-value="togglePlan(prod)"></q-checkbox>
              </div>
            </div>
          </div>
        </div>


      </div>

      <!--      COST ESTIMATES-->
      <div :class="`col-12 col-md-${split ? '6' : '12'} q-${split ? 'pa-md' : 'py-md'}`" :style="colStyle">
        <div class="font-1-1-4r text-weight-bolder q-py-lg main-font">Plan cost estimates (monthly)</div>
        <div class="_fw __tbw">
          <table>
            <tr>
              <td></td>
              <td v-for="(k, i) in Object.keys(tableData)" :key="`k-${i}`" class="text-weight-bold font-3-4r">
                {{ k }}
              </td>
            </tr>
            <tr class="__sub">
              <td>Group</td>
              <td v-for="(k, i) in Object.keys(tableData)" :key="`er-${k}${i}`"></td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;Current</td>
              <td v-for="(k, i) in Object.keys(tableData)" :key="`er-current-${i}`" class="__num">
                <div v-html="formatNumber(k, 'group', 'current')"></div>
              </td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;Proposed</td>
              <td v-for="(k, i) in Object.keys(tableData)" :key="`er-proposed-${i}`" class="__num __p">
                <div v-html="formatNumber(k, 'group', 'proposed')"></div>
              </td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;Savings</td>
              <td v-for="(k, i) in Object.keys(tableData)" :key="`er-savings-${i}`" class="__num __s">
                <q-icon v-if="tableData[k].message" name="mdi-information">
                  <q-tooltip class="font-3-4r text-weight-medium">{{ tableData[k].message }}</q-tooltip>
                </q-icon>
                <div v-else v-html="formatNumber(k, 'group', 'savings')"></div>
              </td>
            </tr>
            <tr class="_fw __sub">
              <td>Participant</td>
              <td v-for="(k, i) in Object.keys(tableData)" :key="`ee-${k}${i}`"></td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;Current</td>
              <td v-for="(k, i) in Object.keys(tableData)" :key="`ee-current-${i}`" class="__num">
                <div v-html="formatNumber(k , 'participant', 'current')"></div>
              </td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;Proposed</td>
              <td v-for="(k, i) in Object.keys(tableData)" :key="`ee-proposed-${i}`" class="__num __p">
                <div v-html="formatNumber(k, 'participant', 'proposed')"></div>
              </td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;Savings</td>
              <td v-for="(k, i) in Object.keys(tableData)" :key="`ee-savings-${i}`" class="__num __s">
                <q-icon v-if="tableData[k].message" name="mdi-information">
                  <q-tooltip class="font-3-4r text-weight-medium">{{ tableData[k].message }}</q-tooltip>
                </q-icon>
                <div v-else v-html="formatNumber(k, 'participant', 'savings')"></div>
              </td>
            </tr>
          </table>
        </div>


        <div class="_fw q-px-md">
          <div class="flex items-center q-py-sm">
            <q-img style="height: 30px; width: 30px" fit="contain" :src="icon" class="q-mr-sm"></q-img>
            <div><h5>Fees</h5></div>
          </div>
          <div class="__fee">Fees are based on your selected add ons. You can generate a plan here for no charge. Fees
            are for the extras we provide to make your plan awesome - and the fees are ridiculously fair
          </div>
        </div>

        <div class="_fw q-py-md">

          <div class="font-3-4r text-ir-grey-7 text-weight-bold flex items-center">
            <q-chip class="text-weight-bold" outline color="primary" :label="combineTotals('cc')"></q-chip>
            <div>Per Employee Per Month (PEPM)</div>
          </div>

          <div class="text-ir-grey-7 q-py-md">Who pays CommonCare fees?</div>
          <div class="row items-end font-3-4r text-weight-bold text-ir-grey-7">
            <div class="q-pa-sm _fw">

              <q-slider
                  v-model="form.payerSplit"
                  :max="100"
                  :min="0"
                  :step="10"
                  markers
                  track-color="primary"
                  selection-color="secondary"
              ></q-slider>

              <div class="flex items-center text-center">
                <div>
                  <div>{{ combineTotals('groupCc') }} pepm</div>
                  <q-badge
                      class="text-weight-bold"
                      color="primary">
                    Group: {{ 100 - form.payerSplit }}%
                  </q-badge>
                </div>
                &nbsp;-&nbsp;
                <div>
                  <div>{{ combineTotals('participantCc') }} pepm</div>

                  <q-badge
                      class="text-weight-bold"
                      color="secondary"
                  >
                    {{ form.payerSplit }}% Participant
                  </q-badge>
                </div>
              </div>
            </div>
          </div>
        </div>

        <h5>Cost/Benefit for {{ form.fteCount }} participants</h5>

        <div class="_fw __tbw">
          <table>
            <tr class="text-weight-bold">
              <td></td>
              <td>Group</td>
              <td>Participant</td>
              <td>Monthly Total</td>
              <td>Annual Total</td>
            </tr>
            <tr class="c0">
              <td>Today's Plan Costs</td>
              <td class="__num" v-html="formatNumber('Total', 'group', 'current')"></td>
              <td class="__num" v-html="formatNumber('Total', 'participant', 'current')"></td>
              <td class="__num __t" v-html="combineTotals('current')"></td>
              <td class="__num" v-html="combineTotals('annual_current')"></td>
            </tr>
            <tr class="c1">
              <td>Estimated New Plan Costs</td>
              <td class="__num" v-html="formatNumber('Total', 'group', 'proposed')"></td>
              <td class="__num" v-html="formatNumber('Total', 'participant', 'proposed')"></td>
              <td class="__num __t" v-html="combineTotals('proposed')"></td>
              <td class="__num" v-html="combineTotals('annual_proposed')"></td>
            </tr>
            <tr class="bg-p0">
              <td>Plan Cost Savings</td>
              <td class="__num" v-html="combineTotals('group_other')"></td>
              <td class="__num" v-html="combineTotals('participant_other')"></td>
              <td class="__num __t" v-html="combineTotals('other')"></td>
              <td class="__num" v-html="combineTotals('annual_other')"></td>
            </tr>
            <tr class="bg-p1">
              <template v-if="!form.existingPlan">
                <td>Tax Savings</td>
                <td class="__num" v-html="formatNumber('Taxes', 'group', 'savings')"></td>
                <td class="__num" v-html="formatNumber('Taxes', 'participant', 'savings')"></td>
                <td class="__num __t" v-html="combineTotals('tax')"></td>
                <td class="__num" v-html="combineTotals('annual_tax')"></td>
              </template>
            </tr>
            <tr class="text-weight-bold s2">
              <template v-if="!form.existingPlan">
                <td>Total Savings</td>
                <td class="__num" v-html="formatNumber('Total', 'group', 'savings')"></td>
                <td class="__num" v-html="formatNumber('Total', 'participant', 'savings')"></td>
                <td class="__num __t" v-html="combineTotals('savings')"></td>
                <td class="__num" v-html="combineTotals('annual_savings')"></td>
              </template>
            </tr>
            <tr>
              <td>CommonCare Add Ons</td>
              <td class="__num">{{ combineTotals('groupCcTotal') }}</td>
              <td class="__num">{{ combineTotals('participantCcTotal') }}</td>
              <td class="__num">{{ combineTotals('ccTotal') }}</td>
              <td class="__num">{{ combineTotals('annual_ccTotal') }}</td>
            </tr>
          </table>
        </div>


      </div>


    </div>

    <div class="row justify-center">
      <div class="_xsent q-py-lg q-px-md">

        <div class="text-weight-bold font-1-1-4r main-font">This is a good estimate - let's drill it down IRL.</div>
        <email-field v-model:valid="emailValid" hide-bottom-space v-model="form.email"
                     placeholder="Email"></email-field>
        <phone-input :input-attrs="{ inputClass: 'font-1r' }" v-model="form.phone"></phone-input>
        <q-slide-transition>
          <div class="_fw" v-if="emailValid || form.phone?.valid">
            <q-input label="Your Name (Optional)" v-model="form.name"></q-input>
            <q-input label="Company Name (Optional)" v-model="form.orgName"></q-input>
          </div>
        </q-slide-transition>
        <div class="row q-py-md justify-end">
          <q-btn @click="save" label="Reach Out" glossy color="black" push no-caps class="text-weight-bold"></q-btn>
        </div>
      </div>

      <div class="font-3-4r q-pa-sm">
        <div>*Average spend is healthcare costs and premiums not covered by another group plan. If you have a lot of
          participants with families - this can go up.
        </div>
        <div class="q-pt-xs">**CommonCare plans can, at your option, be administered by a broker, TPA, or combination of
          help. Our only requirement is transparency in fee for value provided.
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import EmailField from 'src/components/common/input/EmailField.vue';
  import MoneyInput from 'src/components/common/input/MoneyInput.vue';
  import MetricSlider from 'src/components/utils/MetricSlider.vue';

  import {computed, onMounted, ref} from 'vue';
  import {dollarString} from 'src/utils/global-methods';
  import icon from 'src/assets/commoncare_icon.svg';
  import {prods, keys} from '../products'
  import {_get} from 'symbol-syntax-utils'
  import PhoneInput from 'src/components/common/phone/PhoneInput.vue';
  import {HForm} from 'src/utils/hForm';
  import {useDocRequests} from 'src/stores/doc-requests';
  import {LocalStorage} from 'symbol-auth-client';

  const store = useDocRequests();
  // import {qs} from '../products'

  const inputs = ref({
    dense: true,
    class: 'alt-font',
    hideBottomSpace: true
  });
  const props = defineProps({
    modelValue: Object,
    split: Boolean,
    colStyle: [String,Object]
  })
  const emit = defineEmits(['update:model-value']);


  const afterFn = (val) => {
    emit('update:model-value', val);
  };

  const { form, save } = HForm({
    store,
    afterFn,
    value: computed(() => props.modelValue),
    validate: true,
    vOpts: computed(() => {
      return {
        'email': { name: 'Email', v: form.value.phone?.valid ? [] : ['email'] },
        'phone': { name: 'Phone', v: form.value.email ? [] : ['phone'] }
      }
    })
  })

  const sliders = ref({
    min: 0
  });

  const emailValid = ref(false);

  const togglePlan = (prod) => {
    const idx = (form.value.products || []).indexOf(prod);
    if (idx > -1) form.value.products.splice(idx, 1);
    else form.value.products.push(prod);
  };

  const toggleCurrent = (val) => {
    if (val) {
      if (!form.value.currentTPA) form.value.currentTPA = 35;
      if (!form.value.currentCommission) form.value.currentCommission = 5
      if (!form.value.currentGroupContribution) form.value.currentGroupContribution = 200;
      if (!form.value.estGroupContribution) form.value.estGroupContribution = 200;
      if (!form.value.currentParticipantContribution) form.value.currentParticipantContribution = 200;
      if (!form.value.estParticipantContribution) form.value.estParticipantContribution = 200;
    }
    form.value.existingPlan = val;
  }

  const discount = computed(() => {
    const cashDiscount = form.value.commonPay ? .8 : 1;
    const rbpDiscount = form.value.rbp ? .8 : 1;
    const medDiscount = form.value.mediation ? .8 : 1;
    const sum = Number((cashDiscount + rbpDiscount + medDiscount).toFixed(1));

    if (sum === 2.4) return .6
    else if (sum === 2.6) return .7
    else if (sum === 2.8) return .8
    else return 1
  })

  const tableData = computed(() => {
    const fte = form.value.fteCount || 1;

    const obj = {
      'Contributions': {
        group: {
          current: form.value.existingPlan ? fte * (form.value.currentGroupContribution || 0) : 0,
          proposed: fte * (form.value.estGroupContribution || 0)
        },
        participant: {
          current: form.value.existingPlan ? fte * form.value.currentParticipantContribution : 0,
          proposed: fte * form.value.estParticipantContribution
        }
      },
      'Healthcare Spend': {
        participant: {
          current: fte * (form.value.avgSpend - form.value.currentGroupContribution - form.value.currentParticipantContribution),
          proposed: fte * ((form.value.avgSpend * discount.value) - form.value.estGroupContribution - form.value.estParticipantContribution)
        }
      },
      'TPA & Commissions': {
        group: {
          current: form.value.existingPlan ? fte * (form.value.currentTPA + ((form.value.currentCommission / 100) * (form.value.currentGroupContribution + form.value.currentParticipantContribution))) : 0,
          proposed: fte * (form.value.estTPA + form.value.estCommission)
        }
      },
      'Taxes': {
        message: form.value.existingPlan ? 'Tax savings when changing plans is complex. We\'d need to ask more questions than you\'d probably want to input in this quick form to get a fair answer' : false,
        group: {
          savings: form.value.existingPlan ? 0 : (fte * (form.value.avgSpend)) * .0979
        },
        participant: {
          savings: form.value.existingPlan ? 0 : (fte * (form.value.avgSpend)) * .2542
        }
      },
      'Total': {
        group: {
          current: 0,
          proposed: 0,
          savings: 0
        },
        participant: {
          current: 0,
          proposed: 0,
          savings: 0
        }
      }
    }

    Object.keys(obj).filter(a => a !== 'Total').forEach(k => {
      ['group', 'participant'].forEach(t => {
        if (obj[k][t]) {
          if (!obj[k][t].savings && obj[k][t].savings !== 0) {
            obj[k][t].savings = obj[k][t].current - obj[k][t].proposed
          }
          obj.Total[t].current += obj[k][t].current || 0;
          obj.Total[t].proposed += obj[k][t].proposed || 0;
          obj.Total[t].savings += obj[k][t].savings || 0;
        }
      })
    })
    return obj;
  });

  const addOns = computed(() => {
    const fte = form.value.fteCount || 0;
    return {
      'Intelligent Admin': {
        key: 'admin',
        priceDisplay: 'Included',
        priceCaption: '',
        true: 'Yes, please',
        false: 'No, I\'ll handle it'
      },
      'CommonPay Cash Network': {
        key: 'commonPay',
        priceDisplay: 'Included',
        priceCaption: '',
        // priceDisplay: dollarString(prods.value.commonPay.pricing.amt(fte), '$', 2),
        // priceCaption: prods.value.commonPay.pricing.caption,
        true: 'Yes, please',
        false: 'No, slow payments please'
      },
      'Bill Mediation': {
        key: 'mediation',
        // priceDisplay: 'Included',
        // priceCaption: '',
        priceDisplay: dollarString(prods.value.mediation.pricing.amt(fte), '$', 2),
        priceCaption: prods.value.mediation.pricing.caption,
        true: 'Yes, please',
        false: 'No, we like our bills'
      },
      'Reference Based Pricing': {
        key: 'rbp',
        priceDisplay: 'Included',
        priceCaption: '',
        // priceDisplay: dollarString(prods.value.rbp.pricing.amt(fte), '$', 2),
        // priceCaption: prods.value.rbp.pricing.caption,
        true: 'Yes, please',
        false: 'No, uncap our fees'
      }
    }
  })

  const formatNumber = (key, payer, row) => {
    const v = tableData.value[key][payer];
    if (!v || !v[row]) return '-';
    else if (v[row] > 0) return `<div>${dollarString(v[row], '$', 0)}</div>`
    else return `<div class="text-red">(${dollarString(v[row], '$', 0)})</div>`
  };

  const totalsData = computed(() => {
    let cc = 0;
    ['admin', 'rbp', 'mediation', 'commonPay'].forEach(k => {
      if (form.value[k]) cc += prods.value[k].pricing.amt(form.value.fteCount);
    })
    if (form.value.products) (form.value.products || []).forEach(p => {
      cc += prods.value[p].pricing.amt(form.value.fteCount);
    })
    const ccTotal = cc * form.value.fteCount;
    const groupCc = (1 - (form.value.payerSplit / 100)) * cc;
    const participantCc = (form.value.payerSplit / 100) * cc;
    const obj = {
      group_other: _get(tableData.value, ['Contributions', 'group', 'savings'], 0) + _get(tableData.value, ['TPA & Commissions', 'group', 'savings'], 0),
      participant_other: _get(tableData.value, ['Contributions', 'participant', 'savings']) + _get(tableData.value, ['Healthcare Spend', 'participant', 'savings'], 0),
      current: _get(tableData.value, ['Total', 'group', 'current'], 0) + _get(tableData.value, ['Total', 'participant', 'current'], 0),
      proposed: _get(tableData.value, ['Total', 'group', 'proposed'], 0) + _get(tableData.value, ['Total', 'participant', 'proposed'], 0),
      savings: _get(tableData.value, ['Total', 'group', 'savings'], 0) + _get(tableData.value, ['Total', 'participant', 'savings'], 0),
      cc,
      ccTotal,
      groupCc,
      participantCc,
      participantCcTotal: participantCc * form.value.fteCount,
      groupCcTotal: groupCc * form.value.fteCount,
    };
    const taxOnly = _get(tableData.value, ['Taxes', 'group', 'savings'], 0) + _get(tableData.value, ['Taxes', 'participant', 'savings'], 0);
    obj['tax'] = taxOnly;
    obj['other'] = obj['savings'] - taxOnly;
    Object.keys(obj).forEach(k => obj[`annual_${k}`] = obj[k] * 12);
    return obj;
  });
  const combineTotals = (row) => {
    return dollarString(totalsData.value[row], '$', 0);
  }

  const emitModelValue = () => {
    emit('update:model-value', form.value)
    // LocalStorage.setItem('docRequest', form.value)
  }

  onMounted(() => {
    setTimeout(() => {
      const el = document.getElementById('SavingsCalculator');
      if(el) {
        el.addEventListener('keyup', () => {
          emitModelValue()
        })
        el.addEventListener('click', () => {
          emitModelValue()
        })
        el.addEventListener('touch', () => {
          emitModelValue()
        })
      }
    }, 100)

  })


</script>

<style lang="scss" scoped>
  .__rr {

  }

  h5 {
    font-size: 1.1rem;
    font-weight: 700;
    line-height: .7;
  }

  .h5 {
    font-size: 1.1rem;
    font-weight: 700;
    padding: 10px 0;
  }


  .__sub {
    background: #eeeeee;
    font-weight: bold;
  }

  .__num {
    text-align: right;
  }

  .__s {
    font-weight: bold;
    //color: var(--q-primary);
  }

  .__p {
    font-weight: bold;
    color: var(--q-primary);
  }

  .__t {
    border-left: solid 1px var(--q-primary);
  }

  .__tbw {
    width: 100%;
    overflow-x: scroll;
    justify-content: center;
  }

  .__caption {
    font-size: .85rem;
    font-weight: 600;
    color: #9e9e9e;
  }

  td {
    padding: 3px 5px;
  }

  .__fee {
    transform: translate(0, -15px);
  }

  .c0 {
    background: white
  }

  .c1 {
    background: white
  }


  .s2 {
    background: var(--q-primary);
    color: white
  }

  @media screen and (max-width: 1023px) {
    .__tbw {
      justify-content: flex-start;
    }
  }

</style>
