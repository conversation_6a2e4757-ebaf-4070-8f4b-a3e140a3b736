<template>
  <q-chip v-bind="{color: 'transparent', ...$attrs}">
    <q-icon v-bind="cats[modelValue]?.icon || {}"></q-icon>
    <span class="q-ml-sm">{{cats[modelValue]?.label || ''}}</span>
  </q-chip>
</template>

<script setup>

  import {computed} from 'vue';

  const props = defineProps({
    modelValue: String
  })

  const cats = computed(() => {
    return {
      'complaint': {
        label: 'Complaint',
        icon: {
          name: 'mdi-alert',
          color: 'red'
        }
      },
      'content': {
        label: 'Bugs/Content',
        icon: {
          name: 'mdi-chat-alert',
          color: 'light-blue'
        }
      }
    }
  })
</script>

<style lang="scss" scoped>

</style>
