<template>
  <q-chip v-bind="{label: multiple ? (modelValue?.length || 0) + ' Selected' : $capitalizeFirstLetter(modelValue || 'Select Status'), iconRight: editing ? 'mdi-menu-down' : undefined, ...$attrs}">
    <q-menu v-if="editing">
      <div class="w300 mw100 q-pa-md bg-white">
        <q-list separator>
          <q-item v-for="channel in channels" :key="`st-${channel}`" clickable @click="emitUp(channel)">
            <q-item-section>
              <q-item-label>{{$capitalizeFirstLetter(channel)}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-menu>
  </q-chip>
</template>

<script setup>

  import {ref} from 'vue';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    editing: Boolean,
    modelValue: String,
    multiple: Boolean
  })

  const channels = ref(['app', 'phone', 'email', 'mail'])

  const emitUp = (val) => {
    if(props.multiple){
      const list = [...props.modelValue || []];
      const idx = list.indexOf(val);
      if(idx > -1) list.splice(idx, 1);
      else list.push(val);
      emit('update:model-value', list);
    } else emit('update:model-value', val);
  }
</script>

<style lang="scss" scoped>

</style>
