<template>
  <q-page>
    <div class="row justify-center pd12">
      <div class="_cent">
        <div class="row">
          <div class="col-12 col-md-6 q-pa-sm">
            <div class="_fw q-pa-sm q-mb-md">
              <div class="text-xxl text-weight-bold">You can't shoot for healthcare reform and not look at research
              </div>
              <div class="text-md">After all, medicine is about science. Our current system provides pretty serious
                challenge to evolving science.
              </div>
            </div>
            <div class="__here">Here are some challenges to medical research today</div>
            <div class="q-py-md text-md __bx">
              <div class="__c" v-for="(reason, i) in reasons" :key="`reason-${i}`">
                <div class="alt-font __n flex flex-center">
                  <div>{{ i + 1 }}</div>
                </div>
                <div class="text-weight-bold font-1-1-4r text-p9">{{ reason }}</div>
              </div>
            </div>
          </div>
          <div class="col-12 col-md-6 q-pa-md">

            <div class="__form">
              <div class="__fc">
                <div class="q-pa-sm">
                  <div class="font-1-1-2r text-weight-bolder alt-font">
                    This is a new frontier - and we're new at it.
                  </div>
                  <div class="font-1r alt-font">We have some really great tools for getting smart contracts together for
                    funding and sharing ideas. We have a really great network to bring ideas to. We have invested
                    company funds in research and plan to continue doing so. If you have research to fund, let's see
                    what we can build for you.
                  </div>
                </div>
                <contact-base></contact-base>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
    <div class="row justify-center">
      <div class="_cent">
        <div class="row justify-center">
          <div class="_xsent q-pa-md">
            <div class="text-center text-xxl text-weight-bold">Well, why is that in our lane - and what can we do about
              it?
            </div>
            <div class="text-md text-center">If you're going to build a new experience in healthcare, you have to go do
              the root drivers - to the science.
            </div>
          </div>
        </div>

        <div class="row pd5 q-mb-xl">
          <div class="col-12 col-md-6 q-pa-md">
            <div class="text-lg text-weight-bolder q-py-xs text-p9">Our model is about measuring the actuarial
              experience of healthcare.
            </div>
            <div class="text-sm text-weight-medium">We can stop at focusing on the costs, but that seems like a tragic
              waste. We want to actually start at the quality layer, the scientific layer. We believe costs flow from
              there.
            </div>
            <div class="q-my-lg __here row justify-center items-center">
              <div>
                Here's how we are doing it
              </div>
              <q-icon class="q-ml-md" :name="$q.screen.lt.md ? 'mdi-chevron-down' : 'mdi-chevron-right'"></q-icon>
            </div>
          </div>
          <div class="col-12 col-md-6 q-pa-md">
            <div class="row justify-center">
              <div class="col-12">
                <div class="__item q-my-sm" v-for="(item, i) in hows" :key="`item-${i}`">
                  <div class="row no-wrap">
                    <div class="col-shrink">
                      <div class="row items-center">
                        <div class="__dot"></div>
                        <div class="__letter text-md text-weight-bold">U</div>
                      </div>
                    </div>
                    <div class="col-11 col-md-9">
                      <div class="text-md text-weight-bold">{{ item.label }}
                      </div>
                      <div class="text-xs">{{ item.text }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </q-page>
</template>

<script setup>

  import {ref} from 'vue';
  import ContactBase from 'src/components/common/contact/ContactBase.vue';

  const reasons = ref([
    'Conducting medical research is unbelievably expensive',
    'Conducting research that leads to usable treatments is tremendously more expensive',
    'There are few organizations with the money and motives to fund such research',
    'Where there are strong motives and money spent, research isn\'t even selected for funding unless it proves it serves an interest'
  ]);

  const hows = ref([
    {
      label: 'Uncover Inefficiencies',
      text: 'Actuarial claims data actually provides sufficient material for producing research all by itself.'
    },
    {
      label: 'Democratize Funding',
      text: 'Research is actually expensive to conduct. However, there are many people who are interested in getting involved - but there\'s no relevant path for that currently'
    },
    {
      label: 'Implement Change',
      text: 'Unfettered by big medicine, working directly with a community of providers and the end patient - practitioners can explore and implement best practices with no friction.'
    }
  ])

</script>

<style lang="scss" scoped>

  .__bx {
    border-radius: 20px;
    padding: 15px;
    //border: solid 4px var(--q-p2);
  }

  .__dot {
    height: 20px;
    width: 20px;
    background: linear-gradient(12deg, var(--q-primary), var(--q-p9));
    border-radius: 50%;
  }

  .__letter {
    color: transparent;
    z-index: -1;
  }

  .__form {
    border-radius: 15px;
    background: linear-gradient(-192deg, var(--q-primary), var(--q-p2));
    padding: 10px;

    .__fc {
      padding: 50px 2vw;
      border-radius: inherit;
      box-shadow: 0 0 20px -8px rgba(0, 0, 0, .6);
      background: white;
    }
  }

  .__here {
    padding: 15px 20px;
    border-radius: 8px;
    background: linear-gradient(-12deg, var(--q-p9), var(--q-primary));
    color: white;
    font-size: 1.25rem;
    text-align: center;
    font-weight: 700;
  }

  .__c {
    border-radius: 8px;
    background: white;
    //box-shadow: 0 10px 20px -10px rgba(0,0,0,.6);
    padding: 10px 5px;
    margin-top: 20px;
    position: relative;

    .__n {
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 50%;
      height: 120px;
      width: 120px;
      opacity: .3;
      border: solid 2px var(--q-primary);
      color: var(--q-primary);
      font-size: 3rem;
      font-weight: 900;
    }
  }
</style>
