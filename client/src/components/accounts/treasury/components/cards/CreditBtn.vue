<template>
  <q-btn v-bind="{class: '_bub', ...$attrs}" @click="dialog = !dialog">
    <slot name="default"></slot>
    <common-dialog v-model="dialog" setting="smmd">
      <div class="_fw bg-white q-pa-md">
        <q-tab-panels v-model="tab" class="_panel" animated>
          <q-tab-panel class="_panel" name="account">
            <div class="__title">Bank account to pull from</div>
            <bank-account-list no-edit :org="org" @update:model-value="setAccount">
              <template v-slot:side="scope">
                <q-btn flat v-if="org?.bankAccounts[scope.acct._id]?.default" color="accent" size="sm"
                       label="Default"></q-btn>
                <span v-else></span>
              </template>
            </bank-account-list>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="amount">
            <q-tab-panels class="_panel" :model-value="confirmed" animated>
              <q-tab-panel class="_panel" :name="false">
                <div class="__title">Amount to add</div>
                <div class="__g">
                  <div class="__l">From</div>
                  <div class="q-px-xs">
                    <account-item dense :model-value="account">
                      <template v-slot:side>
                        <q-btn flat size="sm" color="red" icon="mdi-close" @click="setAccount()"></q-btn>
                      </template>
                    </account-item>
                  </div>
                  <div class="__l">Amount</div>
                  <div>
                    <money-input :decimal="2" prefix="$" v-model="form.amount"></money-input>
                  </div>
                  <div class="__l">Memo</div>
                  <div class="q-px-sm">
                    <q-input autogrow v-model="form.description"></q-input>
                  </div>
                </div>
                <div class="q-pa-md row justify-end">
                  <q-btn push  class="bg-accent text-white tw-six" no-caps icon-right="mdi-cash" label="Add Funds"
                         @click="sendTransfer"></q-btn>
                </div>
              </q-tab-panel>
              <q-tab-panel class="_panel" :name="true">
                <div class="tw-six font-1r text-ir-mid q-pa-sm">Confirm Transaction</div>
                <div class="tw-five font-7-8r q-pa-sm">Send <span class="tw-six">{{dollarString(form.amount, '$', 2)}}</span> from your <span class="tw-six">{{account.bankName || 'bank'}} account ending ...{{account.last4}}</span> to your Plan Wallet Balance?</div>
                <div class="q-pt-lg row justify-end">

                  <q-btn v-if="loading || !transferOptions.destinationOptions?.length || !transferOptions.sourceOptions?.length" push class="bg-primary tw-six text-white" no-caps disable>
                    <span class="q-mr-sm">Loading</span>
                    <q-spinner color="white"></q-spinner>
                  </q-btn>
                  <q-btn v-else push class="bg-primary tw-six text-white" no-caps @click="sendTransfer">
                    <span class="q-mr-sm">Yes</span>
                    <q-icon name="mdi-check-circle"></q-icon>
                  </q-btn>
                </div>
              </q-tab-panel>
            </q-tab-panels>

          </q-tab-panel>
        </q-tab-panels>

      </div>
    </common-dialog>
  </q-btn>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import MoneyInput from 'components/common/input/MoneyInput.vue';
  import BankAccountList from 'components/accounts/lists/BankAccountList.vue';
  import AccountItem from 'components/accounts/cards/AccountItem.vue';

  import {computed, ref} from 'vue';
  import {idGet} from 'src/utils/id-get';
  import {useBankAccounts} from 'stores/bank-accounts';
  import {useBanking} from 'stores/banking';
  import {$errNotify, dollarString} from 'src/utils/global-methods';

  const accountStore = useBankAccounts();
  const bankStore = useBanking();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    moov_account: { required: true },
    care_account: { required: true },
    org: { required: true }
  })

  const accountSet = ref(undefined);

  const formFn = (defs) => {
    return { amount: 0, currency: 'usd', description: '', ...defs }
  }
  const form = ref(formFn());
  const linkedAccount = ref({})
  const { item: account } = idGet({
    store: accountStore,
    value: computed(() => accountSet.value)
  })

  const tab = ref('account');
  const dialog = ref(false);

  const checkPm = async (tries = 0) => {
    if (account.value) {

      if(account.value.moov_link_id){

        linkedAccount.value = await bankStore.get(props.moov_account?.accountID, { banking: { moov: { method: 'get_external_account', args: [account.value.moov_link_id]}}})
      }
      else if(tries < 10) setTimeout(() => checkPm(tries + 1), 500)
      else $errNotify('This account is not verified for transfers')
    } else if(tries < 10) setTimeout(() => checkPm(tries + 1), 500)
  }
  const setAccount = (val) => {
    accountSet.value = val;
    if (val) {
      tab.value = 'amount';
      setTimeout(() => {
        checkPm(0)
      }, 250)
    } else {
      accountSet.value = undefined;
      tab.value = 'account'
    }
  }

  const loading = ref(false);
  const confirmed = ref(false);
  const transferOptions = ref({ destinationOptions: [], sourceOptions: []})
  const sendTransfer = async () => {
    const accountID = props.moov_account.accountID
    const amount = { currency: form.value.currency?.toUpperCase() || 'UST', value: form.value.amount * 100 }
    if(!confirmed.value) {
      transferOptions.value = await bankStore.get(accountID, { banking: { moov: { method: 'transfer_options', args: [{ amount, source: { accountID }, destination: { accountID }}] } } })
          .catch(err => {
            console.log(`Error getting transfer options: ${err.message}`);
            return transferOptions.value;
          })
      return confirmed.value = true;
    }
    loading.value = true
    const transfer = {
      destination: { paymentMethodID: transferOptions.value.destinationOptions.filter(a => !!a.wallet)[0].paymentMethodID },
      source: { paymentMethodID: transferOptions.value.sourceOptions.filter(a => !!a.bankAccount && a.bankAccount.bankAccountID === account.value.moov_link_id)[0].paymentMethodID },
      amount,
      // description: form.value.description
    }
   const t = await bankStore.get(props.moov_account.accountID, { banking: { moov: { method: 'create_transfer', args: [transfer] }} })
        .catch(err => {
          console.log(`Error creating transfer: ${err.message}`)
          $errNotify(err.message);
        })
    loading.value = false;
    if(t?.transferID) {
      emit('update:model-value', t.transferID);
      dialog.value = false;
    }
  }

</script>

<style lang="scss" scoped>
  .__title {
    padding: 10px;
    font-weight: bold;
    color: #999;
    font-size: .9rem;
  }

  .__g {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
    justify-content: center;

    div {
      padding: 5px 10px;
    }
  }

  .__l {
    font-weight: 600;
    font-size: .7rem;
    color: #999;
    border-right: solid .5px #999;
  }
</style>
