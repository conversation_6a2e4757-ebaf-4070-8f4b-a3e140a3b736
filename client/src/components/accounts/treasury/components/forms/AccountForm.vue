<template>
  <div class="_fw relative-position q-py-md pw2">

    <div class="q-px-md" v-if="!account?.accountID">
      You need to set up your Plan Wallet first.
    </div>

    <div class="row">
      <div class="col-12 q-pa-lg" v-if="account?.accountID && !account.termsOfService?.acceptedDate">
        <tos-accept v-model:manual-tos="manualTos" v-model:tos-token="tosToken" @update:account="emit('update:model-value', $event)" :account="account"></tos-accept>
      </div>
      <div class="col-12 col-md-6 q-pa-sm">
        <div class="_f_g" v-if="account?.accountID">
          <div class="_f_l _a_chip">Company Info</div>
          <div class="_form_grid _f_g_r">
            <div class="_form_label">Legal Name</div>
            <div class="q-pa-sm">
              <q-input
                  :error="isM('legalBusinessName')"
                  no-error-icon
                  :model-value="account.profile.business.legalBusinessName"
                  @update:model-value="update($event, 'legalBusinessName', 'legalName')"
              ></q-input>
            </div>
            <div class="_form_label">Email</div>
            <div class="q-pa-sm">
              <email-field
                  :error="isM('email')"
                  no-error-icon
                  hide-bottom-space
                  :icon="false"
                  :model-value="account?.profile.business.email"
                  @update:modelValue="update($event, 'email', 'email')"
              ></email-field>
            </div>

            <div class="_form_label">Tax ID</div>
            <div class="q-pa-sm">
              <ssn-input
                  ein
                  no-error-icon
                  :error="isM('taxIDProvided')"
                  :model-value="fullOrg?.ein"
                  @update:model-value="updateTaxId"
              ></ssn-input>
            </div>
            <div class="_form_label">Merchant Category Code (MCC)</div>
            <div class="q-pa-sm">
              <mcc-form
                  :input-attrs="{
                 error: isM('industryCodes.mcc'),
                 noErrorIcon: true
                }"
                  :model-value="account.profile.business.industryCodes?.mcc"
                  @update:model-value="update($event, 'industryCodes.mcc', 'mcc')"
                  @update:sic="update($event, 'industryCodes.sic', 'sic')"
                  @update:naics="update($event, 'industryCodes.naics', 'naics')"
              ></mcc-form>
            </div>
            <div class="_form_label">Business Website</div>
            <div class="q-pa-sm">
              <q-input
                  :error="isM('website')"
                  no-error-icon
                  label="Website URL - 'none' if you don't have one"
                  :model-value="account.profile.business.website"
                  @update:model-value="update($event, 'website', 'treasury.business_profile.website')"
              ></q-input>
            </div>
          </div>

        </div>
      </div>
      <div class="col-12 col-md-6 q-pa-sm">

        <div class="_f_g" v-if="account?.accountID">

          <div class="_f_l _a_chip">Account Features</div>
          <feature-form :moov-acct="account"></feature-form>

          <div class="_f_l _a_chip">Owners & Representatives</div>
          <div class="q-pa-sm">
            <account-owners
                v-if="peopleLoaded"
                @update:account="updateAccount"
                :account="account"
                :org="fullOrg"
                :account-people="accountPeople"
            ></account-owners>
          </div>

        </div>
      </div>

    </div>

  </div>
</template>

<script setup>
  import EmailField from 'components/common/input/EmailField.vue';
  import MccForm from 'components/accounts/treasury/components/forms/MccForm.vue';
  import AccountOwners from 'components/accounts/treasury/components/cards/AccountOwners.vue';
  import SsnInput from 'components/common/input/SsnInput.vue';
  import FeatureForm from 'components/accounts/treasury/components/forms/FeatureForm.vue';
  import TosAccept from 'components/care-accounts/utils/TosAccept.vue';

  import {computed, ref, watch} from 'vue';
  import {_get, _set} from 'symbol-syntax-utils';
  import {useBanking} from 'stores/banking';
  import {$errNotify} from 'src/utils/global-methods';
  import {useOrgs} from 'stores/orgs';
  import {getDiff} from '../../utils/stripe-utils';
  import {runMoovAccountMap} from 'components/accounts/treasury/utils/moov-utils';
  import {useRouter} from 'vue-router';

  const router = useRouter()

  const bankStore = useBanking();
  const orgStore = useOrgs();

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    modelValue: { required: true },
    org: { required: true }
  })

  const tosToken = ref()
  const manualTos = ref()
  const fullOrg = computed(() => props.org);

  const loading = ref(false);
  const accountChanges = ref({})
  const account = computed(() => {
    return {
      ...props.modelValue,
      profile: { business: { ...props.modelValue?.profile?.business, ...accountChanges.value } }
    };
  })
  const accountPeople = ref({ data: [] })
  const peopleLoaded = ref(false);


  const dirty = ref(false);
  const orgChanges = ref({ $set: {} })
  const saveTo = ref();
  const saveAll = async () => {
    loading.value = true;
    if (Object.keys(orgChanges.value.$set || {}).length) await orgStore.patch(fullOrg.value._id, orgChanges.value)
    const id = account.value.accountID;
    if (id && Object.keys(accountChanges.value).length) {
      const acct = await bankStore.get(id, {
        banking: {
          moov: {
            method: 'account_update',
            args: [{ profile: { business: accountChanges.value } }]
          }
        }
      })
          .catch(err => {
            // $errNotify(`Error saving account: ${err.message}`);
            console.error(err.message);
          })
      if (acct) emit('update:model-value', acct);
      orgChanges.value = { $set: {} }
      accountChanges.value = {}
    }
    loading.value = false;

  }
  const maybeSave = () => {
    if (saveTo.value) clearTimeout(saveTo.value);
    saveTo.value = setTimeout(() => {
      saveAll()
    }, 3000)
  }

  const update = (val, accountPath, orgPath, orgVal) => {
    accountChanges.value = _set(accountChanges.value, accountPath, val);
    if (orgPath) orgChanges.value.$set[orgPath] = orgVal || val
    dirty.value = true;
    maybeSave()
  }

  const updateTaxId = (val) => {
    update(val.split('-').join(''), 'taxID.ein.number', 'ein', val)
  }

  const isM = (path) => {
    const v = _get(account.value, `profile.business.${path}`)
    return !v && v !== 0;
  }

  const to = ref(false);
  const loadPersons = async (acct) => {
    if (!to.value) {
      peopleLoaded.value = false;
      to.value = true;
      setTimeout(() => to.value = false, 3000);
    }
    accountPeople.value = await bankStore.get(acct.accountID, {
      banking: {
        stripe: {
          method: 'list_persons',
          args: [acct.accountID]
        }
      }
    })
        .catch(err => {
          console.error(`Error loading account people: ${err.message}`)
        });
    peopleLoaded.value = true
  }

  const updateAccount = (val) => {
    emit('update:model-value', val);
    loadPersons(val)
    // setTimeout(() => {
    //   peopleLoaded.value = true;
    // }, 250)
  }

  const syncOrg = (tries = 0) => {
    if (fullOrg.value?._id) {
      const acct = runMoovAccountMap({ org: fullOrg.value, account: account.value });
      const diff = getDiff(acct?.profile?.business, account.value.profile.business);
      if (Object.keys(diff).length) {
        for (const k in diff) {
          accountChanges.value = _set(accountChanges.value, k, _get(acct, k))
        }
        saveAll()
      }
    } else if (tries < 5) {
      setTimeout(() => syncOrg(tries + 1), 1000);
    }
  }
  watch(account, async (nv, ov) => {
    if (nv?.accountID && nv.accountID !== ov?.accountID) {
      loadPersons(nv);
      syncOrg(0);
      if (nv.profile.business.ownersProvided && nv.verification.status !== 'verified') {
        console.log('run verify')
        const acct = await bankStore.get(nv.accountID, {
          banking: {
            moov: {
              method: 'add_capabilities',
              args: [nv.accountID, ['wallet']]
            }
          }
        })
            .catch(err => console.error(`Error adding capabilities: ${err.message}`))
        if (acct) router.go()
      }
    }
  }, { immediate: true })

  watch(fullOrg, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      setTimeout(() => {
        const mcc = nv.mcc;
        if (account.value && account.value.verification?.status !== 'verified' && mcc && mcc !== account.value?.industryCodes?.mcc) {
          update(mcc, 'industryCodes.mcc')
          update(nv.naics, 'industryCodes.naics')
          update(nv.sic, 'industryCodes.sic')
        }
      }, 3000)
    }
  }, { immediate: true })


</script>

<style lang="scss" scoped>
  .__reqs {
    border-radius: 10px;
    padding: 30px 15px;
    background: #efefef;
    color: #222;
  }
</style>
