export const accountTypes = {
    'company': { label: 'Company' },
    'government_entity':  { label: 'Government Entity' },
    'non_profits':  { label: 'Non Profit' },
    'individual': { label: 'Individual' },
}

export const faName = (fa:any) => {
    if(!fa) return '';
    const { name } = fa || {}
    return `${name || 'Untitled'}`
}

export const capabilities_list = ['transfers', 'send-funds', 'collect-funds', 'wallet', 'card_issuing']



export const walletBalance = (wallet:any) => wallet?.availableBalance?.value
