// Map CommonCare org structures to Moov org structures
//  see https://docs.moov.io/node/accounts/#businessprofile
import {_get} from 'symbol-syntax-utils';

export const structureMap = (org: any): any => {
    // 'trust', 'unincorporatedNonProfit',
    const obj: any = {
        'PARTNERSHIP': 'partnership',
        'SOLE_PROPRIETOR': 'soleProprietorship',
        'LLC': 'llc',
        'NONPROFIT': 'incorporatedNonProfit', // NOTE: 'unincorporatedNonProfit' is also available
        'CORPORATION': org.public ? 'publicCorporation' : 'privateCorporation',
        'S-CORP': 'privateCorporation',
        'LLP': 'partnership',
        'OTHER': 'unincorporatedAssociation'
    }
    return obj[org?.structure]
}

export const moovDob = (dob: string | Date) => {
    const d = new Date(dob);
    return {
        day: d.getDate(),
        month: d.getMonth() + 1,
        year: d.getFullYear()
    }
}

export const addressToMoov = (address: any) => {
    const {city, address1, address2, postal, region} = address || {}
    return {
        addressLine1: address1,
        addressLine2: address2,
        city,                     // 25 char max
        stateOrProvince: region,  // 2 chars
        postalCode: postal,       // 5 chars
        country: 'US',            // 2 chars
    }
}
export const moovAddressFormatted = (val: any) => {
    if (!val) return '';
    const {addressLine1: line1, addressLine2: line2, city, stateOrProvince: state, postal_code} = val
    return `${line1 || ''} ${line2 || ''} ${city || 'Unknown City'}, ${state || 'Unknown State'} ${postal_code || ''}`
}

const moovPhone = (p: any) => {
    if (!p) return undefined;
    if (typeof p === 'string') return {number: p.slice(2), countryCode: p.split('+')[0]?.charAt(0)}
    return {number: p.number?.e164.slice(2), countryCode: String(p.countryCode)}
};


export const getDiff = (obj1: any, obj2: any) => {
    if (!obj2) return {}
    const obj: any = {};
    for (const k in obj1 || {}) {
        if (typeof obj1[k] === 'object') {
            for (const subK in obj1[k]) {
                if (typeof obj1[k][subK] === 'object') {
                    for (const subSubK in obj1[k][subK]) {
                        if (obj1[k][subK][subSubK] !== _get(obj2, `${k}.${subK}.${subSubK}`)) obj[`${k}.${subK}.${subSubK}`] = true
                    }
                } else if (obj1[k][subK] !== _get(obj2, `${k}.${subK}`)) obj[`${k}.${subK}`] = true
            }
        } else if (JSON.stringify(obj1[k]) !== JSON.stringify(obj2[k])) obj[k] = true;
    }
    return obj;
}

const getSsn = (val: string) => {
    return val?.split('-').join('');
}

type MoovPersonMap = {
    [key:string]: (person:any, owner?:any, accountPerson?:any, min?:number) => any
}
export const moovPersonMap:MoovPersonMap = {
    name: (p: any) => {
        return {
            firstName: p.firstName || p.name?.split(' ')[0],
            lastName: p.lastName || p.name?.split(' ')[p.name.split(' ').length - 1]
        }
    },
    'phone': (p: any) => {
        return {
            number: p.phone?.number?.e164.slice(2),
            countryCode: p.phone?.countryCode?.toString()
        }
    },
    'email': (p: any) => p.email,
    'address': (p: any) => p.address ? addressToMoov(p.address) : undefined,
    'birthDate': (p: any, owner: any, ap: any) => {
        if (ap?.birthDate?.year) return ap.birthDate
        return moovDob(p.dob)
    },
    governmentID: (p: any) => {
        const obj: any = {};
        const num = getSsn(p.ssn || p.itin);
        if (p.ssn) {
            obj.ssn = {full: num, last4: num.slice(num.length - 4)}
        }
        if (p.itin) {
            obj.itin = {full: num, last4: num.slice(num.length - 4)}
        }
        return obj;
    },
    responsibilities: (p: any, owner?: any, ap?: any, min?:number) => {
        const {position, percent} = owner || {}
        // console.log('responsibilities', ap)
        return {
            isController: !!ap?.responsibilities?.isController,
            isOwner: !!percent,
            ownershipPercentage: Math.max(min || 0, percent || !!percent ? (min || 1) : 0),
            jobTitle: position || 'owner',
        }
    }
}

type MoovPersonOptions = { person: any, accountPerson?: any, org?: any, minPercent?:number }
export const runMoovPersonMap = ({person, accountPerson, org, minPercent}: MoovPersonOptions) => {
    const returnObj: any = {};
    const owner = org?.owners?.filter((a: any) => a.id === person._id)[0]
    for (const k in moovPersonMap) {
        const val = moovPersonMap[k as keyof typeof moovPersonMap](person, owner, accountPerson, minPercent);
        if(val || !val && !(accountPerson || {})[k]) returnObj[k] = val;
    }
    return returnObj;
}

export const moovAccountMap = {
    'email': (o: any) => o.email,
    'address': (o: any) => addressToMoov(o.address),
    'businessType': (o: any) => structureMap(o),
    'description': (o: any) => o?.treasury?.business_profile?.product_description,
    'doingBusinessAs': (o: any) => o.legalName !== o.name ? o.name : undefined,
    'industryCodes': (o: any) => {
        const {mcc, sic, naics} = o || {};
        if (mcc && sic && naics) return {mcc, sic, naics};
        return undefined
    },
    'legalBusinessName': (o: any) => o.legalName || o.name,
    'phone': (o: any) => moovPhone(o.phone),
    'taxID': (o: any) => {
        if (o.ein?.split('-').join('').length === 9) return {ein: o.ein.split('-').join('')}
        return undefined
    },
    'website': (o: any) => o.treasury?.business_profile?.url
}

type MoovAccountOptions = { org: any, account?: any }
export const runMoovAccountMap = ({org, account}: MoovAccountOptions) => {
    const returnObj: any = {};
    for (const k in moovAccountMap) {
        const val = moovAccountMap[k as keyof typeof moovAccountMap](org);
        if(val || !val && !(account || {})[k]) returnObj[k] = val;
    }
    return { profile: { business: returnObj }};
}

export const orgToCompany = (org: any, account:any) => {
    return runMoovAccountMap({org, account});
}

// const accountPayload = {
//     accountType: "business",
//     profile: {
//         business: {
//             address: {
//                 addressLine1: "123 Main Street",
//                 addressLine2: "Unit 302",
//                 city: "Boulder",
//                 stateOrProvince: "CO",
//                 postalCode: "80301",
//                 country: "US"
//             },
//             businessType: "llc",
//             description: "Local fitness center paying out instructors",
//             doingBusinessAs: "Whole Body Fitness",
//             email: "<EMAIL>",
//             industryCodes: {
//                 naics: "713940",
//                 sic: "7991",
//                 mcc: "7997"
//             },
//             legalBusinessName: "Whole Body Fitness LLC",
//             phone: {
//                 number: "**********",
//                 countryCode: "1"
//             },
//             taxID: {
//                 ein: {
//                     number: "***********"
//                 }
//             },
//             website: "www.wholebodyfitnessgym.com",
//         }
//     },
//     capabilities: [
//         "transfers",
//         "send-funds",
//         "collect-funds",
//         "wallet"
//     ],
//     customerSupport: {
//         address: {
//             addressLine1: "123 Main Street",
//             addressLine2: "Unit 302",
//             city: "Boulder",
//             stateOrProvince: "CO",
//             postalCode: "80301",
//             country: "US"
//         },
//         email: "<EMAIL>",
//         phone: {
//             number: "**********",
//             countryCode: "1"
//         },
//         website: "www.wholebodyfitnessgym.com"
//     },
//     foreignId: "your-correlation-id",
//     metadata: {
//         property1: "string",
//         property2: "string"
//     },
//     mode: "production",
//     settings: {
//         cardPayment: {
//             statementDescriptor: "Whole Body Fitness"
//         }
//     },
//     termsOfService: {
//         token: "kgT1uxoMAk7QKuyJcmQE8nqW_HjpyuXBabiXPi6T83fUQoxsyWYPcYzuHQTqrt7YRp4gCwyDQvb6U5REM9Pgl2EloCe35t-eiMAbUWGo3Kerxme6aqNcKrP_6-v0MTXViOEJ96IBxPFTvMV7EROI2dq3u4e-x4BbGSCedAX-ViAQND6hcreCDXwrO6sHuzh5Xi2IzSqZHxaovnWEboaxuZKRJkA3dsFID6fzitMpm2qrOh4"
//     },
// };
