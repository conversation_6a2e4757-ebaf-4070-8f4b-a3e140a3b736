<template>
  <q-page>
    <div class="row justify-center">
      <div class="_cent pd2 pw1">
        <account-form :org="org" v-model="moov_account"></account-form>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import AccountForm from 'components/accounts/treasury/components/forms/AccountForm.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {useOrgs} from 'stores/orgs';
  import {useBanking} from 'stores/banking';

  const orgStore = useOrgs();
  const bankStore = useBanking();

  const { item:org } = idGet({
    store: orgStore,
    value: computed(() => LocalStorage.getItem('org_id')),
  })

  const accountId = computed(() => org.value?.treasury?.id);

  const moov_account = ref();

  watch(accountId, async (nv, ov) => {
    if(nv && nv !== ov){
      moov_account.value = await bankStore.get(nv, { banking: { moov: { method: 'get_account', args: [] }}})
      if(moov_account.value) SessionStorage.setItem('moov_account', moov_account.value.id)
    }
  }, { immediate: true })


</script>

<style lang="scss" scoped>

</style>
