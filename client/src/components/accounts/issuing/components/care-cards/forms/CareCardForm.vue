<template>
  <div class="_fw">

    <div class="_f_l _f_chip">{{ form?._id ? 'Edit Card' : 'Add Card' }}</div>
    <div class="_form_grid _f_g_r">
      <div class="_form_label">Card Name</div>
      <div class="q-pa-sm">
        <q-input dense filled placeholder="Enter Name..." v-model="form.name"
                 @update:model-value="autoSave('name')"></q-input>
      </div>
      <div class="_form_label">Budget</div>
      <div class="q-pa-md">
        <budget-picker
            disabled
            dense
            filled
            :model-value="form.budget"
        ></budget-picker>
      </div>
      <div class="_form_label">Cardholder</div>
      <div class="q-pa-sm">
        <cardholder-form v-model="form.cardholder" :budget="budget" @update:tos="tosReady"></cardholder-form>
      </div>
      <div class="_form_label">Status</div>
      <div class="q-pa-sm">
        <status-chip :picker="!form.status === 'canceled'" :model-value="form.status" @update:model-value="setStatus"></status-chip>
      </div>
    </div>

    <template v-if="form?._id">
      <div class="_f_l _f_chip">Funds</div>
      <div class="q-py-sm">
        <div class="_form_grid _f_g_r">
          <div class="_form_label">Available Funds</div>
          <div class="q-pa-sm">
            <amount-form
                path="amount"
                :input-attrs="{ filled: true }"
                :id="form._id"
                :store="cardStore"
                :model-value="form.amount"
                :parent-amount="parent_balances.amount"
                :parent-assigned="parent_balances.assigned_amount"
                :spent="spent"
            ></amount-form>
          </div>
          <div class="_form_label">Recurring Funds (monthly)</div>
          <div class="q-pa-sm">
            <amount-form
                path="recurs"
                :input-attrs="{ filled: true }"
                :id="form._id"
                :store="cardStore"
                :model-value="form.recurs"
                :parent-amount="parent_balances.recurs"
                :parent-assigned="parent_balances.assigned_recurs"
            ></amount-form>
          </div>
        </div>
      </div>
      <div class="_f_l _f_chip">Members</div>
      <div class="q-py-sm">
        <div class="_form_grid _f_g_r">
          <div class="_form_label">Managers</div>
          <div class="q-pa-sm">
            <div class="__cap">Managers manage all budget details</div>

            <budget-members
                :store="budgetStore"
                adding
                path="managers"
                :model-value="form"
            ></budget-members>
          </div>
          <div class="_form_label">Approvers</div>
          <div class="q-pa-sm">
            <div class="__cap">Approvers approve budget spend that requires approval</div>
            <budget-members
                :store="budgetStore"
                adding
                path="approvers"
                :model-value="form"
            ></budget-members>
          </div>
          <div class="_form_label">Members</div>
          <div class="q-pa-sm">
            <div class="__cap">Members can use available budget funds</div>
            <budget-members
                :store="budgetStore"
                adding
                path="members"
                :model-value="form"
            ></budget-members>
          </div>
        </div>
      </div>
      <div class="_f_l _f_chip">Spend Categories</div>
      <div class="q-py-sm">
        <div class="_form_grid _f_g_r">
          <div class="_form_label">Allowed Categories</div>
          <div class="q-pa-sm">
            <mcc-picker medical-option path="mcc_whitelist" :store="cardStore" :id="form?._id"
                        @update:model-value="autoSave('mcc_whitelist')" v-model="form.mcc_whitelist"></mcc-picker>

          </div>

          <div class="_form_label">Disallowed Categories</div>
          <div class="q-pa-sm">
            <mcc-picker path="mcc_blacklist" allow="Restrict" :id="form?._id" :store="cardStore"
                        v-model="form.mcc_blacklist"></mcc-picker>
          </div>
        </div>
      </div>
    </template>

    <div class="q-pa-md row justify-end" v-if="form && form.name && form.cardholder && !form._id">
      <q-btn color="accent" push no-caps class="tw-six" label="Create Card" @click="save"></q-btn>
    </div>

    <common-dialog setting="sm" :model-value="!!cancelDialog" @update:model-value="val => val ? '' : cancelDialog = ''">
      <div class="q-pa-lg _fw">
        <div class="font-1r">Are you sure you want to cancel this card?</div>
        <div class="q-pa-sm font-3-4r">If you only mean to "pause" or "freeze" the card, choose "Inactive" instead</div>
        <div class="q-pt-md row justify-end">
          <q-btn flat no-caps @click="cancelDialog = ''">
            <q-icon color="primary" name="mdi-chevron-left"></q-icon>
            <span class="q-ml-sm">No, go back</span>
          </q-btn>
          <q-btn flat no-caps @click="setStatus(cancelDialog, true)">
            <q-icon color="red" name="mdi-close"></q-icon>
            <span class="q-ml-sm">Yes, Cancel</span>
          </q-btn>
        </div>
      </div>
    </common-dialog>

  </div>
</template>

<script setup>
  import AmountForm from 'components/accounts/issuing/components/budgets/forms/AmountForm.vue';
  import BudgetPicker from 'components/accounts/issuing/components/budgets/lists/BudgetPicker.vue';
  import CardholderForm from 'components/accounts/issuing/components/care-cards/forms/CardholderForm.vue';
  import MccPicker from 'components/accounts/issuing/components/budgets/forms/MccPicker.vue';
  import BudgetMembers from 'components/accounts/issuing/components/budgets/forms/BudgetMembers.vue';
  import StatusChip from '../cards/StatusChip.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {useCareCards} from 'stores/care-cards';
  import {idGet} from 'src/utils/id-get';
  import {computed, ref, watch} from 'vue';
  import {HForm, HSave} from 'src/utils/hForm';

  import {budgetTree} from 'components/accounts/issuing/components/budgets/utils/budget-tree';
  import {useBanking} from 'stores/banking';

  const cardStore = useCareCards();
  const bankStore = useBanking();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    budget: { required: true },
    modelValue: { required: true },
    careAccount: { required: true }
  })

  const { item: card } = idGet({
    store: cardStore,
    value: computed(() => props.modelValue),
  })
  const { ca, budget, budgetStore } = budgetTree(computed(() => props.budget))


  const spent = computed(() => (card.value?.spent || 0) + (card.value?.spent_pending || 0))
  const parent_balances = computed(() => {
    if (budget.value) {
      const { amount = 0, recurs = 0, assigned_amount = 0, assigned_recurs = 0 } = budget.value;
      return { amount, recurs, assigned_amount, assigned_recurs }
    } else return { amount: 0, recurs: 0, assigned_amount: 0, assigned_recurs: 0 }
  })

  const moovCard = ref({});

  const { form, save } = HForm({
    store: cardStore,
    value: card,
    beforeFn: (val) => {
      if (!val.owner) val.owner = budget.value?.owner
      if (!val.budget) val.budget = budget.value?._id
      if (!val.connect_id) val.connect_id = budget.value?.connect_id
      return val;
    },
    afterFn: (val) => emit('update:model-value', val)
  })

  const { autoSave, setForm } = HSave({ form, store: cardStore })

  const tosLoading = ref(false);
  const tosReady = async () => {
    if (!form.value?._id && !tosLoading.value) {
      tosLoading.value = true;
      await save()
          .catch(err => {
            console.error(`Error saving card: ${err.message}`);
            return
          })
      tosLoading.value = false;
    }
  }

  const setMoovCard = (crd) => {
    if(crd) {
      const changes = {}
      if (crd.state && crd.state !== form.value.status) changes.status = crd.state;
      if (crd.lastFourCardNumber && crd.lastFourCardNumber !== form.value.last4) changes.last4 = crd.lastFourCardNumber;
      if (Object.keys(changes).length) {
        cardStore.patch(form.value._id, changes)
      }
      moovCard.value = crd;
    }
  }

  const setCareCard = async (val) => {
    if (!val) val = { ...form.value };
    if(val?.moov_card) {
      const sc = await bankStore.get(val.moov_id, {
        banking: {
          moov: {
            method: 'get_card',
            args: [val.moov_card]
          }
        }
      })
          .catch(err => console.error(`Error retrieving card: ${err.message}`))
      setMoovCard(sc)
    }
  }

  const cancelDialog = ref('');
  const setStatus = async (val, go) => {
    const oldStatus = form.value.status;
    if(oldStatus !== val) {
      if(!go && val === 'canceled') cancelDialog.value = val;
      else {
        cancelDialog.value = '';
        cardStore.patchInStore(card.value._id, { status: val });
        const updated = await bankStore.get(card.value.connect_id, {
          banking: {
            moov: {
              method: 'update_card',
              args: [card.value.moov_card, { state: val }, card.value._id]
            }
          }
        })
            .catch(err => {
              console.error(`Error setting card status: ${err.message}`);
              return undefined
            })
        if (!updated) cardStore.patchInStore(card.value._id, { status: oldStatus })
      }
    }

  }

  watch(budget, (nv) => {
    if (nv && !form.value?.budget) form.value.budget = nv._id
  }, { immediate: true })

  watch(card, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      setCareCard(nv)
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
