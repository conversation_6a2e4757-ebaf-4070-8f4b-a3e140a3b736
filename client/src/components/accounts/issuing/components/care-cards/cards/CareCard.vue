<template>
  <div class="_fw">

    <template v-if="isAllowed">
      <div class="q-pa-sm tw-six font-1r">{{card?.name}}</div>
      <div class="row justify-center __bg pw2 pd4">
        <stripe-view-card
            :budget="budget"
            :connect_id="card?.connect_id"
            :model-value="stripeCard"
            :care-card="card"
            :org="org"
        ></stripe-view-card>
      </div>

      <div class="_fw pw2 pd4">
        <card-transactions :org="org" :care-card="card"></card-transactions>
      </div>

    </template>
    <template v-else>
      <q-spinner v-if="!org?._id" size="40px" class="q-ma-xl" color="primary"></q-spinner>
      <div v-else class="q-pa-lg text-italic font-1r">You don't have permission to view this card</div>
    </template>
  </div>
</template>

<script setup>
  import StripeViewCard from 'components/accounts/issuing/components/care-cards/cards/StripeViewCard.vue';
  import CardTransactions from 'components/accounts/issuing/components/care-cards/lists/CardTransactions.vue';

  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {idGet} from 'src/utils/id-get';
  import {useCareCards} from 'stores/care-cards';
  import {computed, ref, watch} from 'vue';
  import {useRoute} from 'vue-router';
  import {useBudgets} from 'stores/budgets';
  import {useOrgs} from 'stores/orgs';
  import {loginPerson} from 'stores/utils/login';

  const { person, login } = loginPerson()

  const cardStore = useCareCards();
  const budgetStore = useBudgets();
  const orgStore = useOrgs();
  const route = useRoute();

  import {useBanking} from 'stores/banking';
  const bankStore = useBanking()

  const props = defineProps({
    careCardId: { required: false }
  })

  const { item: card } = idGet({
    store: cardStore,
    value: computed(() => props.careCardId || route.params.cardId),
    params: ref({ runJoin: { card_budget_owner: true, card_stripe_card: true } })
  })
  const { item: budget } = idGet({
    store: budgetStore,
    value: computed(() => card.value?._fastjoin?.budget || card.value?.budget),
    params: ref({ runJoin: { budget_owner: true } })
  })
  const { item: org } = idGet({
    store: orgStore,
    value: computed(() => budget.value?._fastjoin?.owner || budget.value?.owner)
  })

  const stripeCard = ref({});
  watch(card, async (nv) => {
    if (nv?._id && nv.stripe_card !== stripeCard.value?.id) {
      if (nv._fastjoin.stripe_card) stripeCard.value = nv._fastjoin.stripe_card;
      else stripeCard.value = await bankStore.get(nv.connect_id, {
        banking: {
          stripe: {
            method: 'get_card',
            args: [nv.stripe_card]
          }
        }
      })
    }
  }, { immediate: true })

  const { canEdit } = clientCanU({
    subject: org,
    or: true,
    caps: computed(() => [[`orgs:${org.value?._id}`, ['orgAdmin']], [`orgs:${org.value?._id}`, ['WRITE']], ['orgs', 'WRITE']]),
    cap_subjects: computed(() => [org.value._id]),
    login
  })

  const isAllowed = computed(() => {
    if (canEdit.value?.ok) return true;
    const id = person.value?._id;
    if (!id || !card.value || !budget.value || !person.value) return false;
    let allList = [];
    const keys = ['members', 'managers', 'approvers']
    for (let i = 0; i < 3; i++) {
      const cardList = card.value[keys[i]] || [];
      for (let idx = 0; idx < cardList.length; idx++) {
        allList.push(cardList[idx])
      }
      const budgetList = budget.value[keys[i]] || [];
      for (let idx = 0; idx < budgetList.length; idx++) {
        allList.push(budgetList[idx])
      }
    }
    return allList.includes(person.value?._id);
  })


</script>

<style lang="scss" scoped>
  .__bg {
    background: #dedede;
  }
</style>
