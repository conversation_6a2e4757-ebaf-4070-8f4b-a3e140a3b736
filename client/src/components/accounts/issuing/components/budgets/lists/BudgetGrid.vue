<template>
  <div class="_fw">

    <div class="_fw q-pa-md" v-if="ca?._id">
      <div class="row items-center __tr">
        <q-chip dense clickable color="transparent" class="tw-six">
          <span>Budgets</span>
        </q-chip>
        <q-space></q-space>
        <q-btn no-caps flat @click="adding = true">
          <span class="alt-font text-grey-7">Add New</span>
          <q-icon color="primary" class="q-ml-sm" name="mdi-plus"></q-icon>
        </q-btn>
      </div>
      <div class="row">
        <div class="col-12 col-md-6 q-pb-sm">
          <q-input dense filled v-model="search.text" placeholder="Search Budgets...">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </div>
      </div>

      <div class="row items-center">
        <div class="col-12 col-sm-6 col-md-4 q-pa-sm" v-for="budget in b$.data" :key="`budget-${budget._id}`">
          <div class="__c">
            <div class="t-r">
              <q-btn dense flat icon="mdi-dots-vertical" @click="editing = budget._id"></q-btn>
            </div>
            <budget-card @click="setBudget(budget._id)" :model-value="budget"></budget-card>
          </div>
        </div>

      </div>
      <div class="row justify-end q-py-md">
        <q-pagination
            @update:model-value="b$.toPage($event)"
            :model-value="pagination.currentPage"
            :min="1"
            :max="pagination.pageCount"
            direction-links
            boundary-numbers
        ></q-pagination>
      </div>
    </div>

    <common-dialog setting="right" :model-value="adding || !!editing" @update:model-value="toggleDialog">
      <div class="_fw q-pa-md bg-white">
        <budget-form :parent="parent" :org="fullOrg" :model-value="editing"></budget-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import BudgetCard from 'components/accounts/issuing/components/budgets/cards/BudgetCard.vue';
  import BudgetForm from 'components/accounts/issuing/components/budgets/forms/BudgetForm.vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {computed, ref} from 'vue';
  import {useBudgets} from 'stores/budgets';
  import {idGet} from 'src/utils/id-get';
  import {LocalStorage} from 'symbol-auth-client';
  import {useOrgs} from 'stores/orgs';
  import {useCareAccounts} from 'stores/care-accounts';
  import {useRouter} from 'vue-router';

  const router = useRouter();

  const budgetStore = useBudgets();
  const orgStore = useOrgs();
  const caStore = useCareAccounts();

  const props = defineProps({
    query: Object,
    parent: { required: false }
  })

  const { item: fullOrg } = idGet({
    store: orgStore,
    value: computed(() => LocalStorage.getItem('org_id'))
  })
  const { item:ca } = idGet({
    store: caStore,
    value: computed(() => LocalStorage.getItem('care_account_id')),
  })

  const { search, searchQ } = HQuery({})
  const { h$: b$, pagination } = HFind({
    store: budgetStore,
    limit: ref(10),
    pause: computed(() => !fullOrg.value?.budgets?.length),
    params: computed(() => {
      const query = {
        ...searchQ.value,
        owner: fullOrg.value?._id,
        parent: { $exists: false },
        ...props.query
      }
      if(props.parent) query.parent = props.parent._id || props.parent
      return {
        query
      }
    })
  })

  const setBudget = (id) => {
    if(id) {
      router.push({name: 'org-budget', params: { budgetId: id}})
    }
  }

  const editing = ref();
  const adding = ref(false);
  const toggleDialog = (val) => {
    if (!val) {
      adding.value = false;
      editing.value = undefined;
    }
  }
</script>

<style lang="scss" scoped>
  .__tr {
    //background: var(--q-p0);
    padding: 10px min(30px, 2vw);
    border-radius: 6px;
    //box-shadow: 0 2px 8px -4px #999;
  }
  .__c {
    position: relative;
    width: 100%;
    padding: 20px;
    border-radius: 8px;
    background: white;
    border: solid 2px #dedede;
    box-shadow: 0 10px 15px -10px #999;
    cursor: pointer;
    transition: all .1s;

    &:hover {
      background: #f9f9f9;
    }
  }
</style>
