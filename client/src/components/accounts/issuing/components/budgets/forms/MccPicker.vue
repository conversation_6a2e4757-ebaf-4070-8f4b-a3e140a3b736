<template>


  <q-item dense clickable @click="dialog = true">
    <q-item-section>
      <q-item-label class="tw-six text-grey-7">Add New</q-item-label>
    </q-item-section>
    <q-item-section side>
      <q-icon name="mdi-plus" color="primary"></q-icon>
    </q-item-section>
    <common-dialog v-model="dialog" setting="smmd">
      <q-checkbox label="Medical Categories Only" :model-value="!general"
                  @update:model-value="general = !$event"></q-checkbox>
      <div class="q-pa-md bg-white _fw">
        <q-input dense filled v-model="search.text">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <q-list separator dense>
          <q-item clickable @click="emitUp(jd.itemName)" v-for="jd in h$.data" :key="jd._id">
            <q-item-section avatar>
              <q-avatar size="15px" :color="getColor(jd.itemName)"></q-avatar>
            </q-item-section>
            <q-item-section>
              {{ jd.data.description || jd.itemName.split('_').map(a => $capitalizeFirstLetter(a)).join(' ') }}
            </q-item-section>
            <q-item-section side>
              <q-icon color="green" name="mdi-check-circle" v-if="items.includes(jd.itemName)"></q-icon>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </common-dialog>
  </q-item>
  <q-separator></q-separator>
  <div class="flex items-center">
    <q-checkbox v-if="medicalOption" size="sm" color="primary" :label="`${allow} All Medical`" :model-value="allMed"
                @update:model-value="setList($event, medical_mcc)"></q-checkbox>
<!--    <q-checkbox size="sm" color="secondary" :label="`${allow} fringe (gyms, spas, grocery)`" :model-value="allFringe"-->
<!--                @update:model-value="setList($event, fringe_mcc)"></q-checkbox>-->
  </div>
  <q-separator></q-separator>
  <div class="q-pa-sm tw-six font-3-4r text-grey-7">{{allow}}ed Categories ({{items.length}})</div>
  <mcc-list :model-value="modelValue">
    <template v-slot:side="scope">
      <q-item-section side>
        <q-btn dense flat size="sm" color="red" icon="mdi-close" @click="emitUp(scope.item)"></q-btn>
      </q-item-section>
    </template>
  </mcc-list>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import MccList from 'components/accounts/issuing/components/budgets/lists/MccList.vue';

  import {computed, ref} from 'vue';
  import {medical_mcc, fringe_mcc, getColor} from '../utils/mcc-codes';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import {$capitalizeFirstLetter} from 'src/utils/global-methods';

  const junkStore = useJunkDrawers();

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    medicalOption: Boolean,
    modelValue: { required: true },
    id: { required: true },
    path: { required: true },
    store: { required: true },
    emitValue: Boolean,
    query: Object,
    params: Object,
    iconAttrs: Object,
    allow: { default: 'Allow' }
  })
  const dialog = ref(false);

  const items = computed(() => props.modelValue || [])
  const allMed = computed(() => {
    let all = true;
    const list = Object.keys(medical_mcc);
    for (let i = 0; i < list.length; i++) {
      if (!items.value.includes(list[i])) {
        all = false;
        break;
      }
    }
    return all
  })
  const allFringe = computed(() => {
    let all = true;
    const list = Object.keys(fringe_mcc);
    for (let i = 0; i < list.length; i++) {
      if (!items.value.includes(list[i])) {
        all = false;
        break;
      }
    }
    return all
  })

  const setList = async (val, mccObj) => {
    const list = [...props.modelValue || []]
    const newList = [];
    const keys = Object.keys(mccObj);
    let run;
    const splices = [];
    for (let i = 0; i < keys.length; i++) {
      const idx = list.indexOf(keys[i]);
      if (idx > -1 && !val) {
        run = true;
        newList.push(keys[i]);
        splices.push(i);
      }
      else if (idx === -1 && val){
        run = true;
        newList.push(keys[i]);
        list.push(keys[i]);
      }
    }
    if(run) {
      splices.map(a => list.splice(a, 1))
      const obj = {};
      if (val) obj.$addToSet = { [props.path]: { $each: list } }
      await props.store.patchInStore(props.id, { [props.path]: list })
      await props.store.patch(props.id, obj)
    }
  }

  const emitUp = (v) => {
    if (!props.modelValue?.length) emit('update:model-value', [v]);
    else {
      const list = [...props.modelValue || []]
      const idx = list.indexOf(v);
      if (idx > -1) {
        list.splice(idx, 1);
        props.store.patchInStore(props.id, { [props.path]: list })
        props.store.patch(props.id, { [props.path]: {$pull: v }})
      } else {
        list.push(v);
        props.store.patchInStore(props.id, { [props.path]: list })
        props.store.patch(props.id, { [props.path]: {$pull: v }})
      }
    }
  }

  const { search, searchQ } = HQuery({ keys: ['itemName'] });
  const general = ref(false);
  const { h$ } = HFind({
    store: junkStore,
    limit: ref(10),
    pause: computed(() => !dialog.value),
    params: computed(() => {
      const query = {
        ...searchQ.value,
        drawer: 'mcc'
      }
      if (!general.value) query['data.category'] = { $in: ['medical'] }
      return {
        query
      }
    })
  })
</script>

<style lang="scss" scoped>

</style>
