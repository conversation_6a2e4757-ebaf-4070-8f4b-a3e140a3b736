<template>
  <q-input v-bind="{
    modelValue: input,
    placeholder: '#########',
    max: 9,
    min: 9,
    error: !!error,
    hint,
    errorMessage: error,
    ...$attrs
  }"
           @update:model-value="setInput"
           @keyup.enter="select({ routingNumber: input })"
  >
    <template v-slot:prepend>
      <slot name="prepend"></slot>
    </template>
    <template v-slot:append>
      <slot name="append">
        <q-spinner color="primary" v-if="loading"></q-spinner>
        <q-icon name="mdi-check-circle" color="green" v-else-if="selected?.routingNumber"></q-icon>
        <q-btn v-else-if="input?.length === 9" dense flat label="Add" no-caps color="blue" @click="select({ routingNumber: input })"></q-btn>
      </slot>
    </template>
  </q-input>
  <q-slide-transition>
    <div class="_fw" v-if="res.total && !selected?.routingNumber">
      <q-list dense separator>
        <q-item clickable @click="select(bank)" v-for="(bank, i) in res.data" :key="`bank-${i}`">
          <q-item-section>
            <q-item-label caption>{{bank.routingNumber}}</q-item-label>
            <q-item-label>{{bank.cleanName || bank.customerName}}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>
  </q-slide-transition>
  <common-dialog size="small" v-model="confirmDialog">
    <div class="q-pa-md">
      <div class="font-1r tw-six">Manually Add Routing Number?</div>
      <div class="font-3-4r">Select a matching financial institution or confirm you want to manually enter {{input}}</div>
      <div class="q-pt-sm row justify-end">
        <q-btn flat no-caps @click="confirmDialog = false">
          <span class="q-mr-sm">Cancel</span>
          <q-icon name="mdi-close" color="red"></q-icon>
        </q-btn>
        <q-btn @click="select({ customerName: '_manual_add', routingNumber: input })" color="green" icon-right="mdi-check" label="Add" push glossy no-caps>
        </q-btn>
      </div>
    </div>
  </common-dialog>
</template>

<script setup>

  import {useBanking} from 'stores/banking';
  import {computed, ref, watch} from 'vue';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  const store = useBanking();

  const emit = defineEmits(['update:model-value', 'bank-name']);
  const props = defineProps({
    modelValue: { required: true }
  })

  const input = ref('');

  const error = ref('');
  const hint = computed(() => {
    if(input.value?.length !== 9) return 'Routing number must be exactly 9 digits'
    else return undefined
  })

  const res = ref({ data: [], total: 0 });
  const selected = ref({});
  const loading = ref(false);

  const checkDone = async () => {
    loading.value = true;
    res.value = await store.find({ query: { routingNumber: input.value}, banking: { lookup: { routing: true }} })
        .catch(err => {
          loading.value = false
          console.error('Error searching for routing number', err.message);
        });
    loading.value = false;
  }
  const setInput = (val) => {
    input.value = val;
    if(val.length === 9) checkDone();
  }

  const confirmDialog = ref(false);

  const select = (val) => {
    if(val?.customerName){
      selected.value = val;
      confirmDialog.value = false;
      emit('update:model-value', val.routingNumber);
      if(val.customerName && val.customerName !== '_manual_add') emit('bank-name', val.customerName)
    } else if(val.routingNumber?.length === 9) {
      confirmDialog.value = true;
    }
  }

  watch(() => props.modelValue, (nv, ov) => {
    if(nv && nv !== ov) input.value = nv;
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
