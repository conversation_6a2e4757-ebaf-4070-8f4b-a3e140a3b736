<template>
  <div style="position: relative">
    <div @pointerenter="tip = true" @pointerleave="tip = false" @touch="tip = true" :class="textClass"
         class="text-xxs text-mb-xxs text-weight-light">
      <div class="flex items-start">
        <div>Tax:</div>
        <q-icon size="15px" :name="currencyIcon" class="q-mr-xs"></q-icon>
        <div v-if="postal_code">{{ dollarString(taxTotal, '', 2) }}</div>
        <div v-else>
          <q-btn dense size="sm" flat color="primary" icon="mdi-help-circle">
            <q-popup-edit v-if="!origin" :model-value="postal_code_input">
              <q-input label="Postal Code" v-model="postal_code_input" @keyup.enter="setPostal">
                <template v-slot:append>
                  <q-btn round flat icon="mdi-check" color="primary" @click="setPostal"/>
                </template>
              </q-input>
            </q-popup-edit>
          </q-btn>
        </div>
        <q-icon v-if="postal_code" name="mdi-information" size="12px" class="q-ml-xs pointer">
          <q-menu>
            <q-card style="width: 250px; max-width: 100%;" class="q-pa-sm">
              <div class="row justify-end">
                <q-btn icon-right="mdi-menu-down" dense size="sm" flat color="primary"
                       :label="postal_code ? postal_code : 'Postal Code'">
                  <q-popup-edit v-if="!origin" :value="postal_code_input">
                    <q-input label="Postal Code" v-model="postal_code_input"
                             @keyup.enter="setPostal">
                      <template v-slot:append>
                        <q-btn round flat icon="mdi-check" color="primary" @click="setPostal"/>
                      </template>
                    </q-input>
                  </q-popup-edit>
                </q-btn>
              </div>
              <q-list separator dense>
                <q-item v-for="(item, i) in $lget(taxData, 'taxes', [])" :key="`tax-item-${i}`">
                  <q-item-section>
                    <q-item-label>{{ item.name }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>
                    <div class="text-xxs text-mb-xxs text-weight-bold">
                      {{
                        dollarString(item.rate, '', 2) / (item.type === 'percent' ? 100 : 1)
                      }}{{ item.type === 'percent' ? '%' : '' }}
                    </div>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card>
          </q-menu>
        </q-icon>
      </div>
    </div>
  </div>
</template>

<script setup>

  import {ref, watch} from 'vue';
  import {getTaxLine} from 'components/bills/tax/utils';
  import { dollarString } from 'src/utils/global-methods';

  const emit = defineEmits(['total'])
  const props = defineProps({
    subTotal: { type: Number, default: 0 },
    currencyIcon: String,
    sellerAddress: Object,
    buyerAddress: Object,
    settings: Object,
    textClass: {
      type: String,
      default: 'text-xxs text-mb-xxs text-weight-light'
    }
  })

  const tip = ref(false);
  const postal_code_input = ref('');

  const { getTaxTotals, postal_code, state, city, taxData, taxTotal } = getTaxLine({ settings: props.settings,emit, subTotal: props.subTotal, sellerAddress: props.sellerAddress, buyerAddress: props.buyerAddress })

  const setPostal = () => {
    if (postal_code_input.value?.length === 5) postal_code.value = postal_code_input.value;
    getTaxTotals()
  }

  watch(() => props.address, (nv) => {
    if (nv) postal_code.value = nv.postal;
  }, { immediate: true })


</script>

<style scoped lang="scss">
  .__tax_popup {
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    transform: translate(-100%, -100%);
    overflow: hidden;
    transition: all .2s ease-out;
  }
</style>
