import {AnyObj} from 'src/utils/types';
import {dollarString} from 'src/utils/global-methods';
import {isEmailRule} from 'stores/validate/validators';
import {isValid} from 'src/utils/date-utils';
import {getAge} from 'components/enrollments/ichra/utils/index';
import {computed, ref} from 'vue';

const censusFieldIndexes: { [key: number | string]: string } = {
    0: 'firstName',
    1: 'lastName',
    2: 'email',
    3: 'age',
    4: 'married',
    5: 'deps',
    6: 'zip',
    7: 'wage'
}
const idxByKey = () => {
    const obj = {};
    for (const k in censusFieldIndexes) {
        obj[censusFieldIndexes[k]] = k;
    }
    return obj
}
export const censusIndexByKey = idxByKey()

const parseDate = (val) => {
    // console.log('parse data', val, isValid(val))
    if (isValid(val)) return getAge(val);
    else return val;
}

export const getReqs = () => {
    const csvData = ref([[]])
    const errs = ref({});
    const reqName = ref(false);
    const reqs = computed(() => [
        {
            key: 'age',
            label: 'Age or DOB',
            tooltip: 'Enter age or dob as MM/DD/YYYY',
            ex: 41,
            def: -1,
            format: (val) => {
                return val;
            },
            rev: (val) => {
                // console.log('rev', val)
                let spl = [];
                if (typeof val === 'string') spl = val.split('/');
                if (spl.length > 2) {
                    return parseDate(val)
                } else {
                    const v = Number(val);
                    if (typeof v === 'number') {
                        return v
                    } else return val;
                }
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`
                if (typeof val !== 'number' || val < 0 || val > 120) {
                    errs.value[key] = 'Invalid Age'
                    return false
                }
                else {
                    delete errs.value[key]
                    return true
                }
            }
        },
        {
            key: 'wage',
            label: 'Annual Pay',
            tooltip: 'Annual if salary, hourly if hourly',
            ex: 85000,
            def: 0,
            format: (val) => {
                return dollarString(val, '$', 0, '')
            },
            rev: (val) => {
                if (!val && val !== 0) return undefined;
                if(typeof val === 'number') return val;
                return Number(val.replace(/[^\d.]/g, ''))
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`
                if (typeof val !== 'number') {
                    errs.value[key] = 'Invalid Income';
                    return false
                }
                else {
                    delete errs.value[key]
                    return true
                }
            }
        },
        {
            key: 'married',
            label: 'Married (Y/N)',
            tooltip: 'Just marital status, not whether spouse participates',
            ex: 'Y',
            def: 'N',
            format: (val) => typeof val === 'string' ? val.charAt(0).toUpperCase() === 'Y' ? 'Y' : 'N' : 'N',
            rev: (val) => {
                if (!val) return 'N'
                if (typeof val === 'string') {
                    const v = val.trim().slice(0, 1).toUpperCase()
                    if (['Y', 'N'].includes(v)) return v;
                }
                return ''
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if (typeof val === 'string') {
                    const v = val.trim().slice(0, 1).toUpperCase()
                    if (['Y', 'N'].includes(v)) {
                        delete errs.value[key];
                        return true
                    }
                }
                errs.value[key] = 'Enter "Y" or "N"'
                return false
            }
        },
        {
            key: 'deps',
            label: '# Dependents',
            tooltip: 'Dependents generally under 26, or disabled',
            ex: 2,
            def: 0,
            format: (val) => val,
            rev: (val) => {
                if (!val) return 0;
                return Number(val)
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if (typeof val !== 'number') {
                    errs.value[key] = 'Must be a number'
                    return true
                } else {
                    delete errs.value[key]
                    return true
                }
            }
        },
        {
            key: 'zip',
            label: 'Zip',
            tooltip: 'Employee home zip code',
            ex: '27283',
            def: '',
            format: (val) => val,
            rev: (val) => {
                const v = val ? String(val) : '';
                return v.replace(/\D/g, '');
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if (/^\d{5}$/.test(val)) {
                    delete errs.value[key];
                    return true
                }
                else {
                    errs.value[key] = 'Invalid Zip Code'
                    return false
                }
            }
        },
        {
            required: true,
            key: 'firstName',
            label: 'First Name',
            tooltip: 'Employee First Name',
            ex: 'John',
            def: '',
            format: (val) => val?.trim(),
            rev: (val) => {
                if(!val) return ''
                return val;
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if(typeof val !== 'string' || !val.length) {
                    errs.value[key] = 'Enter first name'
                    return false
                }
                else {
                    delete errs.value[key];
                    return true
                }
            }
        },
        {
            required: true,
            key: 'lastName',
            label: 'Last Name',
            tooltip: 'Employee Last Name',
            ex: 'Smith',
            def: '',
            format: (val) => val?.trim(),
            rev: (val) => {
                if(!val) return ''
                return val;
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if(typeof val !== 'string' || !val.length) {
                    errs.value[key] = 'Enter last name'
                    return false
                }
                else {
                    delete errs.value[key];
                    return true
                }
            }
        },
        {
            key: 'email',
            label: 'Email',
            tooltip: 'Email to send enrollment invite (when ready)',
            ex: '<EMAIL>',
            def: '',
            format: (val) => val?.trim().toLowerCase(),
            rev: (val) => {
                if(!val) return ''
                return val;
            },
            check: (val, row, col) => {
                const key = `${row}-${col}`;
                if (isEmailRule(val)) {
                    delete errs.value[key];
                    return true
                }
                else {
                    errs.value[key] = 'Invalid Email'
                    return false
                }
            }
        }
    ].sort(((a, b) => censusIndexByKey[a.key] - censusIndexByKey[b.key])))

    const useErrs = computed(() => {
        const obj:any = {};
        for(const k in errs){
            const spl = k.split('-');
            if((csvData.value[spl[0]] || [])[spl[1]]) obj[k] = errs.value[k];
        }
        return obj;
    })
    return {
        errs,
        useErrs,
        reqs,
        csvData,
        reqName
    }
}


export const fromCensus = (val: Array<Array<string | number>>, ex: Array<AnyObj>) => {
    if (!val?.length) return ex || []
    const conv: any = [];
    const exArr: any = ex || [];
    const t = new Date().getTime();
    for (let i = 0; i < val.length; i++) {
        const obj: any = exArr[i] || {};
        for (let idx = 0; idx < val[i].length; idx++) {
            obj[censusFieldIndexes[idx]] = val[i][idx];
        }
        if (!obj.uid) obj.uid = `${i}-${t}`
        obj.updatedAt = new Date();
        obj.hourly = obj.hourly === 'Y'
        if (obj.wage) {
            if (obj.wage > 2000) {
                obj.income = obj.wage;
                obj.hourly = false;
            } else {
                if (!obj.hours) obj.hours = 40;
                obj.income = obj.wage * obj.hours
            }
        } else if(obj.income){
            if(obj.hourly) obj.wage = obj.income / (Number(obj.hours) || 40)
            else obj.wage = obj.income;
        }
        conv.push(obj);
    }
    return conv;
}

export const toCensus = (ex: Array<AnyObj>, reqs:Array<any>) => {
    if (!ex?.length) return [];
    const census: any = [];
    const keys = Object.keys(censusFieldIndexes).sort((a, b) => Number(a) - Number(b));
    for (let i = 0; i < ex.length; i++) {
        const arr: any = []
        const obj = ex[i] || {};
        for (let idx = 0; idx < keys.length; idx++) {
            let v = obj[censusFieldIndexes[idx]];
            if (!v && v !== 0) v = ''
            arr.push(reqs[idx].rev(v));
        }
        census.push(arr);
    }
    return census;
}

