<template>
  <q-chip v-bind="{color: 'ir-bg', clickable: true, ...$attrs}" @click="uploadDialog = true">
    <span class="tw-five font-7-8r">Upload</span>
    <q-icon name="mdi-upload" class="q-ml-sm" color="accent"></q-icon>

    <common-dialog setting="smmd" v-model="uploadDialog">
      <div class="_fw q-pa-lg bg-white">
        <div class="q-py-md font-1-1-4r tw-five">Upload your Benefit Guide or invoice and we'll use it to create your plan in seconds
        </div>
        <div class="font-7-8r tw-six text-accent">Max Size 500KB - PDF only</div>
        <div class="font-7-8r q-pb-md tw-five text-accent">Many benefit guides are larger than 500KB - trim to the
          major medical benefit portion, which is usually just a few short pages showing premiums, deductibles, max
          oop, network, etc.
        </div>

        <div class="row justify-center q-pt-md">
          <div class="__uploader">
            <upload-ui
                v-if="!uploading"
                @update:display="display = $event"
                log
                :div-attrs="{ class: '_fa' }"
                :upload="handleUpload"
                :max-size="512000"
                allow-types="application/pdf"
            >
            </upload-ui>

            <q-slide-transition>
              <div v-if="uploading" class="_fa flex flex-center">
                <div>
                  <div class="row justify-center q-pb-md">
                    <ai-logo opaque></ai-logo>
                  </div>
                  <div class="text-center tw-five font-1r text-ir-deep">Processing Documents...</div>
                </div>
              </div>
            </q-slide-transition>
          </div>
        </div>
      </div>
    </common-dialog>
  </q-chip>
</template>

<script setup>

import AiLogo from 'src/utils/icons/AiLogo.vue';
import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
import UploadUi from 'components/common/uploads/components/UploadUi.vue';
import {ref} from 'vue';
import {axiosFeathers, restCore} from 'components/common/uploads/services/utils';

const emit = defineEmits(['update:gps']);
const props = defineProps({
  gps: { required: true }
})

const uploadDialog = ref(false)
const uploading = ref(false)
const display = ref();


const handleUpload = async (files) => {
  const payload = new FormData();
  for (let i = 0; i < files.length; i++) {
    payload.append('files', files[i].raw)
  }
  uploading.value = true;
  const res = await axiosFeathers().patch(`/gps/${props.gps._id}`, payload, {
    params: {
      core: restCore(),
      runJoin: { ai_upload: true }
    }
  })
      .catch(err => console.error(`Error adding files: ${err.message}`))
  uploading.value = false;
  if (res.data._id) emit('update:gps', res);

  uploadDialog.value = false
  // if(res.data?._id) gpStore.patchInStore(res.data._id, res.data)
}
</script>

<style lang="scss" scoped>
  .__uploader {
    cursor: pointer;
    width: 300px;
    max-width: 100%;
    height: 100px;
    border-radius: 10px;
    background: var(--ir-bg2);
    transition: all .2s;

    &:hover {
      background: var(--ir-bg1);
      transform: translate(0, -3px);
    }
  }
</style>
