import {shopGet} from 'components/market/utils/shop-get';
import {computed, ref} from 'vue';
import {AnyRef} from 'src/utils/types';
import {useEnvStore} from 'stores/env';
import {sessionFamily} from 'components/households/utils/session-family';
import {byIdObj, planTypeManager, rankById} from 'components/market/shop/utils/rank-by-id';
import {useGps} from 'stores/gps';
import {useGpps} from 'stores/gpps';
import {getFixedRateKey} from 'components/coverages/utils/display';
import {shopResetId} from 'components/market/shop/utils/reset-id';
import {countdownUtils} from 'components/market/shop/utils/countdown';
import {$infoNotify, fakeId} from 'src/utils/global-methods';
import {SessionStorage} from 'symbol-auth-client';
import {loginPerson} from 'stores/utils/login';

type Options = {
    gpps: AnyRef<any>,
    gps: AnyRef<any>,
    coverage: AnyRef<any>
    emit: (evt:string, ...args: any) => void
}
export const compareShop = ({ gpps, gps, coverage, emit}:Options) => {
    const envStore:any = useEnvStore()
    const gpsStore:any = useGps();
    const gppsStore:any = useGpps();

    const notabot = ref(false);
    const showTurnstile = ref(true);

    const { person, isAuthenticated } = loginPerson()

    const { household, place } = sessionFamily(envStore)

    const { activeTypes, toggleType } = planTypeManager(async (v) => byId.value = await rankById(shop.value, v))

    const allLength = computed(() => byId.value.average?.length || 0)

    const covRank = computed(() => {
        const id = coverage.value?.id;
        if (!id) return {};
        const idx = byId.value.average.indexOf(id)
        if (idx === -1) return {};
        return {
            rank: idx + 1,
            ...byId.value.all[id],
        }
    })

    const byId = ref(byIdObj())
    const setById = async () => {
        byId.value = await rankById(shop.value, activeTypes.value);
    }

    const ale = ref(false);
    const useAle = computed(() => gps.value._id ? gps.value.ale : ale.value);


    const toggleAptc = (val) => {
        if (val !== shop.value.useAptc) {
            maybeSave('useAptc', val);
        }
        setById()
    }
    const toggleAle = (val) => {
        if (val) toggleAptc(false);
        ale.value = val;
        if (gps.value._id) {
            gpsStore.patchInStore(gps.value._id, { ale: val })
            gpsStore.patch(gps.value._id, { ale: val })
        }
    }


    const { shop, shopStore, route, stats, maybeSave, setStat, hh:hhObj } = shopGet({
        shop: computed(() => gpps.value?.shop),
        onWatch: (val) => {
            console.log('on watch', val);
            emit('shop-change', val);
            if (val?._id) {
                const { age: a, place: pl, income: i, people: pe, gender: ge } = val.stats;
                if (a) envStore.setAge(a)
                if (pl?.countyfips) envStore.setPlace(pl)
                if (i) envStore.setIncome(i)
                if (ge) envStore.setGender(ge)
                if (pe?.length) {
                    let sp = [];
                    const deps = [];
                    const ppl = [{ age: a, gender: ge }];
                    for (let i = 0; i < pe.length; i++) {
                        // console.log('running pe', i, pe[i])
                        if (pe[i].relation === 'spouse' || pe[i].child === false) sp = [pe[i].age, pe[i].gender];
                        else if (pe[i].relation === 'child' || pe[i].child) deps.push([pe[i].age, pe[i].gender])
                        ppl.push(pe[i])
                    }
                    const str = ppl.map(a => `${a.age}-${a.gender}`).join('|')
                    const gppsStr = (gpps.value?.household?.people || []).map(a => `${a.age}-${a.gender}`).join('|')
                    if(str !== gppsStr && gpps.value._id){
                        gppsStore.patch(gpps.value._id, { $set: { 'household.people': ppl} })
                    }
                    if (sp[0]) envStore.setSpouse(sp)
                    if ((deps[0] || [])[0]) envStore.setDeps(deps)
                }
            }
        }
    });

    const householdToShopHousehold = (hh) => {
        if(!hh?.people) return;
        const idx = hh.people.map(a => a.relation).indexOf('self')
        if (idx > -1) {
            const self = hh.people[idx];
            setStat('age', self.age)
            setStat('gender', self.gender)
        }
        const rest = [...hh.people];
        rest.splice(idx, 1);
        setStat('people', rest)
        setStat('place', household.value.place);
    }

    const def_key = computed(() => {
        return getFixedRateKey({ enrolled: household.value.people }).key
    })
    const def_age = computed(() => {
        return stats.value.age || (household.value?.people || [])[0]?.age
    })

    const { getResetId, lastResetId, validPlace, resetTo } = shopResetId()
    const loading = ref(false);
    const { countdown, interval, resetCountdown } = countdownUtils()
    const resetMarket = async (path, rerun, force) => {
        if (resetTo.value) clearTimeout(resetTo.value);
        if (interval.value) clearInterval(interval.value);

        countdown.value = 0;
        const shopId = shop.value._id;

        resetTo.value = setTimeout(async () => {
            if (!notabot.value && !isAuthenticated.value) return $infoNotify('Please verify you are human');

            countdown.value = 0;
            if (interval.value) clearInterval(interval.value);
            const resetId = getResetId(shop.value);
            if(!lastResetId.value) lastResetId.value = shop.value.resetId || '';
            console.log('last reset id', lastResetId.value, shop.value.resetId, resetId);
            const idMismatch = resetId !== lastResetId.value
            const shouldRun = validPlace(place.value) && (!shop.value.spend_dist || !shop.value._id || idMismatch || force)

            console.log('should run?', shouldRun,validPlace(place.value), shop.value.spend_dist, shop.value._id, rerun, shop.value._id, resetId !== lastResetId.value);
            if (!shouldRun) {
                setById()
                return console.log('no rerun sim compare')
            }
            loading.value = true;
            try {
                lastResetId.value = resetId;
                const plc = shop.value?.stats?.place?.countyfips ? shop.value.stats.place : place.value
                const coverages = Object.keys(gps.value.coverages || {}).map(a => {
                    return {
                        ...gps.value.coverages[a],
                        _id: gps.value.coverages[a].id
                    }
                })
                const mr = await shopStore.get(shop.value?._id || route.params.shopId || fakeId, {
                    query: {
                        $limit: 50,
                        household: household.value,
                        household_size: household.value?.people?.length,
                        place: plc
                    },
                    runJoin: {
                        cost_sim: {
                            tax_rate: .05,
                            skip_aptc: !!gps.value.ale,
                            coverages,
                            compare_ids: coverages.map(a => a._id),
                            data: {
                                resetId,
                                useAptc: false,
                                person: shop.value?.person || person.value?._id,
                                enrollment: shop.value?.enrollment,
                                plan: shop.value?.plan || gps.value.plan
                            },
                            stats: { ...stats.value, place: plc },
                            household: household.value,
                            risk: stats.value.risk || 5
                        }
                    }
                })
                    .catch(err => {
                        console.error(`Error running shop sim: ${err.message}`)
                        loading.value = false;
                        return shop.value || {}
                    })

                if (mr._id) {
                    if(gpps.value._id) {
                        gpps.value.shop = mr._id;
                        gppsStore.patch(gpps.value._id, { shop: mr._id })
                    }
                    SessionStorage.setItem('shop_id', mr._id);
                    await shopStore.get(mr._id);
                    setById()
                    if (gpps.value?._id) gppsStore.patch(gpps.value._id, { shop: mr._id })
                }
            } catch (e) {
                console.error(`Error resetting market: ${e.message}`)
            } finally {
                loading.value = false
            }
        }, force ? 100 : shopId ? 10000 : 3000)
        if (shopId && !force) resetCountdown()
    }

    const similar = computed(() => {
        let done;
        const obj = {};
        const coverages = {};
        const shop_coverages = {};
        for (const cov of shop.value.coverages || []) {
            shop_coverages[cov._id || cov.id] = cov;
        }
        for (const k in gps.value.coverages) {
            if (gps.value.coverages[k].similar) {
                obj[k] = { ...gps.value.coverages[k].similar };
                for (const sub in obj[k]) {
                    done = true;
                    if (!shop_coverages[sub]) delete obj[k][sub]
                }
            }
        }
        return { done, obj, coverages, shop_coverages };
    })

    return {
        similar,
        notabot,
        showTurnstile,
        person,
        isAuthenticated,
        household,
        place,
        activeTypes,
        toggleType,
        allLength,
        covRank,
        byId,
        setById,
        useAle,
        toggleAptc,
        toggleAle,
        shop,
        shopStore,
        route,
        stats,
        maybeSave,
        setStat,
        def_key,
        def_age,
        loading,
        resetMarket,
        countdown,
        resetCountdown,
        householdToShopHousehold,
        hh:hhObj
    }

}
