<template>
  <q-list dense>
    <q-expansion-item v-for="(k, i) in Object.keys(similar.obj || {})" :key="`sim-${i}`" :label="gps.coverages[k].name"
                      group="similars" :default-opened="i === 0">
      <q-list separator>
        <q-item v-for="(subK, idx) in Object.keys(similar.obj[k])" :key="`subj-${i}-${idx}`" clickable
                @click="emit('update:selected', similar.shop_coverages[subK])">
          <q-item-section>
            <q-item-label class="tw-six text-ir-mid">{{ similar.shop_coverages[subK]?.carrierName || '' }}
            </q-item-label>
            <q-item-label>
              {{ similar.shop_coverages[subK]?.name || 'Untitled' }}
            </q-item-label>
            <q-item-label caption>
              {{ similar.obj[k][subK] }}
            </q-item-label>
            <q-item-label class="tw-six font-7-8r text-ir-mid">
              Your premium - <span
                class="font-1r text-primary">{{
                dollarString(getRate(similar.shop_coverages[subK]) * 12, '$', 0)
              }}</span>
            </q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-expansion-item>
  </q-list>
</template>

<script setup>

  import {dollarString} from 'symbol-syntax-utils';
  import {getCoverageRate} from 'components/coverages/utils/display';

  const emit = defineEmits(['update:selected']);
  const props = defineProps({
    gps: { required: true },
    stats: {
      default: () => {
        return {}
      }
    },
    similar: {
      default: () => {
        return {}
      }
    }
  })

  const getRate = (v) => {
    if (!v) return 'n/a'
    if (v.acaPlan) return v.premium;
    else return getCoverageRate({
      coverage: v,
      enrolled: [{ age: props.stats.age, relation: 'self' }, ...props.stats.people || []]
    })
  }

</script>

<style lang="scss" scoped>

</style>
