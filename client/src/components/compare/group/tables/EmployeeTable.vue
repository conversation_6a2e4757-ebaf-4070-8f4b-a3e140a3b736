<template>
  <div class="_fw">

    <div class="q-pb-sm _fw">
      <q-chip v-if="Object.keys(gps.coverages || {}).length" dense color="transparent" square class="tw-six text-ir-deep">Copy Coverage IDs:</q-chip>
      <q-chip v-for="(cov, i) in Object.keys(gps.coverages).map(a => gps.coverages[a])" :key="`cov-${i}`"
              color="ir-bg2" clickable @click="$copyTextToClipboard(cov.id, 'ID Copied')">
        <q-icon name="mdi-content-copy" color="accent" class="q-mr-sm"></q-icon>
        <span>{{ cov.name }}</span>
      </q-chip>
    </div>
    <census-table
        :limit="1000"
        save-btn
        :save-btn-label="!gps.employees?.length ? 'Add Employees' : 'Update Employees'"
        use-name
        :adders="adders"
        :store="gpsStore"
        service-path="gps"
        :update="updateCensus"
        :id="gps._id"
    ></census-table>

  </div>
</template>

<script setup>
  import CensusTable from 'pages/landing/sm-er/results/CensusTable.vue';

  import {LocalStorage} from 'symbol-auth-client';
  import {useGps} from 'stores/gps';
  import {useRoute, useRouter} from 'vue-router';
  import {computed, ref} from 'vue';
  import {$copyTextToClipboard} from 'src/utils/global-methods';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: false }
  })

  const gps = computed(() => props.modelValue || {})
  const gpsStore = useGps();
  const router = useRouter();
  const route = useRoute();

  const changes = ref(false);
  const loading = ref(false);

  const updateCensus = async (employees, data, auto) => {
    let method = 'create';
    const args = [{ groupCompare: true, employees, eeCount: data?.length || employees.length }]
    if (gps.value?._id || data._id) {
      method = 'patch';
      args.unshift(gps.value._id || data._id);
    }
    if (!auto) {
      const dr = await gpsStore[method](...args)
          .catch(err => {
            console.error(`Error adding dr: ${err.message}`);
            loading.value = false;
          })
      if (dr._id) {
        router.push({ ...route, params: { ...route.params, gpsId: dr._id } })
        LocalStorage.setItem('gps', dr._id);
        changes.value = true;
        if (!auto) emit('update:model-value', dr)
      }
    } else if (gps.value._id) gpsStore.patchInStore(...args)
  }

  const adders = ({ errs, csvData }) => {
    return [
      {
        required: true,
        label: 'Coverage ID',
        key: 'coverage',
        ex: '1234567890',
        def: '',
        format: (val) => val?.trim() || '',
        rev: (val) => val?.trim() || '',
        check: () => {
          return
        }
      }
    ]
  }
</script>

<style lang="scss" scoped>

</style>
