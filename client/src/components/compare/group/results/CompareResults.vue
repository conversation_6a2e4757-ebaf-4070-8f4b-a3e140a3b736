<template>
  <div class="_fw">
    <template v-if="!ready.ready">
      <div class="row justify-center q-py-lg">
        <div class="_fw mw800">
          <div class="text-center font-1r tw-five">
            Fix these issues to run simulation:
          </div>
          <ul>
            <li v-if="ready.message">{{ ready.message }}</li>
            <li v-if="ready.premiumMessage">{{ ready.premiumMessage }}</li>
            <li v-if="ready.coverageMessage">{{ ready.coverageMessage }}</li>
          </ul>
        </div>
      </div>
    </template>
    <template v-else>
      <template v-if="notRun.length === gps.employees.length">
        <div class="row justify-center q-py-md">
          <ul class="font-1r tw-five _fw mw800">
            <li v-if="ready.message">{{ ready.message }}</li>
            <li v-if="ready.premiumMessage">{{ ready.premiumMessage }}</li>
            <li v-if="ready.coverageMessage">{{ ready.coverageMessage }}</li>
          </ul>
        </div>
        <div class="row justify-center q-py-md">
          <div class="w600 mw100">
            <div class="text-center font-1r">Once your employee data is accurate, run a market simulation for each
              employee to see how each plan performs.
            </div>
            <div class="row justify-center q-pt-md">
              <q-btn @click="runCompare" :disable="loading" no-caps rounded :color="loading ? 'p1' : 'primary'" glossy>
                <span v-if="!loading" class="q-mr-sm tw-six text-white">Run Simulations</span>
                <span v-else class="q-mr-sm tw-six text-p10">Running Simulations</span>
                <ai-logo :dark="!loading" opaque size="20px"></ai-logo>
              </q-btn>
            </div>
            <div class="row justify-center q-pt-lg" v-if="progressing">
              <div class="__bar">
                <div class="__fill" :style="{ width: `${progress}%` }"></div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>

        <!--      <template v-if="!gps.simStats?.count">-->
        <div class="_fw q-py-sm font-1r">
          There was a problem totaling your results
          <q-chip :clickable="!loading" color="ir-bg2" @click="recalc">
            <span class="q-mr-sm tw-five">Recalculate</span>
            <q-icon v-if="!loading" color="primary" name="mdi-refresh"></q-icon>
            <q-spinner v-else color="primary"></q-spinner>
          </q-chip>
        </div>
        <!--      </template>-->
        <template v-if="gps.lastSim">
          <div class="__title">Group Results</div>

          <table>
            <thead>
            <tr>
              <th></th>
              <th>Current Plan</th>
              <th>Best Insurance</th>
              <th>Best Alternative</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(res, i) in groupResults" :key="`res-${i}`">
              <td>{{ res.label }}</td>
              <td>{{ res.current }}</td>
              <td>{{ res.best }}</td>
              <td>{{ res.alt }}</td>
            </tr>
            </tbody>
          </table>

          <div class="__title">Individual Results</div>

          <q-input @update:modelValue="setSearch" :model-value="eeSearch" class="q-my-md w500 mw100">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
          <table>
            <thead>
            <tr>
              <th>Employee</th>
              <th>Avg Sim Spend</th>
              <th>Current Plan</th>
              <th>Current Rank</th>
              <th>Current Spend</th>
              <th>Current Premium</th>
              <th>Best Plan</th>
              <th>Best Spend</th>
              <th>Best Premium</th>
              <th>Alt Plan</th>
              <th>Alt Spend</th>
              <th>Alt Premium</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(ee, i) in ees" :key="`ee-${i}`">
              <td>{{ ee.name }}</td>
              <td>{{}}</td>
              <td>{{}}</td>
            </tr>
            </tbody>
          </table>
          <pagination-row v-if="gps.employees?.length > pageh.pagination.limit"
                          :max="pageh.pagination.pageCount"
                          :page-record-count="pageh.pageRecordCount"
                          :pagination="pageh.pagination"
                          :h$="pageh"
                          :limit="pageh.pagination.limit"
          ></pagination-row>
        </template>

      </template>
    </template>
    <turnstile-popup v-if="!isAuthenticated && !notabot" v-model:interactive="showTurnstile"
                     v-model="notabot"></turnstile-popup>

  </div>
</template>

<script setup>
  import TurnstilePopup from 'src/utils/turnstile/TurnstilePopup.vue';
  import AiLogo from 'src/utils/icons/AiLogo.vue';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';

  import {computed, ref, watch} from 'vue';
  import {useGps} from 'stores/gps';
  import {$errNotify, $infoNotify, dollarString} from 'src/utils/global-methods';
  import {loginPerson} from 'stores/utils/login';
  import {HFind} from 'src/utils/hFind';
  import {useShops} from 'stores/shops';

  const shopStore = useShops();

  const { isAuthenticated } = loginPerson();

  const gpsStore = useGps();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true }
  })
  const notabot = ref(false);
  const showTurnstile = ref(true);

  const gps = computed(() => props.modelValue || {})

  const loading = ref(false);

  const notRun = computed(() => {
    return (gps.value.employees || []).filter(a => !a.sim)
  })

  const progress = ref(0);
  const progressing = ref(false);
  const runProgress = () => {
    if (!progressing.value) return;
    progress.value += 1;
    if (progress.value < 100) setTimeout(() => {
      runProgress();
    }, 1000 * (gps.value.employees.length / 60))
  }
  const stopProgress = () => {
    progressing.value = false;
    progress.value = 0;
  }

  const ready = computed(() => {
    if (!gps.value.employees?.length) return { ready: false, message: 'Add employees', noPremium: [], noDed: [] }
    let coverageMessage = '';
    const noDed = [];
    let ready = true;
    for (const c in gps.value.coverages || {}) {
      const { deductible } = gps.value.coverages[c];
      if (!Object.keys(deductible || {}).length) noDed.push(gps.value.coverages[c])
    }
    if (noDed.length) {
      coverageMessage += ` The following coverages have no deductible: ${noDed.map(a => a.name).join(', ')}.`
      if (noDed.length === Object.keys(gps.value.coverages || {}).length) {
        coverageMessage += ' Add deductible data to continue.'
        ready = false;
      } else {
        coverageMessage += ' If you do not add a deductible the plan will be excluded from the simulation.'
      }
    }
    let premiumMessage = '';
    const noPremium = [];
    for (const c in gps.value.coverages || {}) {
      const { premium } = gps.value.coverages[c];
      if (!premium) noPremium.push(gps.value.coverages[c])
    }
    if (noPremium.length) {
      premiumMessage += ` The following coverages have no premium: ${noPremium.map(a => a.name).join(', ')}.`
      if (noPremium.length === Object.keys(gps.value.coverages || {}).length) {
        premiumMessage += ' Add premium data to continue.'
        ready = false;
      } else {
        premiumMessage += ' If you do not add a premium the plan will be excluded from the simulation.'
      }
    }
    return {
      premiumMessage,
      noPremium,
      coverageMessage,
      noDed,
      ready
    };
  })

  const runCompare = async () => {
    if (!notabot.value && !isAuthenticated.value) return $infoNotify('Please verify you are human')
    loading.value = true;
    progressing.value = true;
    runProgress();
    const res = await gpsStore.get(props.modelValue._id, {
      runJoin: {
        cost_sim: { exclude: [...ready.value.noDed.map(a => a.id), ...ready.value.noPremium.map(a => a.id)] }
      }
    })
        .catch(err => {
          console.error(`Error running compare: ${err.message}`)
          $errNotify(`Error running simulations - try again. ${err.message}`)
        })
    stopProgress()
    loading.value = false;
    if (res._id) emit('update:model-value', res)
  }

  const eeSearch = ref('');
  const skip = ref(0);
  const pageh = computed(() => {
    const total = gps.value.employees?.length || 0;
    return {
      total,
      toPage: (v) => skip.value = Math.floor((v - 1) * 25),
      pageRecordCount: Math.min(total, skip.value + 25),
      pagination: {
        currentPage: Math.floor(skip.value / 25) + 1,
        pageCount: Math.ceil((gps.value.employees?.length || 0) / 25),
        limit: 25
      }
    }
  })
  const ees = computed(() => {
    const list = gps.value.employees || [];
    return list.filter(a => {
      const name = `${a.firstName} ${a.lastName} ${a.email || ''}`
      return name.toLowerCase().includes(eeSearch.value.toLowerCase())
    }).slice(skip.value, 25 + skip.value);
  })

  const searchTo = ref()
  const setSearch = (val) => {
    clearTimeout(searchTo.value);
    searchTo.value = setTimeout(() => {
      eeSearch.value = val;
      skip.value = 0;
    }, 250)
  }

  const { h$: s$ } = HFind({
    store: shopStore,
    limit: ref(25),
    params: computed(() => {
      const list = [];
      for (let i = 0; i < ees.value.length; i++) {
        if (ees.value[i].sim) list.push(ees.value[i].sim);
      }
      return {
        query: {
          _id: { $in: list }
        }
      }
    })
  })

  const recalc = async () => {
    return await shopStore.find({ query: {}, runJoin: { correct_shops: true } });
    if (!notabot.value && !isAuthenticated.value) return $infoNotify('Please verify you are human')
    loading.value = true;
    const res = await gpsStore.patch(props.modelValue._id, { updatedAt: new Date() }, {
      runJoin: { re_total_sims: true }
    })
        .catch(err => console.error(`Error running totals: ${err.message}`))
    loading.value = false;
    console.log('got res', res);
  }

  const usePtc = ref(true);
  const groupResults = computed(() => {
    const addKey = usePtc.value ? 'Ptc' : ''
    const { currentStats, simStats } = gps.value;
    if (!currentStats || !simStats) return []
    return [
      // {
      //   label: 'Total Premium',
      //   current: dollarString(currentStats.premium, '$', 0),
      //   best: dollarString((simStats[`premium${addKey}`]/simStats.count) * currentStats.count, '$', 0),
      //   alt: dollarString((simStats.altSpend/simStats.count) * currentStats.count, '$', 0)
      // },
      {
        label: 'Premium (Avg)',
        current: dollarString(currentStats.premium / currentStats.count, '$', 0),
        best: dollarString(simStats[`premium${addKey}`] / simStats.count, '$', 0),
        alt: dollarString(simStats.altSpend / simStats.count, '$', 0)
      },
      {
        label: 'Out Of Pocket (Avg)',
        current: dollarString((currentStats.spend - currentStats.spendPremium) / currentStats.spendCount, '$', 0),
        best: dollarString((simStats[`spend${addKey}`] - simStats[`premium${addKey}`]) / simStats.count, '$', 0),
        alt: dollarString((simStats.altSpend - simStats.altPremium) / simStats.count, '$', 0)
      },
      {
        label: 'Total Cost (Premium + OOP)',
        current: dollarString(currentStats.spend / currentStats.spendCount, '$', 0),
        best: dollarString(simStats[`spend${addKey}`] / simStats.count, '$', 0),
        alt: dollarString(simStats.altSpend / simStats.count, '$', 0)
      }
    ]
  })

  watch(gps, (nv) => {
    if (nv && nv.ale) {
      usePtc.value = false;
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

  .__bar {
    width: 300px;
    max-width: 90vw;
    height: 10px;
    border-radius: 5px;
    background: var(--q-p1);
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .__fill {
      height: 100%;
      background: var(--q-p5);
      transition: all .2s;
      width: 100%;
    }
  }

  .__title {
    padding: 5px;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--ir-deep);
  }
</style>
