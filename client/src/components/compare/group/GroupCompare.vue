<template>
  <q-page>
    <div class="row justify-center">
      <div class="_cent pd10 pw2">
        <q-stepper class="_panel" animated header-nav flat v-model="tab">
          <q-step class="_panel" active-icon="mdi-file" icon="mdi-file" name="plans" title="Plans"></q-step>
          <q-step class="_panel" active-icon="mdi-face-man" icon="mdi-face-man" name="employees"
                  title="Employees"></q-step>
          <q-step class="_panel" active-icon="mdi-checkbox-marked-circle" icon="mdi-checkbox-marked-circle"
                  name="results"
                  title="Results"></q-step>
        </q-stepper>
        <q-tab-panels animated v-model="tab">
          <q-tab-panel class="_panel" name="plans">
            <div class="font-1r tw-six text-ir-deep q-pa-md">Add coverages to compare against the market</div>
            <gps-coverage-manager
                v-model="form"
            ></gps-coverage-manager>
          </q-tab-panel>
          <q-tab-panel class="_panel" name="employees">
            <employee-table @update:modelValue="setTab('results')" :model-value="gps"></employee-table>

          </q-tab-panel>
          <q-tab-panel class="_panel" name="results">
            <compare-results :model-value="gps"></compare-results>
          </q-tab-panel>
        </q-tab-panels>
      </div>
    </div>

  </q-page>
</template>

<script setup>
  import EmployeeTable from 'components/compare/group/tables/EmployeeTable.vue';
  import GpsCoverageManager from 'components/compare/group/plans/GpsCoverageManager.vue';
  import CompareResults from 'components/compare/group/results/CompareResults.vue';

  import {idGet} from 'src/utils/id-get';
  import {useGps} from 'stores/gps';
  import {useRoute, useRouter} from 'vue-router';
  import {onMounted, ref} from 'vue';
  import {HForm} from 'src/utils/hForm';

  const gpsStore = useGps();
  const router = useRouter();
  const route = useRoute();

  const { item: gps } = idGet({
    store: gpsStore,
    routeParamsPath: 'gpsId'
  })

  const { form, save } = HForm({
    store: gpsStore,
    value: gps,
    afterFn: (val) => {
      router.push({ ...route, params: { ...route.params, gpsId: val._id } })
    }
  })


  const tab = ref('plans');


  const setTab = (val) => {
    router.push({ ...route, query: { ...route.query, tab: val } });
  }

  onMounted(() => {
    if (route.query.tab) tab.value = route.query.tab;
  })

</script>

<style lang="scss" scoped>
  .__c {
    width: 100%;
    padding: 20px 12px;
    border-radius: 12px;
    box-shadow: 0 2px 6px var(--ir-light);
    background: white;
  }

  .q-stepper__step-inner {
    padding: 0 !important;
  }
</style>
