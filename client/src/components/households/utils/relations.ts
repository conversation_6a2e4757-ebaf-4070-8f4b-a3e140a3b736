export const relationships = [
    'self',
    'spouse',
    'child',
    'father',
    'mother',
    'grandfather',
    'grandmother',
    'grandson',
    'granddaughter',
    'son_in_law',
    'daughter_in_law',
    'uncle',
    'aunt',
    'nephew',
    'niece',
    'cousin',
    'guardian',
    'stepparent',
    'stepson',
    'stepdaughter',
    'adopted_child',
    'foster_child',
    'sister',
    'brother',
    'brother_in_law',
    'sister_in_law',
    'mother_in_law',
    'father_in_law',
    'ward',
    'sponsored_dependent',
    'dependent_minor_dependent',
    'ex_spouse',
    'court_appointed_guardian',
    'collateral_dependent',
    'life_partner',
    'annultant',
    'trustee',
    'other_relationship',
    'other_relative'
]


export const relations = {
    'Self': 'self',
    'Spouse': 'spouse',
    'Child': 'child',
    'Father': 'father',
    'Mother': 'mother',
    'Grandfather': 'grandfather',
    'Grandmother': 'grandmother',
    'Grandson': 'grandson',
    'Granddaughter': 'granddaughter',
    'Son In Law': 'son_in_law',
    'Daughter In Law': 'daughter_in_law',
    'Uncle': 'uncle',
    'Aunt': 'aunt',
    'Nephew': 'nephew',
    'Niece': 'niece',
    'Cousin': 'cousin',
    'Guardian': 'guardian',
    'Stepparent': 'stepparent',
    'Stepson': 'stepson',
    'Stepdaughter': 'stepdaughter',
    'Adopted Child': 'adopted_child',
    'Foster Child': 'foster_child',
    'Sister': 'sister',
    'Brother': 'brother',
    'Brother In Law': 'brother_in_law',
    'Sister In Law': 'sister_in_law',
    'Mother In Law': 'mother_in_law',
    'Father In Law': 'father_in_law',
    'Ward': 'ward',
    'Sponsored Dependent': 'sponsored_dependent',
    'Dependent Minor Dependent': 'dependent_minor_dependent',
    'Ex Spouse': 'ex_spouse',
    'Court Appointed Guardian': 'court_appointed_guardian',
    'Collateral Dependent': 'collateral_dependent',
    'Life Partner': 'life_partner',
    'Annultant': 'annultant',
    'Trustee': 'trustee',
    'Other Relationship': 'other_relationship',
    'Other Relative': 'other_relative'
}

export const reverse = {
    self: 'Self',
    spouse: 'Spouse',
    child: 'Child',
    father: 'Father',
    mother: 'Mother',
    grandfather: 'Grandfather',
    grandmother: 'Grandmother',
    grandson: 'Grandson',
    granddaughter: 'Granddaughter',
    son_in_law: 'Son In Law',
    daughter_in_law: 'Daughter In Law',
    uncle: 'Uncle',
    aunt: 'Aunt',
    nephew: 'Nephew',
    niece: 'Niece',
    cousin: 'Cousin',
    guardian: 'Guardian',
    stepparent: 'Stepparent',
    stepson: 'Stepson',
    stepdaughter: 'Stepdaughter',
    adopted_child: 'Adopted Child',
    foster_child: 'Foster Child',
    sister: 'Sister',
    brother: 'Brother',
    brother_in_law: 'Brother In Law',
    sister_in_law: 'Sister In Law',
    mother_in_law: 'Mother In Law',
    father_in_law: 'Father In Law',
    ward: 'Ward',
    sponsored_dependent: 'Sponsored Dependent',
    dependent_minor_dependent: 'Dependent Minor Dependent',
    ex_spouse: 'Ex Spouse',
    court_appointed_guardian: 'Court Appointed Guardian',
    collateral_dependent: 'Collateral Dependent',
    life_partner: 'Life Partner',
    annultant: 'Annultant',
    trustee: 'Trustee',
    other_relationship: 'Other Relationship',
    other_relative: 'Other Relative'
}


