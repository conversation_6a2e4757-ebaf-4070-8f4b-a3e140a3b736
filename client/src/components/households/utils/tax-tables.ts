import {Household, totalIncomes, totalSource} from 'components/households/utils/income.js';
import {dateDiff} from 'src/utils/date-utils.js'
import { getEmployerContributions, getElectedContributions } from 'components/plans/utils/calcs.js';

const highest = Number.POSITIVE_INFINITY;

export type FilingStatus = 's' | 'ms' | 'hh' | 'mj';
export const taxYear = 2024;
export const filingStatuses = {
    's': 'Single',
    'ms': 'Married Filing Separate',
    'hh': 'Head of Household',
    'mj': 'Married Filing Jointly'
}

export const graduation: { [status: string]: { [key: number]: number } } = {
    s: { // Single Filers
        0: 11925,
        1: 48475,
        2: 103350,
        3: 197300,
        4: 250525,
        5: 626350,
        6: highest
    },
    ms: { // Married Filing Separately
        0: 11925,
        1: 48475,
        2: 103350,
        3: 197300,
        4: 250525,
        5: 375800,
        6: highest
    },
    hh: { // Head of Household
        0: 17000,
        1: 64850,
        2: 103350,
        3: 197300,
        4: 250500,
        5: 626350,
        6: highest
    },
    mj: { // Married Filing Jointly
        0: 23850,
        1: 96950,
        2: 206700,
        3: 394600,
        4: 501050,
        5: 751600,
        6: highest
    }
};

export const taxRates:{ [key:number]: number } = {
    0: .1,
    1: .12,
    2: .22,
    3: .24,
    4: .32,
    5: .35,
    6: .37
}

export const ficaTax = {
    social_security: {
        rate: .124
    },
    medicare: {
        rate: .029
    },
    additional_medicare: {
        rate: .009,
        start: {
            s: 200000,
            ms: 125000,
            hh: 200000,
            mj: 250000
        }
    }
}

export const standardDeduction = {
    s: 14600,
    ms: 14600,
    hh: 21900,
    mj: 29200
}

export const limits = {
    social_security_wage_base: 168600
}

type GetFicaOptions = {
    status?: 'ic' | 'ee',
    filingAs?: FilingStatus
}
export const getPayrollTax = (wageAmount = 0, selfEmployed = 0, { filingAs = 's'}: GetFicaOptions) => {
    const excess = Math.max(0, wageAmount - (ficaTax.additional_medicare.start[filingAs] || 0))
    const ssLimit = Math.min(wageAmount, limits.social_security_wage_base);
    const ss = ssLimit * ficaTax.social_security.rate;
    const med = wageAmount * ficaTax.medicare.rate;
    const social_security_ee = ss * .5;
    const medicare_ee = med * .5;
    const social_security_er = ss * .5;
    const medicare_er = med * .5;
    const additional_medicare = excess * ficaTax.additional_medicare.rate;
    const social_security_se = Math.min(selfEmployed, Math.max(0, ssLimit - limits.social_security_wage_base)) * ficaTax.social_security.rate;
    const medicare_se = selfEmployed * ficaTax.medicare.rate;
    const additional_medicare_se = (Math.max(0, (wageAmount + selfEmployed) - (ficaTax.additional_medicare.start[filingAs])) - excess) * ficaTax.additional_medicare.rate;
    const eeTotal = social_security_ee + medicare_ee + additional_medicare;
    const seTotal = social_security_se + medicare_se + additional_medicare_se;
    const erTotal = social_security_er + medicare_er;
    return {
        total: erTotal + eeTotal + seTotal,
        ee: {
            total: eeTotal,
            social_security: social_security_ee,
            medicare: medicare_ee,
            additional_medicare
        },
        er: {
            total: erTotal,
            social_security: social_security_er,
            medicare: medicare_er
        },
        se: {
            total: seTotal,
            social_security: social_security_se,
            medicare: medicare_se,
            additional_medicare: additional_medicare_se
        }
    }
}

type NumObj = {[key:string]:number};
export const childTaxCredit = ({ magi }: NumObj, household:Household):number => {
    const limit = household?.filingAs === 'mj' ? 400000 : 200000;
    let total = 0;
    if(magi <= limit){
        for(const k in household.members || {}){
            const member = household.members[k];
            if(member.dob && member.dependent){
                const eoyAge = member.age || dateDiff(new Date(taxYear, 11, 31), member.dob);
                if(eoyAge < 17) total += 2000;
            }
        }
    }
    return total;
}

export const earnedIncomeCredit = ({ agi, investment }:NumObj, household:Household) => {
    if(investment > 11000) return 0;
    const deps = Object.keys(household.members || {}).filter(a => household.members[a].dependent).length || 0;
    //limits [allOthers, marriedFJointly, amount];
    const limits:{[key:number]:[number,number,number]} = {
        0: [18591,25511,632],
        1: [49084,56004,4213],
        2:[55768,62688,6960],
        3:[59899,66819,7830]
    }
    const idx = household?.filingAs === 'mj' ? 1 : 0;
    const arr = limits[deps] || [0,0,0];
    if(arr[idx] <= agi) return 0;
    else return arr[2];
}

type TaxOptions = {
    deductions?:{ [key:string]: { amount:number } },
    plan?:any,
    enrollment?:any
}

type QuickOptions = {income:number, filing_as?:'s'|'ms'|'hh'|'mj', hh_members?: { [key:string]: { dob?:string, age?:number, dependent?:boolean } }, pretax_ee?:number, pretax_er?:number, passive_inc?:number }
export const quickTaxTotal = ({
                                  income,
                                  hh_members,
                                  pretax_ee,
                                  // pretax_er,
                                  // passive_inc,
                                  filing_as
}:QuickOptions) => {
    const fa = filing_as || 's'
    const ee = pretax_ee || 0;
    // const er = pretax_er || 0;
    const pyTax = getPayrollTax(income - ee, 0, { filingAs: fa })
    const sd = standardDeduction[fa];
    const agi = income - ee - sd
    let tax = 0;
    let last = 0;
    const brackets:{[key:number|string]: number} = graduation[fa] as { [key:number|string]: number }
    for(const k in Object.keys(brackets)){
        const bracket = brackets[k];
        if(agi >= bracket){
            tax += ((bracket - last) * taxRates[k])
            last = bracket;
        } else {
            tax += ((agi - last) * taxRates[k]);
            break;
        }
    }
    if(tax < 0) tax = 0;
    const incomeObj = { agi, magi:agi, investment: 0 };
    const credits:{[key:string]:number} = {
        child_tax_credit: childTaxCredit(incomeObj, { members:hh_members } as any),
        earned_income_credit: earnedIncomeCredit(incomeObj, { members:hh_members } as any)
    };
    let adjustedTax = tax;
    let creditTotal = 0;
    for(const cred in credits){
        creditTotal += (credits[cred] || 0);
    }
    adjustedTax -= creditTotal;
    return { magi:agi, agi, adjustedTax, tax, payrollTax:pyTax, credits, creditTotal }
}

export const taxBreakdown = (household: Household, { deductions, plan, enrollment }:TaxOptions) => {
    const {selfEmployed, total, sources, incomes, wages, investment} = totalIncomes(household);

    //Calculate taxable benefits if there are any passed
    let pretaxBenefits = 0;
    let contributions = 0;
    let taxableBenefits = 0;
    let erBenefits = 0;

    if(plan && enrollment?.person) {
        const subSource:any = { total: 0, selfEmployed: 0, investment: 0, wages: 0 };
        const incomes = household._fastjoin?.income_sources || {};
        for(const cam of household._fastjoin?.cams || []){
            const src = incomes[cam._id];
            if(src){
                const totes:any = totalSource(src);
                for(const k in totes){
                    subSource[k] += totes[k];
                }
            }
        }
        if(subSource.total) {
            contributions = (getElectedContributions(enrollment)?.total || 0) * 12
            erBenefits = (getEmployerContributions(plan, {enrollment, totalIncome: Math.max(0, subSource.total - (subSource.selfEmployed || 0)), baseIncome: subSource.wages, contributions}) || 0) * 12;
            const taxableCafe = (enrollment.cafe || {'cash': { amount: 0 }})['cash'];
            if(!taxableCafe?.optOut && taxableCafe?.amount) taxableBenefits = taxableCafe?.amount * 12;
            //employee contributions only
            contributions = contributions - erBenefits;
            //any pretax employer benefit (not used on taxable)
            const taxableRemainder = taxableBenefits - erBenefits;
            //any eeContributions needed to make up taxable benefits
            if(taxableRemainder > 0) contributions = contributions - taxableRemainder;
            //erBenefits was greater than taxable - remainder is pretax;
            else pretaxBenefits = taxableRemainder * -1;
        }
    }

    const filingAs = household?.filingAs || 's';
    const payrollTax = getPayrollTax(wages - (isNaN(contributions) ? 0 : contributions || 0), selfEmployed, { filingAs: household?.filingAs || 's' });
    const deds:any = {
        standard: { amount: standardDeduction[filingAs] },
        SECA: { atl: true, amount: (payrollTax.se.social_security + payrollTax.se.medicare)/2 },
        ...household?.deductions || {},
        ...deductions
    }

    let taxable = (total || 0) + taxableBenefits - (contributions + pretaxBenefits);
    let agi = taxable;
    for(const k in deds){
        const amt = deds[k].off ? 0 : Math.max(0, Math.min(agi, deds[k].amount || 0));
        taxable -= amt;
        if(deds[k].atl) agi -= amt;
    }

    let tax = 0;
    let last = 0;
    const brackets:{[key:number|string]: number} = graduation[household?.filingAs || 's'] as { [key:number|string]: number }
    for(const k in Object.keys(brackets)){
        const bracket = brackets[k];
        if(taxable >= bracket){
            tax += ((bracket - last) * taxRates[k])
            last = bracket;
        } else {
            tax += ((taxable - last) * taxRates[k]);
            break;
        }
    }
    if(tax < 0) tax = 0;
    const magi = agi + (deds.SECA?.amount || 0)
    const incomeObj = { magi, agi, investment };
    const credits:{[key:string]:number} = {
        child_tax_credit: childTaxCredit(incomeObj, (household || {}) as any),
        earned_income_credit: earnedIncomeCredit(incomeObj, (household || {}) as any)
    };
    let adjustedTax = tax;
    let creditTotal = 0;
    for(const cred in credits){
        creditTotal += (credits[cred] || 0);
    }
    adjustedTax -= creditTotal;
    return {
        taxable,
        magi,
        deductions: deds,
        agi,
        tax,
        adjustedTax,
        creditTotal,
        credits,
        payrollTax,
        selfEmployed,
        total,
        incomes,
        sources,
        wages,
        investment,
        taxableBenefits,
        pretaxBenefits,
        contributions,
        erBenefits
    }
}
