<template>
  <div class="_fw">
    <div class="row">
      <q-tabs indicator-color="accent" align="left" v-model="tab" no-caps>
        <q-tab name="plans" label="Plans"></q-tab>
        <q-tab name="bundles" label="Bundles"></q-tab>
      </q-tabs>
    </div>

    <q-tab-panels class="_panel" v-model="tab" animated>
      <q-tab-panel class="_panel" name="plan">
        <table>
          <tr>
            <th>Company</th>
            <th>Plan</th>
            <th>Members</th>
            <th>Approve</th>
          </tr>
          <tr v-for="(p, i) in p$.data" :key="`plan-${i}`">
            <td>
              <org-chip :model-value="p._fastjoin?.org || p.org"></org-chip>
            </td>
            <td>{{ p.name }}</td>
            <td>{{ dollarString(p.estFte, '', 0) }}</td>
            <td>
              <q-btn dense flat icon="mdi-check-circle"></q-btn>
            </td>
          </tr>
        </table>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="bundles">
        <div class="row">
          <div v-for="(pb, i) in pb$.data" :key="`pb-${i}`" class="col-12 col-md-4 q-pa-sm">
            <div class="__c">
              <bundle-card :model-value="pb"></bundle-card>
            </div>
          </div>
        </div>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup>
  import OrgChip from 'components/orgs/cards/OrgChip.vue';
  import BundleCard from 'components/providers/bundles/cards/BundleCard.vue';

  import {computed, ref} from 'vue';
  import {dollarString} from 'src/utils/global-methods';
  import {HFind} from 'src/utils/hFind';
  import {usePlans} from 'stores/plans';
  import {useBundles} from 'stores/bundles';

  const planStore = usePlans();
  const pbStore = useBundles();

  const props = defineProps({
    network: { required: true }
  })

  const tab = ref('plans');

  const { h$:p$ } = HFind({
    store: planStore,
    pause: computed(() => tab.value !== 'plans'),
    params: computed(() => {
      return {
        query: {
          _id: { $in: props.network?.plan_reqs || [] }
        }
      }
    })
  })
  const { h$:pb$ } = HFind({
    store: pbStore,
    pause: computed(() => tab.value !== 'bundles'),
    params: computed(() => {
      return {
        query: {
          _id: { $in: props.network?.bundle_reqs || [] }
        }
      }
    })
  })
</script>

<style lang="scss" scoped>
  table {
    width: 100%;

    tr {
      th {
        font-weight: bold;
        color: #999;
        font-size: .75rem;
      }
      td {
        font-size: .9rem;
        padding: 5px 8px;
        border-bottom: solid .3px #999
      }
    }
    tr:last-child {
      td {
        border-bottom: none;
      }
    }
  }
</style>
