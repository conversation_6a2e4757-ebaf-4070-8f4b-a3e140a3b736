<template>
  <div class="_fw">
    <div class="row q-py-sm" v-if="canEdit?.ok">
      <q-btn no-caps flat @click="dialog = true">
        <span class="tw-six font-3-4r text-grey-7">Add Bundle</span>
        <q-icon class="q-ml-sm" color="accent" name="mdi-plus"></q-icon>
      </q-btn>
    </div>

    <q-tab-panels class="_panel" :model-value="!!viewing" animated>
      <q-tab-panel class="_panel" :name="false">
        <div class="row q-px-md q-pb-md">
          <q-input class="w600 mw100" filled v-model="search.text">
            <template v-slot:prepend>
              <q-icon name="mdi-magnify"></q-icon>
            </template>
          </q-input>
        </div>
        <div v-if="!p$.data.length" class="q-pa-md text-italic font-1r">No Bundles Added</div>
        <div class="row">
          <div class="col-12 col-md-4 q-pa-sm" v-for="(p, i) in p$.data" :key="`p-${i}`">
            <div class="__c" @click="viewing = p">
              <bundle-card :model-value="p"></bundle-card>
            </div>
          </div>
        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" :name="true">
        <div class="row">
          <q-btn flat icon="mdi-chevron-left" color="accent" @click="viewing = undefined"></q-btn>
        </div>
        <bundle-page :model-value="viewing"></bundle-page>
        <div class="row justify-end q-py-md" v-if="canEdit?.ok">
          <remove-proxy @remove="remove(viewing)" :name="`${viewing.name} from ${network.name}`"></remove-proxy>
        </div>
      </q-tab-panel>
    </q-tab-panels>


    <common-dialog setting="right" v-model="dialog">
      <div class="q-pa-md bg-white">
        <bundle-form :network="network"></bundle-form>
      </div>
    </common-dialog>
  </div>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import BundleForm from 'components/providers/bundles/forms/BundleForm.vue';
  import BundleCard from 'components/providers/bundles/cards/BundleCard.vue';
  import BundlePage from 'components/providers/bundles/pages/BundlePage.vue';
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';

  import {computed, ref} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {useBundles} from 'stores/bundles';
  import {idGet} from 'src/utils/id-get';
  import {useNetworks} from 'stores/networks';
  import {useRoute, useRouter} from 'vue-router';
  import {$errNotify} from 'src/utils/global-methods';


  const pbStore = useBundles();
  const networkStore = useNetworks();
  const route = useRoute();
  const router = useRouter();

  const props = defineProps({
    canEdit: Object
  })

  const viewing = ref(undefined);

  const { item: network } = idGet({
    store: networkStore,
    value: computed(() => route.params.networkId),
    params: ref({ runJoin: { sync_bundles: true, sync_plans: true } })
  })

  const dialog = ref(false);

  const query = computed(() => {
    return {}
  })

  const { search, searchQ } = HQuery({ query });

  const { h$: p$ } = HFind({
    store: pbStore,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          _id: { $in: network.value?.bundles || [] }
        }
      }
    })
  })

  const remove = async (val) => {
    await pbStore.patch(val._id, { $pull: { networks: network.value._id } })
        .catch(err => {
          $errNotify(`Error removing: ${err.message}`)
          console.error(`Error removing bundle: ${err.message}`)
        })
    // console.log('removing bundle', val._id, network.value._id);
    await networkStore.patch(network.value._id, { $pull: { bundles: val._id } })
        .catch(err => {
          $errNotify(`Error removing: ${err.message}`)
          console.error(`Error removing bundle: ${err.message}`)
        })
    router.go()
  }

</script>

<style lang="scss" scoped>
  .__c {
    border-radius: 15px;
    background: white;
    border: solid 2px var(--q-ir-grey-4);
    margin: 10px 0;
    padding: 20px 15px;
    transform: none;
    transition: all .2s;
    cursor: pointer;

    &:hover {
      transform: translate(0, -2px);
    }
  }
</style>
