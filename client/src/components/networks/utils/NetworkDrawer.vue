<template>
  <div class="_fw">
    <q-expansion-item
        :dark="dark"
        :default-opened="link.on"
        v-for="(link, i) in links"
        :key="`link-${i}`"
        hide-expand-icon
        dense
        style="padding: 0"
        :class="`bg-${link.on ? activeBg : 'transparent'}`"
        @update:model-value="openExp($event, link)"
        :model-value="link.on"
    >
      <template v-slot:header>
        <q-item class="_fw">
          <q-item-section avatar>
            <q-icon :name="link.icon"></q-icon>
          </q-item-section>
          <q-item-section>
            <q-item-label :class="`tw-six ${link.on ? ` text-${activeText}` : ''}`">{{ link.label }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </template>

      <q-list v-for="(item, idx) in Object.keys(link.items)" :key="`item-${i}-${idx}`">
        <!--                <div class="q-pl-lg q-py-sm text-p7 font-3-4r tw-six" header v-if="item?.length">{{ item }}</div>-->
        <template
            v-for="(sub, index) in Object.keys(link.items[item])"
            :key="`sub-${i}-${idx}-${index}`">
          <q-item
              :inset-level=".5"
              clickable
              @click="$router.push(link.items[item][sub].link?.route)">
            <q-item-section>
              <q-item-label :class="`text-${link.items[item][sub].on ? activeText + ' tw-six' : ''}`">
                {{ link.items[item][sub].label || sub }}
              </q-item-label>
            </q-item-section>
          </q-item>
          <q-list separator v-if="link.items[item][sub].on">
            <q-item
                :inset-level=".25" v-for="(subub, ii) in link.items[item][sub].subs || []"
                :key="`subub-${i}${idx}${index}${ii}`"
                clickable
                @click="$router.push(subub.link.route)"
            >
              <q-item-section avatar>
                <q-avatar v-if="subub.on" size="15px" :color="lightText"></q-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label :class="`tw-five text-${subub.on ? activeText : ''}`">
                  <q-badge class="alt-font tw-five" :text-color="darkText" :color="subub.on ? lightText : offWhiteText">
                    {{ subub.label }}
                  </q-badge>
                </q-item-label>
              </q-item-section>
            </q-item>
          </q-list>

        </template>
      </q-list>
    </q-expansion-item>
  </div>
</template>

<script setup>

  import {networkNav} from 'components/networks/utils/network-nav';
  import {useRouter} from 'vue-router';

  const router = useRouter()
  const { links } = networkNav()

  const props = defineProps({
    dark: Boolean,
    activeBg: {default: 'white'},
    activeText: { default: 'a6'},
    darkText: { default: 'a9'},
    lightText: { default: 'a2'},
    offWhiteText: { default: 'a0'},
  })

  const openExp = (val, link) => {
    if(val) router.push(link.link.route)
    else {
      setTimeout(() => {
        if (link.on) router.push(link.link.route)
      }, 50)
    }
  }
</script>

<style lang="scss" scoped>

</style>
