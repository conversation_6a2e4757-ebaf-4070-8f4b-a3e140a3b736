import {AnyRef} from 'src/utils/types';
import {dollarString} from 'src/utils/global-methods';
import {computed, ref} from 'vue';

/** These also exist on the server - updates must by synced */
export const censusIndexByKey: { [key: string]: number } = {
    'ndc': 0,
    'price': 1,
    'name': 2,
    's_f': 3,
    'description': 4,
    // 'providerName': 5,
    // 'rxcui': 6,
    // 'code': 7
}


type ReqOptions = { priceUpload?:AnyRef<boolean>, ndcVersion?:AnyRef<10|11> }
export const getReqs = ({priceUpload, ndcVersion}:ReqOptions = {}) => {
    const csvData = ref([[]])
    const parsedFile = ref([[]])
    const errs = ref({});
    const reqs = computed(() =>  [
        {
            required: true,
            results: true,
            key: 'ndc',
            label: 'NDC',
            tooltip: 'NDC code for this drug',
            ex: '**********',
            def: undefined,
            format: (val) => val?.split('-').join('').trim() || undefined,
            rev: (val) => {
                    if(!val) return;
                    const ndc = ndcVersion?.value || 11;

                    val = String(val).replace(/\D/g, ''); // Remove any non-numeric characters
                    const l = val.length;
                    if (l < 11) {
                        // Pad to 10 digits if requested format is NDC 10
                        if (ndc === 10) {
                            val = val.padStart(10, '0');
                        }
                        // Pad to 11 digits if requested format is NDC 11
                        else if (ndc === 11) {
                            val = val.padStart(11, '0');
                        }
                    }
                    return val;
            },
            check: (val, row, col) => {
                const ndc = ndcVersion?.value || 11;
                const key = `${row}-${col}`;
                if (!val) {
                    return errs.value[key] = 'Invalid NDC';
                }

                val = String(val).replace(/\D/g, ''); // Remove any non-numeric characters

                if (/^\d{10}$/.test(val) || /^\d{11}$/.test(val)) {
                    delete errs.value[key]; // Valid NDC format
                    return;
                }

                const l = val.length;

                if (l < 10) {
                    // Pad to 10 digits if requested format is NDC 10
                    if (ndc === 10) {
                        val = val.padStart(10, '0');
                    }
                    // Pad to 11 digits if requested format is NDC 11
                    else if (ndc === 11) {
                        val = val.padStart(11, '0');
                    }
                }

                // Final validation check after padding
                if ((ndc === 10 && /^\d{10}$/.test(val)) || (ndc === 11 && /^\d{11}$/.test(val))) {
                    delete errs.value[key]; // Valid NDC
                } else {
                    errs.value[key] = 'Invalid NDC';
                }
            }
        },
        {
            required: priceUpload?.value || false,
            results: true,
            key: 'price',
            label: 'Price',
            tooltip: 'Enter dollars/cents ex: $10.15',
            ex: '10.15',
            def: undefined,
            rev: (val) => {
                if(!val) return undefined
                return Number(val.replace(/[^\d.]/g, '') || 0) || undefined;
            },
            format: (val) => dollarString(val),
            check: (v, row, col) => {
                const val = Number(v.replace(/[^\d.]/g, '') || 0)
                const key = `${row}-${col}`
                if (typeof val !== 'number' || val < 0 || val > 120) errs.value[key] = 'Invalid Price'
                else delete errs.value[key]
            }
        },
        {
            results: true,
            required: false,
            key: 'name',
            label: 'Name',
            tooltip: 'Drug Name',
            ex: 'Tylenol',
            def: undefined,
            format: (val) => {
                return val?.trim() || undefined
            },
            rev: (val) => val || undefined,
            check: (val, row, col) => {
                const key = `${row}-${col}`
                if (!val) errs.value[key] = 'Empty drug name';
                else delete errs.value[key]
            }
        },
        {
            results: true,
            required: false,
            key: 's_f',
            label: 'Strength/Form',
            tooltip: 'ex: 10mg tabs',
            ex: '10mg tabs',
            def: undefined,
            format: (val) => val?.trim() || undefined,
            rev: (val) => val || undefined,
            check: () => {
                return
            }
        },
        {
            required: false,
            key: 'description',
            label: 'Generic Description',
            tooltip: 'ex: Acetaminophen',
            ex: 'Acetaminophen',
            def: undefined,
            format: (val) => val?.trim() || undefined,
            rev: (val) => val?.trim() || undefined,
            check: () => ''
        }
        // {
        //     required: false,
        //     key: 'providerName',
        //     label: 'Mfr/Provider',
        //     tooltip: 'Who is selling at this price?',
        //     ex: 'Johnson & Johnson',
        //     def: undefined,
        //     format: (val) => val?.trim() || undefined,
        //     rev: (val) => val?.trim() || undefined,
        //     check: () => ''
        // },
        // {
        //     required: false,
        //     key: 'rxcui',
        //     label: 'RXCUI',
        //     tooltip: 'Drug RXCUI code',
        //     ex: '209387',
        //     def: undefined,
        //     format: (val) => val?.trim() || undefined,
        //     rev: (val) => val || undefined,
        //     check: () => {
        //         return
        //     }
        // },
        // {
        //     required: false,
        //     key: 'code',
        //     label: 'Procedure Code',
        //     tooltip: 'Some drugs have a procedure code',
        //     ex: 'J0131',
        //     def: undefined,
        //     format: (val) => val?.trim() || undefined,
        //     rev: (val) => val || undefined,
        //     check: () => {
        //         return
        //     }
        // }
    ].sort(((a, b) => censusIndexByKey[a.key] - censusIndexByKey[b.key])))
    return {
        errs,
        reqs,
        csvData,
        parsedFile
    }
}


export const toList = (ex: Array<any[]>, reqs:Array<any>) => {
    if (!ex?.length) return [];
    const census: any = [];
    for (let i = 0; i < ex.length; i++) {
        const arr:any = []
        const arr2:any[] = ex[i] || [];
        for (let idx = 0; idx < arr2.length; idx++) {
            let v = arr2[idx];
            if (!v && v !== 0) v = ''
            arr.push(reqs[idx].rev(v));
        }
        census.push(arr);
    }
    return census;
}

