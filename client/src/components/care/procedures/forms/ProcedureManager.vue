<template>
  <div class="_fw">

    <template v-if="Object.keys(modelValue || {}).length">
      <procedure-table
          :model-value="modelValue"
          @update:model-value="emit('update:model-value', $event)"
      ></procedure-table>
    </template>
    <q-item dense class="_fw" clickable @click="adding = true">
      <q-item-section>
        <q-item-label class="tw-five text-grey-6">
          <div class="flex items-center">
            <q-icon name="mdi-plus" color="primary" class="q-mr-sm"></q-icon>
            <div>Add Procedure</div>
          </div>
        </q-item-label>
      </q-item-section>
    </q-item>


    <q-slide-transition>
      <div class="_fw mw600" v-if="adding || !modelValue">
        <procedure-autocomplete v-bind="inputAttrs" @update:model-value="add"></procedure-autocomplete>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup>
  import ProcedureAutocomplete from 'components/care/procedures/lists/ProcedureAutocomplete.vue';
  import ProcedureTable from 'components/care/procedures/lists/ProcedureTable.vue';

  import {ref} from 'vue';
  import {loginPerson} from 'stores/utils/login';
  const { person } = loginPerson()

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: Object,
    inputAttrs: Object
  })

  const adding = ref(false);

  const add = (val) => {
    const {
      name, code, standard, _id, layName,
      layDescription,
      descriptions,
      description,
      parent,
    } = val;
    if (!(props.modelValue || { [val._id]: false })[val._id]) {
      const obj = {
        id: _id,
        name,
        code,
        layName,
        layDescription: (descriptions || [])[0] || layDescription,
        parent,
        description,
        standard,
        loggedBy: person.value._id,
        loggedAt: new Date()
      }
      emit('update:model-value', { ...props.modelValue || {}, [val._id]: obj });
    }
  }
</script>

<style lang="scss" scoped>

</style>
