<template>
  <div class="q-pa-md _fw mw400">
    <template v-if="proc">
      <div class="font-3-4r tw-six text-white flex items-center">
        <div>{{ (proc.names || [])[0] || proc.layName || proc.name || '' }}</div>
      </div>
      <div class="font-3-4r">{{ (proc.descriptions || [])[0] || proc.layDescription || proc.description || 'No added description' }}</div>
      <div v-if="proc.loggedBy" class="_fw q-pt-sm">
        <div class="font-3-4r bg-ir-grey-1 q-pa-sm" v-if="proc.notes">{{ proc.notes }}</div>
        <div class="row items-center">
          <div class="font-3-4r">By:</div>
          <default-chip :model-value="person"></default-chip>
          <q-chip :label="$ago(proc.loggedAt)"></q-chip>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {useProcedures} from 'stores/procedures';
  import {usePpls} from 'stores/ppls';
  import {$ago} from 'src/utils/global-methods';

  const store = useProcedures();
  const pplStore = usePpls();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: proc } = idGet({
    store,
    idPath: 'id',
    value: computed(() => props.modelValue)
  })

  const parent = computed(() => {
    const spl = (proc.value?.parent || '').split(';');
    return spl[spl.length - 2]
  })

  const { item:person } = idGet({
    store: pplStore,
    value: computed(() => proc.value?.loggedBy)
  })
</script>

<style lang="scss" scoped>

</style>
