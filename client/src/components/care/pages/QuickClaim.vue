<template>
  <q-page class="bg-p0">
    <plan-select-top></plan-select-top>
    <div class="row justify-center">
      <div class="_xsent pd2 pw1 mnh80 relative-position">
        <div class="__c pd5 pw2">
          <div class="row justify-end">
            <q-btn v-if="visit?._id && (Object.keys(visit.files || {}).length || visit.claims?.length)" class="_inp"
                   flat no-caps @click="$router.go()">
              <span class="q-mr-sm">Enter Another Bill</span>
              <q-icon color="primary" name="mdi-reload"></q-icon>
            </q-btn>
          </div>
          <template v-if="person?._id">
            <div v-if="!planId" class="__cover pd5 text-center">
              Select Plan First
            </div>
            <div class="q-pa-sm font-1-1-8r tw-eight text-primary">Add Medical Bills</div>
            <div class="_f_l _f_chip">Patient</div>
            <div class="q-pa-sm">
              <hh-member-select
                  auto-close
                  :model-value="care.patient"
                  emit-value
                  :person="person"
                  @update:model-value="addPatient"
              >
              </hh-member-select>
            </div>


            <q-slide-transition>
              <div class="_fw" v-if="care?._id">
                <div class="_f_l _f_chip">Add Bills</div>
                <claim-request-form :visit="visit" :plan-id="planId" :patient-id="care?.patient"
                                    :person-id="care?.person"></claim-request-form>

                <div class="_f_l _f_chip">Details</div>
                <div class="_form_grid q-py-sm">
                  <div class="_form_label">Preventive</div>
                  <div class="q-pa-sm">
                    <q-checkbox label="Visit was a preventive care visit" :model-value="!!visit.preventive"
                                @update:model-value="setPreventive"></q-checkbox>
                  </div>
                  <div class="_form_label">Provider</div>
                  <div class="q-pa-sm q-pt-sm">
                    <provider-select-box
                        :query="providerQuery"
                        @update:model-value="setProvider"
                        :model-value="visit.provider"></provider-select-box>
                  </div>

                  <div class="_form_label">Date</div>
                  <div class="q-pa-sm">
                    <inline-date :input-attrs="{ class: '_inp' }" :model-value="visit.date"
                                 @update:model-value="setDate"></inline-date>
                  </div>

                  <div class="_form_label">Condition</div>
                  <div class="q-pa-sm">
                    <conditions-manager :input-attrs="{ selectClass: '_inp' }"
                                        v-model="care.conditions"
                                        @update:model-value="careAutoSave('conditions')"></conditions-manager>
                  </div>
                  <template v-if="visit?._id">
                    <div class="_form_label">Line Items</div>
                    <div class="q-pa-sm">
                      <claims-list adding :visit="visit"></claims-list>
                    </div>
                  </template>
                </div>
              </div>
            </q-slide-transition>
          </template>
          <template v-else>
            <auth-card bordered v-if="!loading"></auth-card>
            <div class="row justify-center" v-else>
              <q-spinner size="40px" color="primary"></q-spinner>
            </div>
          </template>
        </div>
        <q-slide-transition>
        <div v-if="care._id" class="__c pd5 pw2 q-my-md">
          <div class="font1r tw-five">Upload your bill(s) and add any relevant details. Your bill will be processed and appear in your <span class="text-primary tw-six cursor-pointer" @click="$openInNew({ name: 'my-care'}, $router)">care dashboard</span>.</div>
        </div>
        </q-slide-transition>
      </div>
    </div>
  </q-page>
</template>

<script setup>
  import PlanSelectTop from 'components/plans/utils/PlanSelectTop.vue';
  import ProviderSelectBox from 'components/providers/forms/ProviderSelectBox.vue';
  import InlineDate from 'components/common/dates/InlineDate.vue';
  import ConditionsManager from 'components/care/conditions/forms/ConditionsManager.vue';
  import ClaimsList from 'components/claims/lists/ClaimsList.vue';
  import HhMemberSelect from 'components/households/lists/HhMemberSelect.vue';
  import AuthCard from 'components/auth/AuthCard.vue';
  import ClaimRequestForm from 'components/claims/claim-requests/forms/ClaimRequestForm.vue';

  import {computed, onMounted, ref, watch} from 'vue';
  import {loginPerson} from 'stores/utils/login';
  import {idGet} from 'src/utils/id-get';
  import {useHouseholds} from 'stores/households';
  import {HForm, HSave} from 'src/utils/hForm';
  import {useCares} from 'stores/cares';
  import {useVisits} from 'stores/visits';
  import {LocalStorage} from 'symbol-auth-client';
  import {$errNotify, $openInNew} from 'src/utils/global-methods';

  const { person } = loginPerson()

  const hhStore = useHouseholds();
  const careStore = useCares();
  const visitStore = useVisits();

  const careFn = (defs) => {
    return {
      initDate: new Date(),
      ...defs
    }
  }
  const visitFn = (defs) => {
    return {
      date: new Date(),
      ...defs
    }
  }
  const { form: care, save: saveCare } = HForm({
    store: careStore,
    formFn: careFn
  })
  const { autoSave: careAutoSave } = HSave({ form: care, store: careStore })

  const { form: visit, save: saveVisit } = HForm({
    store: visitStore,
    formFn: visitFn
  })
  const { autoSave: visitAutoSave } = HSave({
    form: visit,
    store: visitStore,
    pause: computed(() => !visit.value?.provider)
  })

  const planId = computed(() => LocalStorage.getItem('plan_id'));
  const addPatient = async (val) => {
    care.value.patient = val;
    care.value.plan = planId.value;
    if (!care.value.plan) $errNotify('Select Health Plan');
    care.value.person = person.value._id;
    const c = await saveCare()
    if (c) visit.value.care = c._id;
  }

  const setPreventive = (val) => {
    visit.value.preventive = val;
    care.value.preventive = val;
    saveVisit()
    careAutoSave('preventive')
  }

  const setProvider = (val) => {
    if (val) {
      visit.value.provider = val._id;
      care.value.providers = [...care.value.providers || [], val._id]
      saveVisit()
      careAutoSave('providers');
    }
  }
  const setDate = (val) => {
    visit.value.date = val;
    care.value.initDate = val;
    careAutoSave('initDate');
    visitAutoSave('date');
  }

  const { item: hh } = idGet({
    store: hhStore,
    value: computed(() => person.value?.household)
  })
  const providerQuery = computed(() => {
    return { _id: { $in: hh.value?.providers || [] } }
  })

  const loading = ref(true);
  watch(person, (nv) => {
    if (nv?._id) loading.value = false;
  }, { immediate: true })
  onMounted(() => {
    setTimeout(() => {
      loading.value = false;
    }, 2000)
  })

</script>

<style lang="scss" scoped>
  .__cover {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, .1);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 40;
    font-size: 1.2rem;
    font-weight: bold;
  }

  .__c {
    border-radius: 25px;
    box-shadow: 0 4px 24px -10px rgba(0, 0, 0, .3);
    background: white;
  }
</style>
