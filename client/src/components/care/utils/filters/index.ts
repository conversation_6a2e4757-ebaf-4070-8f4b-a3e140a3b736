import StatusFilter from 'components/care/utils/filters/components/StatusFilter.vue';
import PeopleFilter from 'components/care/utils/filters/components/PeopleFilter.vue';
import PriorityFilter from 'components/care/utils/filters/components/PriorityFilter.vue';
import ProviderFilter from 'components/care/utils/filters/components/ProviderFilter.vue';
import PractitionerFilter from 'components/care/utils/filters/components/PractitionerFilter.vue';
import ConditionsFilter from 'components/care/utils/filters/components/ConditionsFilter.vue';
import ProceduresFilter from 'components/care/utils/filters/components/ProceduresFilter.vue';
import MedsFilter from 'components/care/utils/filters/components/MedsFilter.vue';

import {computed, Ref, ComputedRef, ref} from 'vue';
import {AnyObj} from 'src/utils/types.js';

const arrayOr = (val:any) => {
    if (Array.isArray(val)) return {$in: val};
    return val;
}
const alwaysIn = (val:any) => {
    if (Array.isArray(val)) return {$in: val};
    else return {$in: [val]}
}

const allKeys = {
    cares: ['status', 'patientPriority', 'planPriority', 'providerPriority', 'providers', 'practitioners', 'person', 'conditions'],
    claims: ['status', 'provider', 'practitioner', 'person', 'med', 'procedure'],
}

type Opts = { emit: (path:string, val: any) => void, set: 'cares'|'claims'}
type Filter = { [key: string]: any };
type FilterConfig =  { [key:string]: {
        label: string,
        singular?: string,
        plural?: string,
        count?: number,
        component: any,
        attrs?: any,
        fn: (v:any) => any
    }}
export const careFilters = (query: Ref<AnyObj> | ComputedRef<AnyObj>, options: Opts) => {
    const set = options?.set || 'cares';
    const keys = allKeys[set];
    const filters: Ref<Filter> = ref({})

    const filterConfig:ComputedRef<FilterConfig> = computed(() => {
        const obj: FilterConfig = {
            patient: {label: 'Person', component: PeopleFilter, attrs: {selectAttrs: {class: '', borderless: false}}, fn: arrayOr},
            status: {label: 'Status', attrs: { set }, component: StatusFilter, fn: arrayOr},
            patientPriority: {label: 'Priority', component: PriorityFilter, fn: arrayOr},
            providers: {label: 'Provider', component: ProviderFilter, fn: alwaysIn},
            provider: {label: 'Provider', component: ProviderFilter, fn: arrayOr},
            practitioners: {label: 'Practitioner', component: PractitionerFilter, fn: alwaysIn},
            practitioner: {label: 'Practitioner', component: PractitionerFilter, fn: arrayOr},
            conditions: {label: 'Condition', component: ConditionsFilter, fn: arrayOr},
            procedure: { label: 'Procedures', component: ProceduresFilter, fn:arrayOr },
            med: { label: 'Meds', component: MedsFilter, fn:arrayOr },
            date: { label: 'Date', hide: true }
        }
        const picked:FilterConfig = {};
        for (const k of Object.keys(obj).filter(a => keys.includes(a))) {
            const val = filters.value[k];
            if (Array.isArray(val)) {
                obj[k].count = val.length;
            } else if (val && val !== '*') {
                obj[k].count = 1;
            }
            picked[k] = obj[k]
        }
        return picked;
    })

    const getFilter = (path: string, val: any) => {

        return filterConfig.value[path].fn(val)
    }
    const q = computed(() => {
        const qry: any = {...query.value};
        for (const k in filters.value) {
            const v = filters.value[k];
            if (Array.isArray(v) ? v.length > 0 : typeof v !== 'undefined') qry[k] = getFilter(k, filters.value[k] as any)
            else delete qry[k];
        }
        return qry;
    })

    const dirty = ref(false);

    const clearAll = () => {
        for (const k in filters.value) {
            filters.value[k] = undefined;
        }
        dirty.value = false;
    }

    const setFilter = (path: string, val: any) => {
        dirty.value = true;
        filters.value[path] = val;
        if(options?.emit) options.emit(path, q.value)
    }
    return {
        setFilter,
        dirty,
        clearAll,
        filterConfig,
        q,
        filters,
        getFilter
    }
}
