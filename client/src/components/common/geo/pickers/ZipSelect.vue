<template>
  <q-select
      v-bind="{ useInput: true, placeholder: !modelValue ? 'Choose Zip Code' : undefined, modelValue: !!modelValue ? modelValue : undefined, options:useZips, ...$attrs }"
      @input-value="setZipSearch" @update:model-value="setZip" @keyup.enter="checkZip">
    <template v-slot:selected-item v-if="$slots['selected-item']">
      <slot name="selected-item"></slot>
    </template>
    <template v-slot:prepend v-if="$slots['prepend']">
      <slot name="prepend">
      </slot>
    </template>
    <template v-slot:append v-if="zipSearch?.length === 5 && zipSearch !== modelValue">
      <q-spinner v-if="zipLoading" color="primary"></q-spinner>
      <q-btn v-else dense flat size="sm" push icon="mdi-check" color="green" @click="checkZip"></q-btn>
    </template>
    <template v-slot:after-options>
      <q-item v-if="moreZips" clickable @click="zipNext">
        <q-item-section>
          <q-item-label>Load More...</q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </q-select>
</template>

<script setup>
  import {geoPickerUtils} from 'components/common/geo/pickers/utils';
  import {computed} from 'vue';

  const emit = defineEmits(['update:model-value', 'zip']);
  const props = defineProps({
    state: { required: false },
    modelValue: String
  })

  const emitUp = (evt, ...args) => {
    const emits = {
      'zip': () => emit('update:model-value', ...args),
      'zip-data': () => emit('zip-data', ...args),
      'state': () => emit('state', ...args),
      'five': () => emit('five', ...args)
    }
    if (emits[evt]) emits[evt]();
  }

  const {
    checkZip,
    zipSearch,
    setZipSearch,
    moreZips,
    zipNext,
    zipLoading,
    useZips,
    setZip
  } = geoPickerUtils({ emit: emitUp, state: computed(() => props.state) })


</script>

<style lang="scss" scoped>

</style>
