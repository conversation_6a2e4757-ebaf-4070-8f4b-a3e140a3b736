<template>
  <div class="q-pa-sm flex items-center"
       @mouseover="hover = true"
       @mouseleave="hover = false"
       @touchstart="hover = true"
       @touchend="hover = false"
       :style="{ background: active ? '#eeeeee' : null, transition: 'all .15s ease-out', width: '100%' }"
  >
    <div style="width: 20%">
      <div class="row justify-center">
        <div class="__color_card" :style="{ background: modelValue ? modelValue : hex }"></div>
      </div>
    </div>
    <div style="width: 40%" class="q-px-xs">
      <q-input dense outlined hide-bottom-space
               input-class="text-center text-xxs text-mb-xxs text-weight-bold text-ir-grey-8" v-model="hex"
               @input="setHex"></q-input>
    </div>
    <div style="width: 20%" v-if="gradient" class="q-px-xs">
      <q-input dense outlined hide-bottom-space
               input-class="text-center text-xxs text-mb-xxs text-weight-bold text-ir-grey-8" v-model="stop"
               @input="$emit('stop', stop)"></q-input>
    </div>
    <div style="width: 20%" class="q-px-xs">
      <slot name="side">
        <div class="row justify-center">
          <q-btn dense flat icon="mdi-close" color="ir-grey-6" @click="$emit('close', modelValue)"></q-btn>
        </div>
      </slot>
    </div>
  </div>
</template>

<script>
  import {colors} from 'quasar';

  export default {
    name: 'HexRow',
    props: {
      gradient: Boolean,
      modelValue: String,
      on: Boolean,
      stopIn: Number
    },
    data(){
      return {
        stop: 0,
        hex: '#FF0000',
        hover: false
      };
    },
    watch: {
      stopIn: {
        immediate: true,
        handler(newVal){
          if(newVal){
            this.stop = newVal;
          }
        }
      },
      modelValue: {
        immediate: true,
        handler(newVal) {
          if(newVal && typeof newVal === 'string') {
            if (newVal.indexOf('rgb') > -1) {
              let rgbObj = colors.textToRgb(newVal);
              this.hex = colors.rgbToHex(rgbObj);
              // this.$emit('update:model-value', this.hex);
            } else {
              if (newVal.indexOf('#') > -1) {
                this.hex = newVal;
              } else {
                this.hex = '#' + newVal;
              }
            }
          }
        }
      }
    },
    computed: {
      active(){
        return this.on ? true : this.hover;
      }
    },
    methods: {
      setHex(val) {
        let v = val;
        let hash = val.indexOf('#');
        if (v && !(hash > -1)) {
          v = '#' + val;
        }
        this.hex = v;
        if (v && v.charAt(6)) {
          this.$emit('update:model-value', v);
        }
      },
    }
  };
</script>

<style scoped lang="scss">
  .__color_card {
    height: 40px;
    width: 40px;
    max-width: 95%;
    border-radius: 5px;
    box-shadow: 0 0 4px rgba(0, 0, 0, .15);
  }
</style>
