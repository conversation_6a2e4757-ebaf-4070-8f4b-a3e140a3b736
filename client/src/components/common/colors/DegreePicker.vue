<template>
  <div id="rotator_cont" :style="{boxShadow: '0 0 10px rgba(0,0,0,.2)'}">
    <div id="rotate"
         @pointerdown="startRotate($event)"
         @touchstart="startRotate($event)"
         @mousedown="startRotate($event)"
    ></div>
<!--    <div id="drag" @pointerdown.stop="$emit('close')" @click.stop="$emit('close')">-->
<!--      <slot></slot>-->
<!--    </div>-->
  </div>
</template>

<script>
  export default {
    name: 'DegreePicker',
    props: {
      rotIn: [Number, String]
    },
    data() {
      let safariCheck = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
      return {
        isSafari: safariCheck,
        rotation: 0,
        rotating: false,
        angle: 0,
        startAngle: 0,
        center: {x: 0, y: 0},
        R2D: 180 / Math.PI,
        azimuth: 0,
        static: 0
      };
    },
    // computed: {
    // R2D() {
    //   return 180 / Math.PI
    // },
    // },
    mounted() {
      if (typeof this.rotIn !== 'undefined') {
        this.azimuth = parseFloat(this.rotIn);
        this.static = parseFloat(this.rotIn);
      }
      document.getElementById('rotator_cont').onmousemove = (e) => {
        // e = e || window.event;
        e.preventDefault();
        // console.log('dragging', this.moving)
        if (this.rotating) this.rotate(e);
        // if(this.moving) this.moveAll(e)
      };
      document.getElementById('rotator_cont').addEventListener(this.moveEvent, (e) => {
        // e = e || window.event;
        e.preventDefault();
        // console.log('dragging', this.moving)
        if (this.rotating) this.rotate(e);
        // if(this.moving) this.moveAll(e)
      });
      document.querySelector('body').addEventListener(this.upEvent, (e) => {
        e.preventDefault();
        this.rotating = false;
        document.querySelector('body').removeEventListener('touchmove', (evt) => {
          if(this.rotating) evt.preventDefault();
        }, {passive: false});
        // console.log('done')
      });
      document.getElementById('rotator_cont').ontouchend = (e) => {
        e.preventDefault();
        this.rotating = false;
        document.getElementById('rotator_cont').removeEventListener('touchmove', (evt) => {
          if(this.rotating) evt.preventDefault();
        }, {passive: false});
        // console.log('done')
      };
    },
    computed: {
      moveEvent() {
        if (!this.isSafari) return 'pointermove';
        else return 'mousemove';
      },
      upEvent() {
        if (!this.isSafari) return 'pointerup';
        else return 'mouseup';
      },
    },
    methods: {
      // eslint-disable-next-line no-unused-vars
      handleMove({evt, ...info}) {
        // console.log('handle move', this.rotating, info)
        if (this.rotating) {
          this.rotate(evt);
        }
      },
      startRotate(e) {
        console.log('start');
        document.getElementById('rotator_cont').addEventListener('touchmove', (evt) => {
          if (this.rotating) evt.preventDefault();
        }, {passive: false});
        // console.log('starting rotate', e)
        // e.preventDefault()
        this.static = this.azimuth;
        this.rotating = true;
        let r = document.getElementById('rotator_cont').getBoundingClientRect();
        let s = {
          top: r.top,
          left: r.left,
          height: r.height,
          width: r.width
        };
        this.center = {
          x: s.left + (s.width / 2),
          y: s.top + (s.height / 2)
        };
        let x = e.clientX - (s.left + (s.width / 2));
        let y = e.clientY - (s.top + (s.height / 2));
        this.startAngle = this.R2D * Math.atan2(y, x);
      },
      rotate(e) {
        // console.log('rotating')
        // e.preventDefault()
        let x = e.clientX - this.center.x;
        let y = e.clientY - this.center.y;
        let d = this.R2D * Math.atan2(y, x);
        let rotation = (d - this.startAngle).toFixed(0);
        this.rotation = rotation;
        let rot = parseInt(rotation) + this.static;
        let a;
        if (rot > 0) {
          if (rot < 360) a = rot;
          else a = rot % 360;
        } else {
          if (rot < -360) a = 360 + rot;
          else a = 360 - (Math.abs(rot) % 360);
        }
        // not using this yet, but needed to not restart angle on every click
        let t = document.getElementById('rotate');
        let r = 'rotate(' + a + 'deg)';
        // t.style.transformOrigin = '0 0';
        t.style.transform = r;
        this.azimuth = a;
        if (typeof this.azimuth !== 'undefined') this.$emit('update:model-value', this.azimuth);
      },
    }
  };
</script>

<style scoped lang="scss">
  #rotator_cont {
    height: 34px;
    width: 34px;
    border-radius: 100px;
    position: relative;
    border: solid 2px #212121;
  }

  #drag {
    width: 30%;
    height: 30%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto auto;
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, #333, #444);
    border: 3px solid rgba(200, 200, 200, 0.01);
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.8);
  }

  #rotate {
    width: 100%;
    height: 100%;
    position: absolute;
    border-radius: 50%;
    background: white;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      margin: 3% auto;
      height: 5px;
      width: 5px;
      border-radius: 50%;
      background: #212121;
      box-shadow: 0 0 2px 0 orange;
    }
  }
</style>
