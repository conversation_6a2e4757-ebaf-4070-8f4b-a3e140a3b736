import {computed, ref} from 'vue';
import { _get } from 'symbol-syntax-utils';
import {AnyObj} from 'src/utils/types';
import {bytes} from 'components/common/uploads/utils/index';
declare interface UploaderOpts {
    multiple: boolean,
    log: boolean,
    allowTypes: string[],
    maxSize: number,
    smSize: number,
    upload: (files:any) => void,
    fillColor: string,
    emit: (evt: string, ...args:any) => void
}

export const dataURLToBlob = (dataURL: string, log?:boolean): Blob => {
    if (log) console.log('datarultoblob');
    const BASE64_MARKER = ';base64,';
    if (dataURL.indexOf(BASE64_MARKER) === -1) {
        const parts = dataURL.split(',');
        const contentType = parts[0].split(':')[1];
        const raw = parts[1];

        return new Blob([raw], { type: contentType });
    }

    const parts = dataURL.split(BASE64_MARKER);
    const contentType = parts[0].split(':')[1];
    const raw = window.atob(parts[1]);
    const rawLength = raw.length;

    const uInt8Array = new Uint8Array(rawLength);

    for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
    }

    return new Blob([uInt8Array], { type: contentType });
};

export const uploadFiles = (
    {
        multiple,
        log,
        allowTypes = ['image/*'],
        maxSize = 512000,
        smSize = 512000,
        upload,
        emit = () => console.log('no emit provided')
    }:UploaderOpts
) => {
    const progress = ref(0);
    const loading = ref(false);
    const raw = ref();
    const sm = ref();
    const display = ref('');
    const displayText = ref();
    const errorText = ref();
    const files = ref<AnyObj[]>([]);


    const handleUploads = async (final:any, options:any): Promise<void> =>{
        const fileObj = { raw:options?.raw || raw.value, sm:options?.small || sm.value };
        if(files.value?.length) files.value.push(fileObj)
        else files.value = [fileObj];
        if(log) console.log('upload handled', final, fileObj);
        if((!multiple || final) && upload){
            loading.value = true;
            let ul;

            if(upload) ul = await upload(files.value);
            loading.value = false;
            if(log) console.log('uploaded?', ul);
        } else if(!upload) loading.value = false;

    };

    const handleResize = async (small:any, final: boolean, raw:any):Promise<void> => {
        sm.value = small;
        if(raw) raw.value = raw;
        loading.value = false;
        if(log) console.log('handle resize', small, raw);
        await handleUploads(final, { small, raw });
    };

    const handleImage = (file:File, final:boolean):void => {
        raw.value = file;
        if (log) console.log('handling image', file);
        // Load the image
        const reader = new FileReader();
        reader.onload = function (readerEvent) {
            if (log) console.log('reader loaded');
            const image = new Image();
            if (log) console.log('image', image);
            image.onload = function () {
                if (log) console.log('image loaded');

                // Resize the image
                const canvas = document.createElement('canvas')
                const sm_size = smSize
                // image.setAttribute('style', 'position:fixed; opacity:0;z-index:-100');
                // document.body.appendChild(image);
                const ratio = Math.max(1, sm_size / file.size);
                const newWidth = image.width * ratio;
                const newHeight = image.height * ratio;

                canvas.width = newWidth;
                canvas.height = newHeight;

                const ctx = canvas.getContext('2d');
                if(ctx) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear the canvas to ensure transparency
                    ctx.drawImage(image, 0, 0, newWidth, newHeight); // Draw the image onto the canvas
                }
                const dataUrl = canvas.toDataURL('image/png');
                const resizedImage = dataURLToBlob(dataUrl, log);
                emit('update:sm', dataUrl, resizedImage);
                display.value = dataUrl;
                emit('update:display', dataUrl)
                handleResize(resizedImage, final, file);
                // document.body.removeChild(image);
            }
            const result = readerEvent.target?.result;
            if (typeof result === 'string') {
                image.src = result; // Set image source to the loaded file
            }
        }
        reader.readAsDataURL(file);
    };

    const handleAllTypes = async (file: File, final: boolean): Promise<void> => {
        const runTypeMethods = async (type:string) => {
            if(type.indexOf('image') > -1){
                handleImage(file, final);
            } else if(type.indexOf('video') > -1){
                const f = Array.isArray(file) ?  file : [file];
                for(let i = 0; i < f.length; i++) {
                    raw.value = f[i];
                    sm.value = f[i];
                    await handleUploads(final, {})
                }
                loading.value = false;
            } else {
                const regex = new RegExp('(pdf|msword|officedocument|csv|excel|markdown)', 'i');

                if(regex.test(type)){
                    const f = Array.isArray(file) ?  file : [file];
                    for(let i = 0; i < f.length; i++) {
                        raw.value = f[i];
                        sm.value = f[i];
                        await handleUploads(final, {})
                    }
                    loading.value = false;
                } else {
                    errorText.value = 'invalid file type: ' + type;
                    console.log('invalid file type', type);
                }
            }
        };

        if(log) console.log('handling type', file.type);
        if(file) {
            if(file.size > maxSize) {
                errorText.value = 'file too large: ' + bytes(file.size) + `. Must be ${bytes(maxSize)} or less`;
                console.log('file too large', file.size, ' Max size', maxSize);
                return;
            }
            const type = file.type;
            const truths = allowTypes.filter(a => {
                return !a.split('/').some(b => b !== '*' && !type.includes(b.split('.')[0]));
            });
            if(log) console.log('truth filter', type, allowTypes, truths);
            if(!truths.length){
                let typeString = '';
                allowTypes.forEach(t => typeString += `${t}, `)
                errorText.value = `Supported types: ${typeString.substring(0, typeString.length - 2)}`;
            } else runTypeMethods(type)
        }
    };

    const dropHandler = async (ev:any) => {
        loading.value = true;
        // setTimeout(() => {
        //   this.loading = false;
        // }, 10000);
        if(log) console.log('File(s) dropped', ev);

        // Prevent default behavior (Prevent file from being opened)
        ev.preventDefault();

        if (ev.dataTransfer && ev.dataTransfer.items) {
            const length = ev.dataTransfer.items.length;
            // Use DataTransferItemList interface to access the file(s)
            for (let i = 0; i < length; i++) {
                // If dropped items aren't files, reject them
                if (ev.dataTransfer.items[i].kind === 'file') {
                    const file = ev.dataTransfer.items[i].getAsFile();
                    if(log) console.log('... file[' + i + '].name = ' + file.name);
                    await handleAllTypes(file, i === length-1);
                }
            }
        } else {
            // Use DataTransfer interface to access the file(s)
            const fileList = ev.target.files || ev.dataTransfer.files;
            const length = fileList.length;
            for (let i = 0; i < length; i++) {
                const file = fileList[i];
                if (log) console.log('file', file);
                await handleAllTypes(file, i === length - 1);
            }
        }
    };

    return {
        display,
        displayText,
        errorText,
        progress,
        loading,
        raw,
        sm,
        dataURLToBlob,
        handleUploads,
        handleResize,
        handleImage,
        handleAllTypes,
        dropHandler
    }
}
