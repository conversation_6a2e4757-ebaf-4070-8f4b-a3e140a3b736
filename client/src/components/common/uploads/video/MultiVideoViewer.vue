<template>
  <div class="_fa position-relative">
    <q-video v-bind="{ class: '_fa', ratio: '1.777', src: videoUrl, ...$attrs }"></q-video>

    <template v-if="urls?.length > 1">
      <div class="__left">
        <slot name="left">
          <q-btn class="__btn" dense flat round icon="mdi-chevron-left" :disable="idx === 0" @click="idx--"></q-btn>
        </slot>
      </div>
      <div class="__right">
        <slot name="right">
          <q-btn class="__btn" dense flat round icon="mdi-chevron-right" :disable="idx >= (urls?.length || 1) - 1" @click="idx++"></q-btn>
        </slot>
      </div>
    </template>
  </div>
</template>

<script setup>


  import {computed, ref} from 'vue';

  const props = defineProps({
    urls: { required: true }
  })

  const idx = ref(0)
  const videoUrl = computed(() => (props.urls || [])[idx.value])

</script>

<style lang="scss" scoped>

  .__left, .__right {
    position: absolute;
    top: 50%;

    .__btn {
      background: rgba(0,0,0,.3);
      color: white;
    }

  }
</style>
