<template>
  <div
      @pointerenter="hover = true"
      @pointerleave="hover = false"
      v-bind="{
    id: id,
    class: shape,
    style: { cursor: 'pointer', position: 'relative', height: height, width: width, maxWidth: '100%', ...divStyle },
    ...divAttrs
  }"
      @drop="dropHandler($event)"
  >
    <slot name="default">
      <div class="__cover cursor-pointer flex flex-center"
           :style="(display || modelValue) && !hover ? 'top: 100%; opacity: 0;' : ` background: ${bg}; color: ${textColor}`">

        <div class="__upload_text">
          <slot name="text" :displayText="displayText" :errorText="errorText">
            <slot name="display" :displayText="displayText" :errorText="errorText">
              <span v-if="!displayText && !errorText">{{ `${title}` }}</span>
              <span v-else-if="!!displayText">{{ displayText }}</span>
              <span v-else style="color: #b80000">{{ errorText }}</span>
            </slot>
          </slot>
        </div>

      </div>

      <div v-if="loading" class="__loader">
        <q-circular-progress
            size="30px"
            color="primary"
            :indeterminate="!progress"
            :modelValue="progress"
        ></q-circular-progress>
      </div>

      <slot name="display">
        <file-type-handler
            :height="height"
            :width="width"
            :element-id="id"
            :file="raw"
            :url="modelValue || display"
        ></file-type-handler>
      </slot>
    </slot>

    <slot name="input">
      <input
          :multiple="multiple"
          @change="dropHandler($event)"
          type="file"
          style="opacity: 0; position: absolute; top: 0; left: 0; width: 100%; height: 100%;"
          :accept="allowTypes"
      >
    </slot>
  </div>
</template>

<script setup>
  import FileTypeHandler from '../file-types/fileTypeHandler.vue';
  import {ref, watch} from 'vue';
  import {uploadFiles} from 'src/components/common/uploads';

  const emit = defineEmits(['update:display', 'loading', 'update:sm'])
  const props = defineProps({
    id: { type: String, default: 'uploader_element' },
    height: { type: String, default: '100%' },
    width: { type: String, default: '100%' },
    title: { type: String, default: '⬆️ Upload' },
    shape: { type: String, default: 'square_sm', enum: ['circle', 'circle_sm', 'square', 'square_sm'] },
    allowTypes: {
      type: String,
      default: 'image/*,.jpg,.jpeg,.png,.gif,.bmp,.webp,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.rtf,.odt,.ods,.odp,.csv,.tsv,.svg'
    },
    divStyle: Object,
    multiple: Boolean,
    modelValue: { required: false },
    bg: { type: String, default: '#EEEEEE' },
    textColor: { type: String, default: 'black' },
    maxSize: { type: Number, default: 200 },
    divAttrs: Object,
    log: Boolean,
    smSize: { type: Number, default: 200 },
    upload: {
      type: Function,
      default: () => {
        console.log('You didn\'t pass an upload function prop');
      }
    }
  });

  const hover = ref(false);

  const { display, dropHandler, displayText, errorText, progress, loading, raw } = uploadFiles({
    emit,
    log: props.log,
    allowTypes: Array.isArray(props.allowTypes) ? props.allowTypes : (props.allowTypes || 'image/*').split(' ').join('').split(','),
    maxSize: props.maxSize || props.smSize,
    upload: props.upload
  });

  watch(loading, (nv) => {
    emit('loading', nv)
  }, { immediate: true })
</script>

<style scoped lang="scss">
  .square {
    width: 150px;
    height: 150px;
    overflow: hidden;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 500;
  }

  .circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    font-size: 13px;
    font-weight: 500;
  }

  .square_sm {
    width: 60px;
    height: 60px;
    overflow: hidden;
    border-radius: 5px;
    font-size: 10px;
    font-weight: 500;
  }

  .circle_sm {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    font-size: 10px;
    font-weight: 500;
  }

  .__loader {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
  }

  .__cover {
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(229, 229, 229, 0.81);
    transition: all .2s ease-out;
    pointer-events: none;
  }

  .__upload_text {
    text-align: center;
    display: block;
    font-size: .85rem;
    font-weight: 500;
    font-family: var(--alt-font);
  }
</style>
