<template>
  <q-chip
      v-bind="{
    color: 'white',
    clickable: true,
    ...$attrs
      }"
      @click="dialog = true"
  >
  <q-avatar>
    <div class="flex flex-center">
      <div>
        <b>{{modelValue?.usage?.length || 0}}</b>
      </div>
    </div>
  </q-avatar>
    <span class="q-ml-sm">Usage{{$pluralExtension('', modelValue?.usage || [])}}</span>

    <common-dialog v-model="dialog">
      <div class="_fw q-pa-md">
        <q-item>
          <q-item-section avatar>
            <q-avatar square>
            <file-type-handler
                :file="modelValue" :url="modelValue?.url"></file-type-handler>
            </q-avatar>
          </q-item-section>
          <q-item-section>
            <q-item-label>{{modelValue?.originalname}}</q-item-label>
          </q-item-section>

        </q-item>
        <q-list separator>
          <q-item>
            <q-item-section>
              <q-item-label>Usages - select to verify</q-item-label>
              <q-item-label caption>Last verified: {{ago(modelValue?.usageVerified )}}</q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-checkbox @click="selectAll" :model-value="allSelected"></q-checkbox>
            </q-item-section>
          </q-item>

          <q-item v-for="(item, i) in modelValue?.usage || []" :key="`item-${i}`">
            <q-item-section>
              <q-item-label><b>Service:</b> {{item.subjectModel}} - <b>Path:</b> {{item.subjectPath}}</q-item-label>
              <q-item-label><b>Record:</b> {{item.subject}}</q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-checkbox @click="select(item)" :model-value="selected?.map(a => a.subject).includes(item.subject)"></q-checkbox>
            </q-item-section>
          </q-item>
        </q-list>


        <div class="q-py-md row justify-end">
          <q-btn v-if="!modelValue?.usage?.length" no-caps label="Remove no usage file?" icon-right="mdi-delete" flat color="ir-red-10" @click="verify(true)">
          </q-btn>
          <q-btn v-if="selected?.length" no-caps color="black" @click="verify(false)">
            <span class="q-mr-sm">
              {{`Verify ${selected.length}`}}
            </span>
            <q-spinner v-if="loading" color="white"></q-spinner>
          </q-btn>
        </div>
      </div>
    </common-dialog>
  </q-chip>
</template>

<script setup>
  import {$pluralExtension} from 'src/utils/global-methods';
  import CommonDialog from 'src/components/common/dialogs/CommonDialog.vue';
  import {computed, ref} from 'vue';
  import FileTypeHandler from 'src/components/common/uploads/file-types/fileTypeHandler.vue';
  import { ago } from 'src/utils/date-utils'
  import { useUploads } from 'src/stores/uploads';
  const store = useUploads();

  const props = defineProps({
    modelValue: Object
  })

  const dialog = ref(false);
  const loading = ref(false);

  const selected = ref([]);
  const allSelected = computed(() => props.modelValue?.usage && (selected.value?.length === (props.modelValue?.usage || []).length))
  const selectAll = () => {
    if(allSelected.value) selected.value = [];
    else selected.value = (props.modelValue?.usage || []).slice(0);
  }

  const select = (usage) => {
    const idx = selected.value?.map(a => a.subject).indexOf(usage.subject);
    if(idx > -1) selected.value.splice(idx, 1);
    else selected.value.push(usage);
  }

  const verify = async (deleteUnused) => {
    loading.value = true;
    await store.patch(props.modelValue._id, { verifyUsage: selected.value, ...props.modelValue, deleteUnused });
    loading.value = false;
  }
</script>

<style lang="scss" scoped>

</style>
