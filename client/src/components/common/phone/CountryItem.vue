<template>
  <q-item v-bind="{clickable: true, ...$attrs}">
    <q-item-section avatar>
      <div>
        <span style="font-size: 20px">{{ flag(_get(c, 'iso2'), 'emoji') }}</span>
      </div>
    </q-item-section>
    <q-item-section>
      <q-item-label>
        {{ `+${c.dialCode} - ${c.name}` }}
      </q-item-label>
    </q-item-section>
    <q-item-section side>
      <q-icon v-if="active" name="mdi-check" color="green"></q-icon>
    </q-item-section>
  </q-item>
</template>

<script setup>

  import {_get} from 'symbol-syntax-utils';
  import {flag} from 'components/common/phone/flag-emojis';
  import {computed} from 'vue';

  const props = defineProps({
    modelValue: Object,
    active: Boolean
  })

  const c = computed(() => props.modelValue || {});
</script>

<style lang="scss" scoped>

</style>
