<template>
  <q-chip
      v-bind="{
        label: (modelValue || 'once').toUpperCase(),
        iconRight: 'mdi-menu-down',
        ...$attrs,
      }"
  >
    <q-menu>
      <div class="w250 mw100 bg-white br10 bs2-8 q-pa-md">
        <q-list separator>
          <q-item v-for="i in ['once', 'daily', 'weekly', 'monthly', 'annually']" :key="i" clickable @click="$emit('update:model-value', i)">
            <q-item-section>
              <q-item-label class="text-uppercase">{{i}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-menu>
  </q-chip>
</template>

<script setup>
  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: String
  })

</script>

<style lang="scss" scoped>

</style>
