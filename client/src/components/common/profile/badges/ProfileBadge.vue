<template>
  <q-chip
      v-bind="{
    color: 'white',
    clickable: true,
        ...$attrs,
        label: undefined,
        icon: undefined
      }"
      @click="$emit('update:model-value', modelValue)"
      @remove="$emit('remove', modelValue)"
  >
    <template v-if="badge?.emoji">
      <div class="font-1-1-4r">{{ badge?.emoji }}</div>
    </template>
    <span class="q-mx-xs">{{ badge?.name }}</span>
  </q-chip>
</template>

<script setup>
  import {useEnvStore} from 'src/stores/env';
  const envStore = useEnvStore();
  import {storeToRefs} from 'pinia';
  import {computed} from 'vue';
  const { profileBadges } = storeToRefs(envStore);

  const props = defineProps({
    modelValue: { required: true, type: String }
  });

  const badge = computed(() => {
    return profileBadges.value[props.modelValue];
  })
</script>

<style lang="scss" scoped>

</style>
