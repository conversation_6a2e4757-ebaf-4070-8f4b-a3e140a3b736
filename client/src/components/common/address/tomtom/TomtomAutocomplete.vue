<template>
  <div class="_fw">
    <div v-if="betterList?.length" class="q-py-sm">
      <div class="font-3-4r">Matches Found - are any of these yours?</div>
      <q-list separator dense>
        <q-item v-for="(item, i) in betterList || []" :key="`better-${i}`" clickable @click="geocode(item)">
          <q-item-section>
            <q-item-label>{{ item && item.address ? item.address.freeformAddress : '' }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>
    <q-select
        v-bind="{
          behavior: 'menu',
          placeholder: modelValue && normalizedAddress && normalizedAddress[displayPath] ? '' : 'Enter address (ex. 123 N Main St, New York, NY 12345)...',
          options,
          autocomplete: 'off',
          hideBottomSpace: true,
          style: { 'max-width': '90vw', width: '100%' },
          modelValue: modelValue ? normalizedAddress ? normalizedAddress[displayPath] : undefined : undefined,
          ...$attrs
        }"
        ref="addressSelect"
        @clear="normalizedAddress = null, $emit('clear')"
        use-input
        :modelValue="modelValue ? normalizedAddress ? normalizedAddress[displayPath] : undefined : undefined"
        @input-value="setInput"
        @update:model-value="geocode"
    >
      <template v-slot:prepend v-if="$slots.prepend || icon">
        <slot name="prepend">
          <q-icon v-if="icon" v-bind="icon"></q-icon>
        </slot>
      </template>
      <template v-slot:selected-item="scope" v-if="chips">
        <q-chip
            v-bind="{
          color: 'ir-bg2',
          removable: true,
          iconRemove: 'icon-close',
          ...chipAttrs
            }"
            v-show="scope.opt"
            @remove="removeAddress"
        >
          <span>{{scope.opt}}</span>
        </q-chip>
      </template>
      <template v-slot:before-options>
        <q-item v-if="allowAdd" dense clickable @click="setAdding">
          <q-item-section>
            <q-item-label class="text-weight-bold font-3-4r">Address not popping up? Add Custom Address</q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-icon color="primary" name="mdi-plus"></q-icon>
          </q-item-section>
        </q-item>
      </template>
      <template v-slot:append>
        <slot name="append" :loading="loading" :clear="removeAddress">
          <q-spinner size="30px" v-if="loading"></q-spinner>
        </slot>
      </template>
      <template v-slot:option="{opt, toggleOption}">
        <q-item clickable @click="toggleOption(opt)">
          <q-item-section>
            <q-item-label>{{ opt && opt.address ? opt.address.freeformAddress : '' }}</q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>
    <q-slide-transition>
      <div class="_fw q-pa-md" v-if="adding">
        <div class="text-weight-medium font-3-4r q-pb-sm">We recommend you enter custom address only if search won't
          pull yours up
        </div>
        <enter-address @update:model-value="emitAddress"></enter-address>
      </div>
    </q-slide-transition>
    <slot name="bottom" :clear="removeAddress"></slot>
  </div>
</template>

<script setup>
  import EnterAddress from '../utils/EnterAddress.vue';

  import {computed, watch, ref, useAttrs} from 'vue';
  import {_get} from 'symbol-syntax-utils';
  import {useTomtomGeocode} from 'src/stores/tomtom-geocode';

  const attrs = useAttrs();

  const tStore = useTomtomGeocode();
  import {geoToAddress} from 'src/utils/geo-utils';

  const defaultAddress = (val) => {
    return {
      formatted: '',
      address1: '',
      address2: '',
      region: '',
      city: '',
      postal: '',
      country: '',
      latitude: undefined,
      longitude: undefined,
      tomtomAddress: {},
      ...val
    };
  };

  const props = defineProps({
    chipAttrs: Object,
    modelValue: { required: true },
    icon: Object,
    chips: {
      type: Boolean,
      default: true
    },
    allowAdd: Boolean,
    displayPath: { type: String, default: 'formatted' },
    autocomplete: {
      type: String,
      default: 'off'
    },
    item_text: {
      type: String,
      default: 'formatted'
    },
    search_every_num: {
      type: Number,
      default: 2
    },
    address_format: {
      type: String,
      default: 'short_name',
      validator: modelValue => ['long_name', 'short_name'].indexOf(modelValue) >= 0
    }
  });

  const emit = defineEmits(['update:model-value']);

  const input = ref('');
  const obj = ref(null);
  const options = ref([]);
  const adding = ref(false);
  const normalizedAddress = ref(defaultAddress());
  const loading = ref(false);
  const addressSelect = ref(null);

  const mv = computed(() => {
    return props.modelValue;
  });

  const betterList = ref([]);


  watch(mv, (nv) => {
    if (nv && nv.formatted) {
      normalizedAddress.value = nv;
    }
  });

  const removeAddress = () => {
    emit('update:model-value', undefined);
    normalizedAddress.value = defaultAddress();
  };

  let timeout;
  const setInput = async (val, goNow) => {
    input.value = val;
    const runSearch = async () => {
      loading.value = true;
      const res = await tStore.find({
        query: {
          text: input.value
        },
      })
          .catch(err => {
            loading.value = false;
            console.error(err);
          })
      // console.log('geo res', res);
      loading.value = false;
      if (_get(res, 'data[0]')) {
        addressSelect.value.showPopup();
        options.value = res.data;
        return res.data;
      } else return undefined
    };

    if (val || goNow) {
      if (goNow || val.length % props.search_every_num === 0) {
        if (timeout) clearTimeout(timeout);
        return runSearch();
      } else {
        timeout = setTimeout(() => {
          if (timeout) {
            clearTimeout(timeout);
            return runSearch()
          }
        }, 3000);
        return undefined;
      }
    }
  }

  const setAdding = () => {
    adding.value = true;
    addressSelect.value.hidePopup();
  };

  const emitAddress = async (address) => {
    normalizedAddress.value = defaultAddress(address);
    loading.value = true;
    const better = await setInput(address.formatted, true);
    loading.value = false;
    if (better?.length) betterList.value = better;
    else emit('update:model-value', normalizedAddress.value);
  }
  const geocode = (val) => {
    adding.value = false
    // TODO: need to parse formated_adress by name and not index ...
    let mappedAddress = val ? val : null;
    if (mappedAddress) {
      normalizedAddress.value = geoToAddress(val);
    }
    emit('update:model-value', normalizedAddress.value);
    betterList.value = [];
    // // eslint-disable-next-line no-console
    // console.log(response)
    // }).catch(err => {
    //   this.$emit('error', err);
    // });

  };

</script>

<style scoped>

</style>
