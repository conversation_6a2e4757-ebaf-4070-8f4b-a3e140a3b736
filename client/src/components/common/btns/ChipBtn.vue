<template>
  <div class="__chip_btn">
    <div class="__ic" :style="{ height: size, width: size }">
      <slot name="default">
      </slot>
    </div>
    <div :style="{ height: smaller, color: textColor, borderColor, ...labelStyle }" class="__side">
      <div class="flex items-center">
        <slot name="label">
          <div>{{ label }}</div>
        </slot>
        <slot name="right"></slot>
      </div>
    </div>
    <slot name="menu"></slot>
  </div>
</template>

<script setup>

  import {computed} from 'vue';

  const props = defineProps({
    label: String,
    size: { type: String, default: '30px' },
    labelStyle: Object,
    borderColor: String,
    textColor: String
  });

  const smaller = computed(() => {
    const s = props.size || '30px';
    const num = parseFloat(s);
    return num - (Math.floor(num * .12)) + s.split(`${num}`)[1]
  })
</script>

<style lang="scss" scoped>
  .__chip_btn {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .__ic {
    border-radius: 50%;
    background: white;
    z-index: 2;
  }

  .__side {
    padding: 0 15px;
    transform: translate(-10px, 0);
    z-index: 1;
    display: grid;
    align-items: center;
    justify-items: center;
    border-radius: 0 30px 30px 0;
    border-top: solid 1px;
    border-right: solid 1px;
    border-bottom: solid 1px;
  }
</style>
