<template>
  <q-dialog  v-bind="{...position.dialog, ...dialogAttrs }" :modelValue="modelValue" @update:model-value="handleEmit">

    <div v-bind="{ class: '__cd', ...position.card, ...cardAttrs }">
      <q-btn class="t-r-f bg-black text-white __z" size="sm" dense icon="mdi-close" @click="close()"/>
      <slot name="default"></slot>
    </div>
  </q-dialog>
</template>

<script>
  export default {
    name: 'CommonDialog',
    props: {
      modelValue: Boolean,
      cardAttrs: {
        type: Object
      },
      maximized: Boolean,
      dialogAttrs: {
        type: Object
      },
      setting: { type: String, default: 'standard' },
      id: { type: String, default: String(new Date().getTime()) }
    },
    data(){
      return {
        watchVal: false
      }
    },
    watch: {
      modelValue: {
        immediate: true,
        handler(newVal){
          this.watchVal = newVal;
          if(this.setting === 'full'){
            const {href} = this.$router.resolve({...this.$route, query: {...this.$route.query, [this.id]: newVal }});
            window.history.pushState({}, '', href)
          }
        }
      },
      dialogState: {
        immediate: true,
        handler(newVal){
          this.watchVal = !(!newVal || newVal === 'false');
          this.$emit('update:model-value', this.watchVal);
        }
      }
    },
    computed: {
      value(){
        return this.modelValue || this.watchVal;
      },
      position(){
        if(this.positions[this.setting]) return this.positions[this.setting]
        else return {dialog: {}, card: {}}
      },
      positions() {
        return {
          standard: {
            dialog: {
              maximized: this.maximized || this.$q.screen.lt.sm,
              transitionShow: 'slide-up',
              transitionHide: 'slide-down'
            },
            card: {
              style: {
                width: '100%',
                maxWidth:  this.maximized ? '100%' : '600px'
              }
            }
          },
          right: {
            dialog: {
              position: 'right',
              maximized: this.$q.screen.lt.sm,
              transitionShow: 'slide-left',
              transitionHide: 'slide-right'
            },
            card: {
              style: {
                width: '100vw',
                maxWidth: '700px',
                height: '100%'
              }
            }
          },
          left: {
            dialog: {
              position: 'left',
              maximized: this.$q.screen.lt.sm,
              transitionShow: 'slide-right',
              transitionHide: 'slide-left'
            },
            card: {
              style: {
                width: '100vw',
                maxWidth: '700px',
                height: '100vh'
              }
            }
          },
          smmd: {
            dialog: {
              maximized: this.$q.screen.lt.md,
              transitionShow: 'slide-up',
              transitionHide: 'slide-down'
            },
            card: {
              style: {
                width: '100vw',
                maxWidth: '550px'
              }
            }
          },
          small: {
            dialog: {
              maximized: this.maximized,
              transitionShow: this.maximized ? 'slide-up' : undefined,
              transitionHide: this.maximized ? 'slide-down' : undefined
            },
            card: {
              style: {
                width: '100vw',
                maxWidth: '400px'
              }
            }
          },
          xsmall: {
            dialog: {
              maximized: this.maximized,
              transitionShow: this.maximized ? 'slide-up' : undefined,
              transitionHide: this.maximized ? 'slide-down' : undefined
            },
            card: {
              style: {
                width: '100vw',
                maxWidth: '300px'
              }
            }
          },
          medium: {
            dialog: {
              maximized: this.$q.screen.lt.md,
            },
            card: {
              style: {
                width: '100vw',
                maxWidth: '800px'
              }
            }
          },
          large: {
            dialog: {
              maximized: this.maximized || this.$q.screen.lt.lg,
              transitionShow: 'jump-up',
              transitionHide: 'jump-down'
            },
            card: {
              style: {
                width: '100vw',
                maxWidth: '1100px'
              }
            }
          },
          full: {
            dialog: {
              maximized: true,
              transitionShow: 'slide-up',
              transitionHide: 'slide-down'
            },
            card: {
              height: '100vh',
              width: '100vw'
            }
          },
          message: {
            dialog: {
              maximized: true,
              position: 'bottom',
              transitionShow: 'slide-up',
              transitionHide: 'slide-down'
            },
            card: {
              height: '40vh',
              width: '100vw'
            }
          }
        };
      },
      dialogState(){
        return this.$route.query[this.id];
      }
    },
    methods: {
      handleEmit(val){
        this.$emit('update:model-value', val);
      },
      close() {
        this.$emit('update:model-value', false);
        this.$emit('close');
      }
    }
  };
</script>

<style scoped>
  .__z {
    z-index: 10;
  }
  .__cd {
    width: 100%;
    border-radius: min(15px, 1.2vw);
    background: var(--ir-bg);
  }
</style>
