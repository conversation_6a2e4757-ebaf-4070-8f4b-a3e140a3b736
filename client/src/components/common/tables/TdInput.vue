<template>
  <div class="__col">
    <q-field
        v-bind="{
          dense: true,
          borderless: true,
          modelValue,
          class: 'text-right _fw',
          ...$attrs
        }"
        @focus="focus"
        @blur="blur"
    >
      <template v-slot:control="{ focused }">
        <input
            class="q-field__input _fw text-right"
            :value="modelValue"
            @input="e => $emit('update-model-value', e.target.value)"
            v-show="focused"
        >
        <div v-show="!focused" class="text-weight-medium text-right _fw font-3-4r">
          <div>{{format(modelValue)}}</div>
        </div>
      </template>
    </q-field>
  </div>
</template>

<script setup>

  import {ref} from 'vue';

  const emit = defineEmits('update:model-value', 'focus', 'blur')
  const props = defineProps({
    modelValue: String,
    format: { default: (e) => e }
  })

  const focused = ref(false);
  const focus = (e) => {
    focused.value = true;
    emit('focus', e);
  }

  const blur = (e) => {
    focused.value = false;
    emit('blur', e);
  }
</script>

<style lang="scss" scoped>
  .__col {
    height: 30px;
    display: flex;
    align-items: center;
    position: relative;
  }
</style>
