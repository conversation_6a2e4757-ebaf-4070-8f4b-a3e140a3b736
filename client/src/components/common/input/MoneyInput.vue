<template>
  <q-input @blur="handleBlur" @focus="handleFocus()" v-bind="{ inputClass: 'num-font', ...$attrs }" :model-value="`${getAmt(modelValue)}${suffix || ''}`" @update:model-value="handleInput">
    <template v-slot:prepend v-if="$slots.prepend">
      <slot name="prepend"></slot>
    </template>
    <template v-slot:append v-if="$slots.append">
      <slot name="append"></slot>
    </template>
    <template v-slot:after v-if="$slots.after">
      <slot name="after"></slot>
    </template>
  </q-input>
</template>

<script setup>

  import {ref} from 'vue';

  const emit = defineEmits(['focus', 'blur', 'update:model-value'])
  const props = defineProps({
    def: { default: 0 },
    modelValue: { required: true },
    prefix: String,
    suffix: String,
    decimal: { type: [String,Number], default: 0 }
  })

  const handleInput = (val) => {
    if(!val) emit('update:model-value', val)
    emit('update:model-value', Number(String(val).replace(/[^\d.]/g, '') || 0));
  };

  const focus = ref(false)
  const handleFocus = () => {
    focus.value = true
    emit('focus');
  }

  const dollarString = (val) => {
    const v = typeof val !== 'number' ? parseFloat(val) : val;
    if(isNaN(v)) return props.def;
    // console.log('dollar string', val, v)
    const decimal = props.decimal || props.decimal === 0 ? props.decimal : 2;
    const valDec = v.toFixed(Number(decimal));
    return (props.prefix || '') + valDec.toString().replace(/\B(?=(\d{3})+(?!\d))/g,',');
  }

  const getAmt = (val) => {
    if(focus.value) return isNaN(val) ? '' : val;
    else return dollarString(val);
  }

  const handleBlur = () => {
    focus.value = false;
    emit('blur')
    handleInput(dollarString(props.modelValue))
  }
</script>

<style lang="scss" scoped>

</style>
