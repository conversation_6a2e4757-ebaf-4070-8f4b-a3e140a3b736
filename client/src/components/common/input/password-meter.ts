import {computed, ComputedRef, ref, watch, Ref} from 'vue';
import {AnyObj} from 'src/utils/types';

type PasswordOptions = {
    length?: number,
    uppercase?: boolean,
    lowercase?: boolean,
    number?: boolean,
    special?: boolean
};
type VObj = {
    test: boolean,
    on: boolean,
    message: string
}
type PasswordValidator = {
    uppercase: VObj,
    lowercase: VObj,
    special: VObj,
    number: VObj,
    length: VObj,
    code: number,
    need: Array<string>,
    message: string
};

export const passwordStrength = (val:string, options?: PasswordOptions): PasswordValidator => {
    const {length = 8, uppercase = true, lowercase = true, number = true, special = true} = options || {};

    const list = ['length', 'number', 'uppercase', 'lowercase', 'special'];
    const returnVal = {
        code: 0,
        need: list.map(a => a),
        message: ''
    };

    const obj = {
        'uppercase': {
            test: new RegExp('.*[A-Z]').test(val),
            on: uppercase,
            message: 'Uppercase Letter'
        },
        'lowercase': {
            test: new RegExp('.*[a-z]').test(val),
            on: lowercase,
            message: 'Lowercase Letter'
        },
        'number': {
            test: new RegExp('.*[0-9]').test(val),
            on: number,
            message: 'Number'
        },
        'special': {
            test: new RegExp('.*[!@#$&*%^()_+]').test(val),
            on: special,
            message: 'Special Character'
        },
        'length': {
            test: new RegExp('.{' + String(length) + ',}').test(val),
            on: !!length,
            message: `At least ${length} characters`
        }
    };
    list.forEach((a) => {
        if(obj[a as keyof typeof obj].on){
            if(!obj[a as keyof typeof obj].test){
                returnVal.code++;
                returnVal.message += returnVal.message ? `, ${obj[a as keyof typeof obj].message}` : obj[a as keyof typeof obj].message;
            } else returnVal.need.splice(returnVal.need.indexOf(a), 1);
        }
    })
    return { ...returnVal, ...obj };
};

export const passwordMeter = (value:Ref<string>|ComputedRef<string>, emit:any) => {

    const input = ref('');
    const status = ref(0);
    const unresolved:Ref<string[]> = ref([]);
    const errorMessage = ref('');
    const reqs:Ref<{ [key:string]: { message:string} & AnyObj } & AnyObj> = ref({});
    const error = computed(() => {
        return status.value > 0;
    });
    const errors = computed(() => {
        return unresolved.value.map(a => reqs.value[a]?.message)
    });

    watch(value, nv => {
        input.value = nv;
    }, { immediate: true });

    const handleInput = (val:any) => {
        const { code, need, message, ...rest } = passwordStrength(val);
        status.value = code;
        unresolved.value = need;
        errorMessage.value = message;
        reqs.value = rest;
        input.value = val;
        emit('update:model-value', val);
        if(!error.value) emit('update:valid', true);
        else emit('update:valid', false);
    };

    return {
        error,
        errors,
        input,
        handleInput,
        status,
        errorMessage,
        reqs,
        unresolved
    }
};
