<template>
  <div class="_fw">
    <q-input
        @focus="emit('focus')"
        @blur="emit('blur')"
        @update:model-value="handleInput"
        v-bind="{
          type: show ? '' : 'password',
    modelValue: input,
    ...$attrs
  }">
      <template v-slot:prepend>
        <slot name="prepend">
          <q-btn @click="show = !show" dense flat :icon="show ? 'mdi-eye-off' : 'mdi-eye'" size="sm" color="primary">
            <q-tooltip>{{show ? 'Mask Password' : 'Show Password'}}</q-tooltip>
          </q-btn>
        </slot>
      </template>
      <template v-slot:append>
        <slot name="append">
          <q-avatar size="10px" :color="input ? error ? 'red' : 'green' : 'grey'"></q-avatar>
        </slot>
      </template>
    </q-input>
    <slot name="errors" :error="error" :errors="errors">
      <q-slide-transition>
        <ul v-if="error" class="font-3-4r text-color-red">
          <li v-for="(error, i) in errors" :key="`error-${i}`">{{ error }}</li>
        </ul>
      </q-slide-transition>
    </slot>
  </div>
</template>

<script setup>
  import {computed, ref} from 'vue';
  import {passwordMeter} from './password-meter';

  const emit = defineEmits(['update:model-value', 'update:valid', 'focus', 'blur']);
  const props = defineProps({
    modelValue: String,
    ready: Boolean
  });

  const show = ref(false);

  const { error, errors, input, handleInput, reqs } = passwordMeter(computed(() => props.modelValue), emit);


</script>

<style scoped>

</style>
