export const parseNumber = (val:string):string => {
    return val ? val.replace(/[$,]/g, '') : '0';
};

export const multiCellArray = (pasted:string):Array<Array<string>> => {
    return pasted.trim().split(/\r?\n */).map(r=>r.split(/\t/)).map(a => a.map(b => parseNumber(b)));
};

export const pasteListener = (evt:ClipboardEvent, cb: (text:string) => string|void) => {
    evt.preventDefault();
    const text = evt.clipboardData?.getData('text').trim();
    return cb(text || '');
};
