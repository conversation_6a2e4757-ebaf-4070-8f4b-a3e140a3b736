<template>
  <q-chip v-bind="{ clickable: true, color: 'ir-bg2', ...$attrs }">
    <slot name="left"></slot>
    <span v-if="!modelValue">{{emptyLabel}}</span>
    <span v-else>{{formatDate(modelValue, format)}}</span>
    <slot name="right">
      <q-icon class="q-ml-xs" name="mdi-menu-down"></q-icon>
    </slot>
    <q-popup-proxy>
      <q-card>
        <q-date
            :mask="format"
            :model-value="modelValue ? formatDate(modelValue, format) : undefined"
            @update:model-value="handleInput"
            :dark="dark"
            :color="color"
        ></q-date>
      </q-card>
    </q-popup-proxy>
  </q-chip>
</template>

<script setup>

  import { formatDate } from 'src/utils/date-utils';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { type: String, required: true },
    format: { default: 'MM-DD-YYYY' },
    emptyLabel: { default: 'Choose Date'},
    dark: Boolean,
    color: String
  })

  const handleInput = (val) => {
    emit('update:model-value', new Date(val));
  }

</script>

<style lang="scss" scoped>

</style>
