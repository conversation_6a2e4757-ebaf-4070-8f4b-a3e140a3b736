<template>
  <q-card flat :dark="dark">
    <div class="row items-center">
    <div :class="`col-${cols} col-sm-${sm} col-md-${md} col-lg-${lg} col-xl-${xl} q-pa-sm`">
      <inline-date :disable="disable" keep-time label="From (date)" v-bind="startProps" @update:model-value="handleStart"></inline-date>
    </div>
    <div :class="`col-${cols} col-sm-${sm} col-md-${md} col-lg-${lg} col-xl-${xl} q-pa-sm`">
      <inline-time :disable="disable" label="@ (time)" v-bind="startProps" @update:model-value="handleStart"></inline-time>
    </div>

    <div :class="`col-${cols} col-sm-${sm} col-md-${md} col-lg-${lg} col-xl-${xl} q-pa-sm`">
      <inline-date :disable="disable" keep-time label="To (date)" v-bind="endProps" @update:model-value="handleEnd"></inline-date>
    </div>
    <div :class="`col-${cols} col-sm-${sm} col-md-${md} col-lg-${lg} col-xl-${xl} q-pa-sm`">
      <inline-time :disable="disable" label="@ (time)" v-bind="endProps" @update:model-value="handleEnd"></inline-time>
    </div>
    </div>
    <div v-if="!noRepeat">
      <q-btn
        dense
        flat
        @click="setRules = !setRules"
        :style="{ color: setRules === 0 ? 'var(--ir-grey-6)' : 'var(--q-nice)' }"
        class="q-pa-sm q-mr-sm"
        icon="mdi-repeat"
      ></q-btn>
      <q-slide-transition>
        <div v-if="setRules">
          <r-rule-form :dark="dark" @update:model-value="handleRecurrence" :model-value="form.recurrence"></r-rule-form>
        </div>
      </q-slide-transition>
    </div>
    <div class="row justify-end q-pa-sm" v-if="requireSave">
      <q-btn label="save" size="sm" push glossy color="primary" @click="$emit('update:model-value', form)"></q-btn>
    </div>
  </q-card>
</template>

<script>
  import InlineDate from './InlineDate.vue';
  import InlineTime from './InlineTime.vue';
  import RRuleForm from '../recurrance/RRuleForm.vue';
  import { date } from 'quasar';
  import { _get } from 'symbol-syntax-utils';

  export default {
    name: 'InlinePicker',
    components: { RRuleForm, InlineTime, InlineDate },
    props: {
      disable: Boolean,
      noRepeat: Boolean,
      cols: { type: Number, default: 6 },
      sm: { type: Number, default: 6 },
      md: { type: Number, default: 3 },
      lg: { type: Number, default: 3 },
      xl: { type: Number, default: 3 },
      minuteInterval: { type: Number, default: 5 },
      requireSave: Boolean,
      inputSize: String,
      dark: Boolean,
      color: String,
      buttonColor: String,
      noButton: Boolean,
      right: Boolean,
      noButtonNow: Boolean,
      overlay: Boolean,
      autoClose: Boolean,
      noHeader: Boolean,
      textClass: { type: String, default: 'text-xs text-mb-xs' },
      borderColor: String,
      displayOff: Boolean,
      inline: Boolean,
      format: String,
      model_start: [Date, String],
      model_end: [Date, String],
      dateFormat: String,
      dateDisplay: { type: String, default: 'DD MMM, YYYY' },
      timeDisplay: {type: String, default: 'h:mm a'},
      modelValue: Object,
      outlined: Boolean
    },
    mounted(){

    },
    data(){
      return {
        form: { start: new Date(), end: new Date(), recurrence: undefined },
        setRules: false
      };
    },
    watch: {
      model_start: {
        immediate: true,
        handler(newVal){
          if(newVal){
            this.form.start = newVal;
            if(this.model_end) this.form.end = new Date(this.model_end);
            else this.form.end = date.addToDate(new Date(newVal), { 'hours': 1});
          }
        }
      },
      modelValue: {
        immediate: true,
        handler(newVal){
          if(newVal) this.form = newVal;
        }
      }
    },
    computed: {
      startProps(){
        let obj = Object.assign({}, this.$props);
        obj.value = _get(this.form, 'start', new Date());
        return obj;
      },
      endProps(){
        let obj = Object.assign({}, this.$props);
        obj.value = _get(this.form, 'end', new Date());
        return obj;
      },
    },
    methods: {
      handleStart(date){
        let val = this.form ? Object.assign({}, this.form) : {};
        val.start = date;
        console.log('emit start', val, date);
        this.form = val;
        if(!this.requireSave) {
          this.$emit('update:model-value', val);
        }
      },
      handleRecurrence(val){
        console.log('handle recurrence', val);
        this.form.recurrence = val;
        if(!this.requireSave) {
          this.$emit('update:model-value', this.form);
        }
      },
      handleEnd(date){
        let val = this.form ? Object.assign({}, this.form) : {};
        val.end = date;
        this.form = val;
        if(!this.requireSave) {
          this.$emit('update:model-value', val);
        }
      }
    }
  };
</script>
