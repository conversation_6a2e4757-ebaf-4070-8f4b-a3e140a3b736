<template>
  <div :style="{ width: '100%', ...$attrs.divStyle }">
    <div class="q-pa-md">
      <q-input filled @update:model-value="typeDate" :model-value="formatDate(model, format)" hint="YYYY/MM/DD">
        <template v-slot:prepend>
          <q-icon name="event" class="cursor-pointer">
            <q-popup-proxy transition-show="scale" transition-hide="scale">
              <q-date @update:model-value="handleInput()" v-model="model" :mask="format">
                <div class="row items-center justify-end">
                  <q-btn v-close-popup label="Close" color="primary" flat />
                </div>
              </q-date>
            </q-popup-proxy>
          </q-icon>
        </template>

        <template v-slot:append v-if="timeOn">
          <q-icon name="access_time" class="cursor-pointer">
            <q-popup-proxy transition-show="scale" transition-hide="scale">
              <q-time @update:model-value="handleInput()" v-model="model" :mask="format" format24h>
                <div class="row items-center justify-end">
                  <q-btn v-close-popup label="Close" color="primary" flat />
                </div>
              </q-time>
            </q-popup-proxy>
          </q-icon>
        </template>
      </q-input>
    </div>
  </div>
</template>

<script>
  import { date } from 'quasar';

  export default {
    name: 'DateInput',
    props: {
      format: { type: String, default: 'YYYY/MM/DD h:mm a'},
      timeOn: Boolean,
      modelValue: [Date, String],
      emitString: Boolean
    },
    data(){
      return {
        model: new Date()
      };
    },
    watch: {
      modelValue: {
        immediate: true,
        handler(newVal){
          if(newVal) this.model = newVal;
        }
      }
    },
    methods: {
      formatDate(val){
        return date.formatDate(val, this.format);
      },
      typeDate(evt){
        let d = date.extractDate(evt, 'YYYY/MM/DD');
        let year = parseInt(date.formatDate(d, 'YYYY'));
        let test = year > 1950;
        if(test){
          this.model = d;
          this.handleInput();
        }
      },
      handleInput(){
        let d = this.model;
        if(!this.emitString){
          d = date.extractDate(this.model, this.format);
        }
        this.$emit('update:model-value', d);
      }
    }
  };
</script>

<style scoped>

</style>
