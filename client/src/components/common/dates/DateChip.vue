<template>
  <chip-btn v-bind="{size}">
    <template v-slot:menu>
      <slot name="menu"></slot>
    </template>
    <template v-slot:default>
      <q-avatar :size="size" :color="avatarColor" :text-color="avatarTextColor">
        <div class="col-12">
          <div class="row justify-center items-center" style="font-size: .6rem; padding-bottom: 2px;">{{ month }}</div>
          <div class="row justify-center items-center text-weight-bold" style="font-size: .8rem">{{ day }}</div>
        </div>
      </q-avatar>
    </template>
    <template v-slot:label>
      <div v-if="!hideText" :class="textClass">{{ date }}</div>
    </template>
    <template v-slot:right>
      <slot name="right">
        <q-btn v-if="removable" dense flat icon="mdi-close" @click.stop="$emit('remove', value)"></q-btn>
      </slot>
    </template>
  </chip-btn>
</template>

<script>
  import ChipBtn from 'src/components/common/btns/ChipBtn.vue';

  import {formatDate} from 'src/utils/date-utils';

  export default {
    components: { ChipBtn },
    props: {
      avatarColor: { default: 'ir-grey-3' },
      avatarTextColor: { default: 'black' },
      chipAttrs: Object,
      removable: Boolean,
      dark: Boolean,
      bgIn: String,
      size: { type: String, default: '30px' },
      borderRadius: {
        type: String,
        default: '5px'
      },
      boxShadow: {
        type: String,
        default: '1px 1px 4px rgba(0,0,0,.4)'
      },
      hideText: Boolean,
      textClass: {
        type: String,
        default: 'tw-four font-1r'
      },
      modelValue: [Date, String],
      format: {
        type: String,
        default: 'MMM DD, YYYY'
      }
    },
    computed: {
      month() {
        return formatDate(this.modelValue, 'MMM');
      },
      day() {
        return formatDate(this.modelValue, 'D');
      },
      date() {
        return formatDate(this.modelValue, this.format);
      }
    }
  };
</script>

<style scoped lang="scss">
  .close_btn {
    height: 20px;
    width: 20px;
    border-radius: 10px;
  }
</style>
