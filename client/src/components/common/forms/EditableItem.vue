<template>
  <div class="_fw">
  <div class="__ei" :style="`grid-template-columns: ${leftWidth} 1fr`" @click="emit('edit')">
      <div class="_fw">
        <slot name="left">
          <div class="row justify-center">
            <div v-if="props.label" class="__label">
              {{ props.label }}
            </div>
            <q-icon v-else-if="props.icon" v-bind="useIcon">
              <q-tooltip>{{ props.tooltip }}</q-tooltip>
            </q-icon>
          </div>
        </slot>
      </div>
      <div class="_fw">
        <slot name="top"></slot>
        <slot name="right">
          <q-slide-transition>
            <template v-if="editing">
              <slot name="editing"></slot>
            </template>
            <template v-else>
              <slot name="default"></slot>
            </template>
          </q-slide-transition>
          <slot name="bottom"></slot>
        </slot>
      </div>

    </div>
    <div class="__sep" v-if="separator">
      <q-separator></q-separator>
    </div>
  </div>
</template>

<script setup>

  import {computed} from 'vue';

  const props = defineProps({
    icon: { type: [Object, String] },
    tooltip: String,
    editing: Boolean,
    separator: { default: true },
    leftWidth: { type: String, default: '85px' },
    label: String
  });

  const emit = defineEmits(['edit']);

  const useIcon = computed(() => {
    if (typeof props.icon === 'string') {
      return {
        name: props.icon,
        size: '25px'
      };
    } else return props.icon;
  });

</script>

<style scoped>
  .__ei {
    width: 100%;
    display: grid;
    grid-template-rows: 100%;
    align-items: center;
  }
  .__sep {
    width: 100%;
    padding: 5px 5%;
  }

  .__label {
    font-size: .7rem;
    width: 100%;
  }

</style>
