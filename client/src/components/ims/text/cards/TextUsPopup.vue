<template>
  <common-dialog @update:model-value="openUp" v-model="open" setting="smmd">
    <div class="_fw q-pa-md bg-ir-bg text-ir-text relative-position">
      <div class="row justify-center">
        <div id="qrcode" class="_fa __qr"></div>
      </div>
      <div class="text-center text-xs tw-five">Scan</div>
      <div class="_fw q-py-md">
        <div class="text-center text-xs tw-five">
          Or text
          <q-chip dense square color="white" class="tw-six text-accent text-xs" clickable @click="copy">
            <q-icon class="q-mr-xs text-sm text-accent" name="mdi-content-copy"></q-icon>
            {{ fullLink }}
          </q-chip>
          to
          <div class="_fw row justify-center q-py-sm">
            <q-chip dense square color="primary" class="tw-six text-white text-sm">{{ smsNumber.national }}</q-chip>
          </div>
          <div class="text-center">a human will text you back.</div>
        </div>
        <div class="text-center text-xxs q-pt-md">Disclaimer: If your carrier charges you per-text because this is 2002,
          they will charge you to send a text to us. If you didn't realize that - that's ok, don't text us. It
          will save us both pain.
        </div>
      </div>

    </div>
  </common-dialog>
</template>

<script setup>
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';

  import {smsNumber} from 'components/ims/utils';
  import {computed, nextTick, ref, watch} from 'vue';
  import {LocalStorage, SessionStorage} from 'symbol-auth-client';
  import {Screen} from 'quasar';
  import {$successNotify} from 'src/utils/global-methods';
  import QRCode from 'qrcode-svg';

  const emit = defineEmits(['update:model-value'])
  const props = defineProps({
    imsId: { type: String },
    modelValue: Boolean
  })
  const fullLink = computed(() => `${props.imsId || SessionStorage.getItem('ims_id') || LocalStorage.getItem('fpId')}`)

  const open = ref(false);


  const link = ref({
    lg: `sms:${smsNumber.e164}&body=Hit send to open text chat session id ${fullLink.value}`,
    sm: `sms:${smsNumber.e164}&body=Hit send to open text chat session id ${fullLink.value}`
  });

  const qrcode = ref('');
  const setLink = () => {
    qrcode.value = new QRCode({
      content: link.value.lg,
      color: '#0A1F1D',
      background: '#ffffff',
      container: 'svg-viewbox', //Responsive use
      join: true //Crisp rendering and 4-5x reduced file size
    }).svg();

    setTimeout(() => {
      const el = document.getElementById('qrcode');
      if (el) el.innerHTML = qrcode.value;
    }, 200);
  };

  const openUp = (val) => {
    if(val) {
      if (Screen.lt.md) {
        const a = document.createElement('a');
        a.href = link.value.sm
        a.display = 'none';
        a.target = '_blank';
        document.body.appendChild(a);
        a.click()
        document.body.removeChild(a);
      } else {
        emit('update:model-value', val);
        setLink();
        setTimeout(() => {
          open.value = true;
        }, 100);
      }
    } else emit('update:model-value', val);
  }

  watch(() => props.modelValue, (nv) => {
    if(nv !== open.value) openUp(nv);
  }, { immediate: true });

  const copyTo = ref(false)
  const copy = () => {
    nextTick(async () => {
      await window.navigator.clipboard.writeText(fullLink.value);
      if (!copyTo.value) {
        $successNotify('Copied id')
        copyTo.value = true;
        setTimeout(() => copyTo.value = false, 4000);
      }
    })
  }

</script>

<style lang="scss" scoped>
  .__qr {
    width: 300px;
    height: 300px;
    max-width: 90%;
    max-height: 90vw;
  }
</style>
