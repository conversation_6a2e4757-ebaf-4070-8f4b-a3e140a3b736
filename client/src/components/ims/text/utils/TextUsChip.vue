<template>
  <q-chip v-bind="{ clickable: true, ...$attrs}" @click="openUp">
    <slot name="default">
      <q-icon name="mdi-message" color="accent"></q-icon>
      <span class="q-ml-sm">{{label}}</span>
    </slot>

    <text-us-popup v-model="popup"></text-us-popup>
  </q-chip>

</template>

<script setup>
  import TextUsPopup from 'components/ims/text/cards/TextUsPopup.vue';

  import {ref} from 'vue';

  const popup = ref(false);

  const emit = defineEmits(['open'])
  const props = defineProps({
    label: { default: 'Text us' },
    imsId: { type: String },
  })

  const openUp = () => {
    emit('open')
    popup.value = true
  }


</script>

<style lang="scss" scoped>

</style>
