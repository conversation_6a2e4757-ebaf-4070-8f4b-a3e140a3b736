<template>
  <q-item v-bind="{...$attrs}">
    <q-item-section>
      <q-item-label class="font-7-8r tw-five text-ir-deep">
        <span v-if="!im.messages?.length" class="text-italic"> - no messages</span>
        <span v-else>
        <span class="text-a4 tw-six">{{ firstSenderName }}</span>: <span
            v-html="$limitStr(firstMessage.body, 30, '...')"></span></span>
      </q-item-label>
      <q-item-label class="font-3-4r text-ir-deep tw-five">{{ $ago(im.updatedAt) }}</q-item-label>
    </q-item-section>
  </q-item>
  <slot name="bottom" v-bind="{ im }"></slot>
</template>

<script setup>
  import {computed} from 'vue';
  import {$ago, $limitStr} from 'src/utils/global-methods';

  const props = defineProps({
    modelValue: { required: true }
  })

  const im = computed(() => props.modelValue || {});

  const firstMessage = computed(() => {
    const messages = im.value.messages || []
    const { login, fp } = im.value.participant || {}
    const participantMessages = messages.filter(a => [login, fp].includes(a.pid))
    return participantMessages[0] || messages[0]
  })

  const firstSenderName = computed(() => {
    const pid = firstMessage.value.pid
    const { login, fp, name } = im.value.participant || {}
    return [login, fp].includes(pid) ? name || 'You' : (im.value.support || {})[pid]?.name || 'Support'
  })

</script>

<style lang="scss" scoped>

</style>
