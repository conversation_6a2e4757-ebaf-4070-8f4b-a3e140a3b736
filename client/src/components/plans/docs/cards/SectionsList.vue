<template>
  <q-list separator>
    <q-expansion-item
        :hide-expand-icon="editing"
        expand-icon="mdi-menu-down"
        :content-inset-level=".5"
        group="0"
        v-for="(k, i) in Object.keys(modelValue || {})"
        :default-opened="i === 0"
        :key="`sec-${i}`"
        @update:model-value="val => val ? emit('update:active', `${k}.1`) : undefined"
    >
      <template v-slot:header>
<!--        MAIN SECTION-->
        <q-item class="_fw _p0">
          <q-item-section avatar>
            <div class="flex items-center">
<!--              MOVE UP AND DOWN-->

<!--              SECTION NUMbER-->
              <div :class="`num-font tw-six q-pl-sm ${!isActive(k) ? 'text-ir-grey-7' : 'text-p6 '}`">{{ k }}</div>
            </div>
          </q-item-section>
          <q-item-section>
            <q-item-label :class="`tw-six ${isActive(k) ? 'text-p6' : 'text-ir-grey-7'}`">
              {{ $limitStr(modelValue[k].title || 'Untitled', 40, '...') }}
            </q-item-label>
          </q-item-section>
<!--          EDIT MENU-->
          <q-item-section side v-if="editing">
            <q-btn dense flat icon="mdi-dots-vertical" size="sm" color="ir-grey-6">
              <q-popup-proxy breakpoint="50000">
                <div class="w400 mw100 q-pa-md bg-white">
<!--                  SEcTION TITLE-->
                  <div class="font-7-8r tw-six">Section Title</div>
                  <q-input placeholder="Enter Section Title..." @update:model-value="emit('title', k, $event)"
                           :model-value="modelValue[k].title"></q-input>
                  <div class="row items-center">
<!--                    SECTION ORDER-->
                  <q-btn class="q-mt-md tw-six" :label="`${k}`" icon-right="mdi-menu-down" flat size="sm"
                         color="ir-grey-7">
                    <q-menu>
                      <div class="w200 bg-white">
                        <q-list separator>
                          <q-item-label header>Reorder Section</q-item-label>
                          <q-item v-for="toKey in Object.keys(modelValue)" :key="`order-${toKey}`" clickable
                                  @click="emit('reorder', k, toKey)">
                            <q-item-section>
                              <q-item-label>{{ toKey }}</q-item-label>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </div>
                    </q-menu>
                  </q-btn>
                  <q-space></q-space>
<!--                    REMOVE SECTION-->
                  <remove-button @remove="emit('remove', k)" name="Section"></remove-button>
                  </div>
                </div>
              </q-popup-proxy>
            </q-btn>
          </q-item-section>
        </q-item>
      </template>
      <q-list separator class="_fw">
        <q-item :class="`_fw ${isActive(k, sub) ? 'bg-p1' : ''}`" v-for="(sub, idx) in Object.keys(modelValue[k].sections || {})" :key="`sub-${i}-${idx}`"
                clickable @click="emit('update:active', `${k}.${sub}`)">
          <q-item-section avatar>
            <div class="flex items-center">
              <!--              MOVE UP AND DOWN-->

              <div :class="`num-font text-p6 ${!isActive(k, sub) ? '' : 'tw-eight'}`">{{ k }}.{{ sub }}</div>

            </div>
          </q-item-section>
          <q-item-section>
            <q-item-label :class="`tw-five text-p6 ${isActive(k, sub) ? ' tw-eight' : ''}`">
              {{ $limitStr(modelValue[k].sections[sub].title || '', 40, '...') }}
            </q-item-label>
          </q-item-section>
        </q-item>
        <q-item v-if="editing" clickable @click="emit('add', k)">
          <q-item-section avatar></q-item-section>
          <q-item-section>
            <q-item-label class="text-ir-grey-7">Add {{k + '.' + (Object.keys(modelValue[k].sections || {}).length + 1)}}
            </q-item-label>
          </q-item-section>
          <q-item-section side>
            <q-icon size="15px" color="primary" name="mdi-plus"></q-icon>
          </q-item-section>
        </q-item>
      </q-list>
    </q-expansion-item>

  </q-list>
</template>

<script setup>
  import {$limitStr} from 'src/utils/global-methods';
  import {computed} from 'vue';
  import RemoveButton from 'components/common/buttons/RemoveButton.vue';

  const emit = defineEmits(['update:active', 'title', 'reorder', 'add'])
  const props = defineProps({
    modelValue: Object,
    active: String,
    editing: Boolean
  })

  const activeSplit = computed(() => (props.active || '0.0').split('.'))
  const isActive = (section, sub) => {
    const sec = String(section) === activeSplit.value[0]
    if (sub) return sec && String(sub) === activeSplit.value[1];
    else return sec
  }
</script>

<style lang="scss" scoped>

</style>
