<template>
  <div class="_fw">
    <div class="_fw q-py-xs mw500">
      <q-input placeholder="Search Templates..." filled v-model="search.text">
        <template v-slot:prepend>
          <q-icon name="mdi-magnify"></q-icon>
        </template>
      </q-input>
    </div>
    <div class="q-pa-md tw-six text-grey-7">Choose Template</div>
    <docs-class-filter v-model="query" v-bind="{ planClass, subClass }"></docs-class-filter>
    <div class="__dg">
      <div class="__c" v-for="(doc, i) in h$.data || []" :key="`doc-${i}`">
        <doc-card template :model-value="doc"></doc-card>
        <div class="row items-center q-pt-sm">
          <q-chip color="ir-bg2" class="font-5-8r text-uppercase tw-five" label="Template"></q-chip>
          <q-space></q-space>
          <q-btn no-caps class="tw-six" flat @click="$emit('update:model-value', doc)" size="sm">
            <span class="text-ir-text">Select Doc</span>
            <q-icon color="primary" class="q-ml-xs" name="mdi-check-circle"></q-icon>
          </q-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import DocCard from 'components/plans/docs/cards/DocCard.vue';
  import DocsClassFilter from 'components/plans/docs/lists/DocsClassFilter.vue';
  import {computed, ref} from 'vue';
  import {useDocTemplates} from 'stores/doc-templates';
  import {HFind} from 'src/utils/hFind';
  import {HQuery} from 'src/utils/hQuery';


  const store = useDocTemplates();
  const emit = defineEmits(['update:model-value', 'update:class', 'update:sub'])
  const props = defineProps({
    planClass: String,
    subClass: String
  })

  const query = ref({});

  const { search, searchQ } = HQuery({})

  const { h$ } = HFind({
    store,
    limit: ref(10),
    params: computed(() => {
      return { query: { ...searchQ.value, ...query.value } }
    })
  })


</script>

<style lang="scss" scoped>
  .__dg {
    padding: 15px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 350px));
    justify-content: center;
    grid-gap: 15px;
  }

  .__c {
    width: 100%;
    border-radius: 7px;
    padding: 20px;
    border: solid 2px #999;
  }
</style>
