<template>
  <q-page class="row justify-center">
    <div class="pd5 pw4 _fw">
      <docs-display :plan="planId"></docs-display>
    </div>
  </q-page>
</template>

<script setup>

  import DocsDisplay from 'components/plans/docs/cards/DocsDisplay.vue';
  import {computed} from 'vue';
  import {LocalStorage} from 'symbol-auth-client';
  import {useRoute} from 'vue-router';

  const route = useRoute();

  const planId = computed(() => route.params.planId || LocalStorage.getItem('plan_id'))
</script>

<style lang="scss" scoped>

</style>
