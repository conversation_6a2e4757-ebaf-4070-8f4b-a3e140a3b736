<template>
  <div class="_fw">
    <div class="_f_g">
      <div class="_f_l _f_chip">Name & Type</div>
      <div class="q-pa-sm row">
        <div class="col-12 col-md-6 q-pa-sm">
          <q-input input-class="tw-six" v-model="form.name" placeholder="Template Name..."></q-input>
          <q-input class="q-mt-sm" autogrow filled v-model="form.description" placeholder="Description..."></q-input>
          <div class="row q-py-md">
            <div class="col-6">
              <q-select
                  v-model="form.class"
                  label="Doc Class"
                  :options="Object.keys(planClasses)"
                  filled
              >
                <template v-slot:option="scope">
                  <q-item clickable @click="scope.toggleOption(scope.opt)">
                    <q-item-section avatar>
                      <q-avatar size="30px" :color="planClasses[scope.opt].color"></q-avatar>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ planClasses[scope.opt].name }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
                <template v-slot:selected-item="scope">
                  <q-chip
                      dark
                      class="tw-six"
                      :color="planClasses[scope.opt].color"
                      :label="planClasses[scope.opt].name"
                  ></q-chip>
                </template>
              </q-select>
            </div>
            <div class="col-6">
              <q-select
                  label="Sub Class"
                  filled
                  v-model="form.subClass"
                  :options="planClasses[form.class]?.sub"
              >
                <template v-slot:selected-item="scope">
                  <q-chip dark class="tw-six" :color="planClasses[form.class].color" :label="scope.opt" outline></q-chip>

                </template>
              </q-select>
            </div>
          </div>
        </div>

        <div class="col-12 col-md-6 q-pa-sm">
          <q-checkbox label="SMB PTC Plan" v-model="form.smb"></q-checkbox>
        </div>

      </div>
      <div class="_f_l _f_chip">Sections</div>
      <div class="q-pa-sm">
        <div class="row justify-end">
          <q-btn push class="_p_btn" label="Save" icon-right="mdi-content-save" @click="save"></q-btn>
        </div>
        <docs-editor v-if="form._id" :id="form._id" :custom-values="customValues" v-model="form.sections"></docs-editor>
      </div>
    </div>
  </div>
</template>

<script setup>
  import DocsEditor from 'components/plans/docs/forms/DocsEditor.vue';

  import {HForm} from 'src/utils/hForm';
  import {computed, watch} from 'vue';
  import {useDocTemplates} from 'stores/doc-templates';
  import {planClasses, customValues} from '../../../utils';

  const store = useDocTemplates();

  const props = defineProps({
    modelValue: { required: false }
  })

  const formFn = (defs) => {
    return {
      name: '',
      sections: {},
      ...defs
    }
  }



  const { form, save } = HForm({
    value: computed(() => props.modelValue),
    store,
    formFn
  })

  watch(() => props.modelValue, (nv) => {
    if (nv) form.value = formFn(nv);
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
