<template>
  <div class="q-pa-md bg-ir-bg _fa">
    <template v-if="plan.vectorStoreIds?.plan_docs">
      <ai-chat
          v-bind="{
        subjectLabel: 'plan',
        subject: plan._id,
        runChat,
        chatName: 'plan_docs'
          }">
        <template v-slot:top>
          <div class="_fw  __top">
            <div class="font-1r tw-five">Ask CommonAI About {{ plan.name }}</div>
            <div class="font-7-8r">
              Docs Updated {{ $dateDisplay(doc.sectionsUpdatedAt, 'MM/DD/YYYY', 'Recently') }} - AI Last Indexed:
              {{ $dateDisplay(planUpdated, '', 'Never') }}
              <q-chip v-if="canEdit.ok" clickable size="sm" text-color="white" color="a10" label="Update Now"
                      @click="updateAi = true">
              </q-chip>
            </div>
          </div>
        </template>
      </ai-chat>
    </template>
    <template v-else>
      <div class="q-pa-lg">
        <div class="font-7-8r text-center">This plan has not been indexed for AI yet.</div>
        <div class="row justify-center q-pt-md">
          <q-btn push color="primary" v-if="canEdit.ok" @click="updateAi = true" no-caps class="tw-six">
            <span>Index Now</span>
          </q-btn>
        </div>
      </div>
    </template>
    <vector-store-updater :plan-id="planId" v-model="updateAi"></vector-store-updater>

  </div>
</template>

<script setup>
  import VectorStoreUpdater from 'components/plans/docs/ai/VectorStoreUpdater.vue';
  import AiChat from 'components/ai/chat/AiChat.vue';

  import {computed, onMounted, ref} from 'vue';
  import {$dateDisplay, $errNotify} from 'src/utils/global-methods';
  import {usePlanDocs} from 'stores/plan-docs';
  import {clientCanU} from 'src/utils/ucans/client-auth';
  import {loginPerson} from 'stores/utils/login';
  import {idGet} from 'src/utils/id-get';
  import {usePlans} from 'stores/plans';
  import showdown from 'showdown';
  import {useAiChats} from 'stores/ai-chats';

  const { login, person } = loginPerson();
  const docStore = usePlanDocs();
  const planStore = usePlans()
  const aiChatStore = useAiChats();

  const props = defineProps({
    modelValue: { required: true },
    planId: { required: true },
    orgId: { required: true },
    store: { required: true },
  })

  const updateAi = ref(false);

  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => props.planId)
  })

  const doc = computed(() => props.modelValue);
  const planUpdated = computed(() => plan.value.vectorStoreIds?.plan_docs.updatedAt)
  // const isOutOfDate = computed(() => {
  //   if (!planUpdated.value) return true;
  //   const pu = new Date(planUpdated.value).getTime();
  //   return new Date(doc.value.sectionsUpdatedAt || pu).getTime() > pu
  // });
  const history = ref([]);
  const active = ref({});
  const others = ref([]);
  const setActive = (idx) => {
    for (let i = 0; i < history.value.length; i++) {
      if (i === idx) active.value = history.value[i];
      else others.value.push(history.value[i]);
    }
  }
  const text = ref('');
  const response = ref();

  const thinking = ref(false);

  const typeText = ref('');
  const typing = ref(false);
  const typeResponse = (idx, backlog = '') => {
    if (idx < typeText.value.length) {
      typing.value = true;
      let to = 50;
      const nextChar = typeText.value.substring(idx, idx + 1)
      if (backlog) {
        /** if not closing tag and length of backlog isn't excessive - continue backlogging */
        if (!((nextChar === '>' && backlog.charAt(backlog.length - 1) === '/') || backlog.length > 30)) return typeResponse(idx + 1, backlog + nextChar)
      } else if (nextChar === '<') {
        return typeResponse(idx + 1, nextChar);
      }
      history.value[0].answer += backlog + nextChar;
      setTimeout(() => typeResponse(idx + 1), to)
    } else {
      typing.value = false;
      typeText.value = ''
    }
  }
  const err = ref()
  const send = async () => {
    if (!person.value._id) return err.value = 'Must login/register to utilize AI Chat'
    err.value = ''
    if (thinking.value) return;
    if (!text.value) return;
    const tv = text.value.slice(0)
    if (tv.length < 15) return $errNotify('That question isn\'t detailed enough');
    thinking.value = true;
    history.value.unshift({ createdAt: new Date(), question: tv, answer: '...' })
    setActive(0);
    text.value = '';
    const res = await docStore.get(doc.value._id, { runJoin: { ai_query: { text: tv } } })
        .catch(err => {
          console.error(`Error sending query: ${err.message}`)
          thinking.value = false;
        })
    thinking.value = false;
    if (res) {
      response.value = res._fastjoin.ai_response;
      history.value[0].text = response.value.text;
      const converter = new showdown.Converter();
      typeText.value = converter.makeHtml(response.value.text);
      history.value[0].answer = '';
      typeResponse(0);
      history.value[0].annotations = response.value.annotations;
    }
    console.log('got res', res);
    text.value = '';
  }

  const { canEdit } = clientCanU({
    subject: plan,
    login,
    caps: computed(() => {
      const arr = [['orgs', 'WRITE']]
      if (props.orgId) {
        arr.push([`orgs:${props.orgId}`, 'WRITE'])
        arr.push([`orgs:${props.orgId}`, 'orgAdmin'])
      }
      if (props.planId) {
        arr.push([`plans:${props.planId}`, 'planAdmin'])
      }
      return arr;
    }),
    cap_subjects: computed(() => [props.orgId, props.planId])
  })

  const loadHistory = async () => {
    const ai_chat = aiChatStore.find({
      query: {
        $limit: 1,
        chatId: `${person.value._id}|${plan.value._id}|plan_docs`
      }
    })
    const converter = new showdown.Converter()
    if (ai_chat.data[0].chats) {
      history.value = ai_chat.data[0].chats.filter(a => a.subject === 'plan_docs').map(a => {
        return {
          ...a,
          stale: true,
          text: a.answer,
          answer: converter.makeHtml(a.answer)
        }
      });
      setActive(0);
    }
  }

  const runChat = async (text) => {
    try {
      const res = await docStore.get(doc.value._id, { runJoin: { ai_query: { text } } })
      return res._fastjoin.ai_response
    } catch (e) {
      console.log(`Error running chat in plan docs: ${e.message}`)
    }
  }

  onMounted(() => {
    setTimeout(() => {
      loadHistory()
    }, 1000)
  })
</script>

<style lang="scss" scoped>

  .__top {
    padding: 6px 10px;
    background: var(--ir-bg2);
  }


</style>
