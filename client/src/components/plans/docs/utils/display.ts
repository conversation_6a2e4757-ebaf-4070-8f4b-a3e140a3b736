import {computed, ref, watch, Ref, ComputedRef} from 'vue';
import {getDisplayPlanDoc, planDocPaths} from 'components/plans/utils/index.js';
import {$copyTextToClipboard} from 'src/utils/global-methods.js';
import {HFind} from 'src/utils/hFind.js';
import {usePlanDocs} from 'stores/plan-docs.js';
import {useRouter, useRoute} from 'vue-router';
import ScheduleA from 'components/plans/docs/schedules/ScheduleA.vue';
import ScheduleB from 'components/plans/docs/schedules/ScheduleB.vue';
import ScheduleC from 'components/plans/docs/schedules/ScheduleC.vue';
import ScheduleD from 'components/plans/docs/schedules/ScheduleD.vue';

import { eligibleGroups } from '../../utils/people';

export const docDisplay = (plan?:Ref<any>|ComputedRef<any>) => {
    const store = usePlanDocs() as any;
    const router = useRouter();
    const route = useRoute()

    //utility function that loads the eligible groups who are/can participate in this health plan it loads plan the sponsoring org (byOrg) and the groups or related groups for that org
    const { fullPlan, byOrg, groupStore, planStore, planId } = eligibleGroups(plan);

    const docs:Ref<any> = ref({})

    const planDocs = computed(() => {
        const obj:any = {};
        for(const k in docs.value || {}){
            if(k !== 'spd') obj[k] = docs.value[k];
        }
        return obj;
    })

    //summary plan description (SPD) is the part of the plan documents that legally needs to be distributed to all participants. It's a summary of the plan
    const spd = computed(() => {
        return docs.value[fullPlan.value?.spd];
    })

    const { docPaths } = planDocPaths(fullPlan);

    const planDocIds = computed(() => {
        const list = [];
        for (const p in docPaths.value) {
            if (docPaths.value[p]?.active) list.push(docPaths.value[p]?.doc);
        }
        return list;
    })

    const limit = ref(20);
    const { h$ } = HFind({
        pause: computed(() => !fullPlan.value),
        store,
        limit,
        params: computed(() => {
            return {
                query: {
                    _id: { $in: planDocIds.value || [] }
                }
            }
        })
    })

    const share = () => {
        const { href } = router.resolve({ name: 'plan-docs', params: { planId: fullPlan.value._id }})
        $copyTextToClipboard(`${window.location.origin}${href}`, 'Link Copied');
    }

    const isSet = ref(false);
    watch(() => h$.total, async (nv, ov) => {
        if(nv && nv !== ov){
            for(const id of planDocIds.value || []){
                const doc = store.getFromStore(id);
                if(doc.value){
                    docs.value[id] = doc.value;
                    setTimeout(() => {
                        for(const d in docs.value){
                            docs.value[d].sections = getDisplayPlanDoc(docs.value[d].sections, fullPlan.value);
                        }
                    }, 3000);
                }
            }
            isSet.value = true;
        }
    }, { immediate: true })

    const openPrint = () => {
        const { href } = router.resolve({ name: 'doc-print', params: { planId: fullPlan.value._id }});
        window.open(`${window.location.origin}${href}`, '_blank');
    }

    const schedules =  {
        'a': {
            component: ScheduleA,
            attrs: { plan: plan?.value }
        },
        'b': {
            component: ScheduleB,
            attrs: { plan: plan?.value }
        },
        'c': {
            component: ScheduleC,
            attrs: { plan: plan?.value, byOrg }
        },
        'd': {
            component: ScheduleD,
            attrs: { plan: plan?.value }
        }
    }

    return {
        limit,
        schedules,
        share,
        byOrg,
        isSet,
        docs,
        fullPlan,
        router,
        route,
        openPrint,
        planDocs,
        spd,
        groupStore,
        planStore,
        planId,
        store
    }
}
