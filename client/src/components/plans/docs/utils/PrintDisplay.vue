<template>
  <div id="PrintDisplay" style="width: 100%;">
    <div style="padding: 4vh 4vw; border-radius: 10px; border: solid .1px #999; width: 100%;">
      <div style="font-size: 1.25rem; font-weight: 600; color: #999; padding: 20px 0;">Contents</div>
      <plan-key :model-value="planDocs"></plan-key>
    </div>

    <div v-for="(docId, i) in Object.keys(planDocs)" :key="`plan-${i}`" style="width: 100%">
      <div style="font-size: 2rem; font-weight: 800; padding: 20px 0;">{{ planDocs[docId].name }}</div>


      <div style="width: 100%" v-for="(k, idx) in Object.keys(planDocs[docId].sections || {})"
           :key="`section-${i}-${idx}`">
        <div style="font-size: 1.5rem; font-weight: 700; padding: 15px 0;"><span class="num-font">{{ k || '' }}.</span>&nbsp;
          {{ planDocs[docId].sections[k]?.title }}
        </div>
        <div style="width: 100%;" v-for="(sub, index) in Object.keys(planDocs[docId].sections[k]?.sections)"
             :key="`sub-${i}-${idx}-${index}`">

          <div style="font-size: 1.25rem; font-weight: 600; padding: 10px 0; color: #666;"><span class="num-font">{{ `${k}.${sub}` }}</span> -
            {{ planDocs[docId].sections[k]?.sections[sub]?.title }}</div>
          <md-preview :model-value="planDocs[docId].sections[k].sections[sub]?.body"></md-preview>

        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
  import PlanKey from 'components/plans/docs/utils/PlanKey.vue';
  import {MdPreview} from 'md-editor-v3';

  const props = defineProps({
    planDocs: Object
  })
</script>

<style lang="scss" scoped>

</style>
