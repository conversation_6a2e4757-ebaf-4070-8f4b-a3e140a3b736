<template>
  <div class="w400 mw100 q-pa-lg">
    <div class="font-1r tw-six">Applicable Large Employer (ALE)</div>
    <div class="font-3-4r">An employer, or group of affiliated employers, who have at least 50 Full Time/Full Time Equivalent (FTE) Employees.<br><br>Take all employees working more than 30 hours/week or 130 hours/month in {{lastYear}}. Also combine FTE by adding together part time employees' hours. So 2 - 15 hour/week employees makes 1 - 30 hours/week employee equivalent. Get this count for each month and divide the total months you were in business for {{lastYear}}.<br><br>If you were not in business in {{lastYear}}, ALE is based on the number of employees you expect to have in {{lastYear + 1}}</div>
  </div>
</template>

<script setup>

  import {ref} from 'vue';

  const lastYear = ref(new Date().getFullYear() - 1)
</script>

<style lang="scss" scoped>

</style>
