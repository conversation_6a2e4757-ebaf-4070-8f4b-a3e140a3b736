<template>
  <div class="flex items-center">
    <q-chip
        v-bind="{
        color: 'white',
        label: !modelValue || multiple ? label : modelValue,
        iconRight: 'mdi-menu-down',
        clickable: true,
        ...$attrs
      }"
    >
      <q-menu>
        <q-list separator>
          <q-item v-for="(yr, i) in years" :key="`yr-${i}`" clickable @click="emitUp(yr)">
            <q-item-section>
              <q-item-label>{{ yr }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-menu>
    </q-chip>
    <template v-if="multiple">
      <q-chip v-for="(yr, i) in modelValue || []" :key="`y-${i}`" v-bind="{ color: 'white', label: yr, ...$attrs }" removable icon-remove="mdi-close" @remove="emitUp(yr)"></q-chip>
    </template>
  </div>
</template>

<script setup>

  import {ref} from 'vue';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { type: [String,Number] },
    multiple: Boolean,
    label: { default: 'Plan Year' }
  })

  const yr = new Date().getFullYear();
  const getList = (num = 3) => {
    const list = [];
    for (let i = 0 - num; i <= num; i++) {
      list.push(String(yr + i))
    }
    return list.sort((a, b) => b-a);
  }
  const years = ref(getList());

  const emitUp = (val) => {
    if (props.multiple) {
      const list = [...props.modelValue];
      const idx = list.indexOf(val);
      if (idx > -1) list.splice(idx, 1);
      else list.push(val);
      emit('update:model-value', list.sort((a, b) => b - a));
    } else emit('update:model-value', val);
  }
</script>

<style lang="scss" scoped>

</style>
