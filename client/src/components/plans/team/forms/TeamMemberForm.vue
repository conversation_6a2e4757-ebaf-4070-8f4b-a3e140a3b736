<template>
  <div class="_fw">
    <div class="q-pa-sm font-1r tw-six">Allow Pricing Bids</div>

    <q-tab-panels :model-value="!!hostMenu" class="_panel" animated>
      <q-tab-panel :name="false" class="_panel">
        <div class="_form_grid _f_g_r __role" v-for="key in ['care_director', 'plan_guide', 'compliance', 'finance']"
             :key="`key-${key}`">
          <div class="_form_label">Role</div>
          <div class="q-pa-sm">
            <role-chip :model-value="key"></role-chip>
          </div>
          <div class="_form_label">Accept Bids</div>
          <div class="q-pa-sm">
            <q-radio :model-value="!!form[key]?.public" @update:model-value="setPublic(key, $event)" :val="false"
                     label="By Invite"></q-radio>
            <q-radio :model-value="!!form[key]?.public" @update:model-value="setPublic(key, $event)" :val="true"
                     label="From Anyone"></q-radio>
          </div>
          <template v-if="!form[key]?.public">
            <div class="_form_label">Who can bid</div>
            <div class="q-pa-sm">
              <div class="_fw row items-center cursor-pointer">
                <div>
                <avatar-row v-if="form[key]?.hosts?.length" :limit="3" :model-value="form[key]?.hosts" path="hosts"
                            :avatar-store="hostStore" name-path="dba">
                  <template v-slot:menu>
                    <q-menu>
                      <div class="w300 mw100 q-pa-sm bg-white">
                        <bidder-list :role="roles[key]" :hosts="form[key]?.hosts || []" @remove="removeHost(key, $event)"></bidder-list>
                      </div>
                    </q-menu>
                  </template>
                </avatar-row>
                </div>
                <q-btn size="xs" round icon="mdi-plus" color="accent" @click="hostMenu = key"></q-btn>
              </div>

            </div>
          </template>
          <div class="_form_label">Cap Fee</div>
          <div class="q-pa-sm">
            <fee-chip
                picker
                :model-value="form"
                @update:fee="setFee(key, $event)"
                @update:fee-type="setFeeType(key, $event)"
            ></fee-chip>
          </div>
          <div class="_form_label">Invite Url</div>
          <div class="q-pa-sm">
            <link-card :id="`lc-${key}`" :url="`https://commoncare.org/join-team/${plan._id}/${key}`"></link-card>
          </div>
        </div>
      </q-tab-panel>
      <q-tab-panel :name="true" class="_panel">
        <div class="q-pt-sm row items-center">
          <q-btn dense flat icon="mdi-chevron-left" color="primary" @click="hostMenu = undefined"></q-btn>
          <role-chip :model-value="hostMenu"></role-chip>
        </div>
        <div class="_fw bg-white">
          <q-input dense filled v-model="search.text"
                   :placeholder="`Search ${roles[hostMenu]?.label || 'companie'}s`"></q-input>
          <q-list separator>
            <q-item dense v-if="h$.isPending">
              <q-item-section avatar>
                <q-spinner color="primary" size="30px"></q-spinner>
              </q-item-section>
            </q-item>
            <q-item-label v-else-if="!h$.total" header>None found</q-item-label>
            <q-item v-for="(h, i) in h$.data" :key="`h-${i}`" clickable @click="addHost(hostMenu, h)">
              <q-item-section avatar>
                <default-avatar :model-value="h.avatar || h._fastjoin?.org?.avatar" avatar-path=""></default-avatar>
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ h.dba || h._fastjoin?.org?.name }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>

      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import RoleChip from 'components/plans/team/cards/RoleChip.vue';
  import FeeChip from 'components/plans/team/cards/FeeChip.vue';
  import DefaultAvatar from 'components/common/avatars/DefaultAvatar.vue';
  import AvatarRow from 'components/common/avatars/AvatarRow.vue';
  import LinkCard from 'components/common/links/LinkCard.vue';
  import BidderList from 'components/plans/team/forms/BidderList.vue';

  import {computed, watch, ref} from 'vue';
  import {usePlans} from 'stores/plans';
  import {HFind} from 'src/utils/hFind';
  import {useHosts} from 'stores/hosts';
  import {HQuery} from 'src/utils/hQuery';
  import {roles} from 'components/plans/team/utils/roles';


  const planStore = usePlans();
  const hostStore = useHosts();

  const props = defineProps({
    plan: { required: true },
    id: { required: false }
  })

  const hostMenu = ref(undefined);

  const formFn = (defs) => {
    return {
      ...defs
    }
  }
  const form = ref(formFn())

  watch(() => props.plan, (nv) => {
    if (nv) form.value = formFn(nv.rfp);
  }, { immediate: true })

  const patchAll = ref({});
  const to = ref();
  const autoSave = async (obj) => {
    if (to.value) clearTimeout(to.value);
    patchAll.value = { ...patchAll.value, ...obj, $set: { ...patchAll.value?.$set, ...obj.$set } };
    if (!Object.keys(patchAll.value.$set)) delete patchAll.value.$set;
    to.value = setTimeout(async () => {
      await planStore.patch(props.plan._id || props.plan, patchAll.value);
      patchAll.value = {};
    }, 2500)
  }
  const addHost = (k, h) => {
    form.value[k] = { ...form.value[k], hosts: Array.from(new Set([...form.value[k]?.hosts || [], h._id])) }
    hostMenu.value = undefined;
    autoSave({ $set: { [`rfp.${k}.hosts`]: form.value[k].hosts } })
  }
  const setPublic = (k, val) => {
    form.value[k] = { ...form.value[k], public: val };
    autoSave({ $set: { [`rfp.${k}.public`]: val } })
  }
  const setFee = (k, val) => {
    form.value[k] = { ...form.value[k], fee: val };
    autoSave({ $set: { [`rfp.${k}.fee`]: val } })
  }
  const setFeeType = (k, val) => {
    form.value[k] = { ...form.value[k], feeType: val };
    autoSave({ $set: { [`rfp.${k}.feeType`]: val } })
  }

  const { search, searchQ } = HQuery({})
  const pause = computed(() => !hostMenu.value)
  const activeHosts = computed(() => form.value[hostMenu.value]?.hosts || [])
  const { h$ } = HFind({
    store: hostStore,
    pause,
    params: computed(() => {
      const query = {
        ...searchQ.value,
        roles: { $in: [hostMenu.value] },
        _id: { $nin: activeHosts.value }
      }
      return {
        query
      }
    })
  })

  const removeHost = (k, h) => {
    const hosts = [...form.value[k].hosts || []]
    const idx = hosts.indexOf(h._id);
    hosts.splice(idx, 1);
    form.value[k] = { ...form.value[k], hosts }
    autoSave({ $pull: { [`rfp.${k}.hosts`]: h._id } })
  }
</script>

<style lang="scss" scoped>

  .__role {
    padding: 20px min(15px, 2vw);
    margin: 10px 0;
    border-radius: 15px;
    box-shadow: 2px 2px 8px -2px var(--q-a2);
  }
</style>
