<template>
  <q-chip v-bind="{ color: 'ir-grey-2', class: 'alt-font', ...$attrs }">
    <span>{{types[mv.feeType]?.display(mv.fee) || 'Not Listed'}}</span>
    <q-icon v-if="picker" class="q-ml-sm" name="mdi-menu-down"></q-icon>
    <q-menu v-if="picker">
      <div class="_fw mw400 q-pa-md bg-white">
        <div class="_fw" v-if="mv.feeType">
          <div class="row items-center">
            <money-input :model-value="mv.fee" prefix="$" :decimal="2" dense filled v-if="mv.feeType !== 'alg'" @update:model-value="$emit('update:fee', $event)"></money-input>
            <span class="q-mx-sm">{{types[mv.feeType].label}}</span>
            <q-btn dense flat size="sm" icon="mdi-close" color="red" @click="clearAll"></q-btn>
          </div>
        </div>
        <q-list v-else separator>
          <q-item-label header>Select Fee Type</q-item-label>
          <q-item v-for="t in Object.keys(types)" :key="t" clickable @click="$emit('update:fee-type', t)">
            <q-item-section>
              <q-item-label>{{types[t].label}}</q-item-label>
              <q-item-label caption>{{types[t].description}}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-menu>
  </q-chip>
</template>

<script setup>
  import MoneyInput from 'components/common/input/MoneyInput.vue';

  import {dollarString} from 'src/utils/global-methods';
  import {computed} from 'vue';

  const emit = defineEmits(['update:fee', 'update:feeType']);
  const props = defineProps({
    modelValue: { type: Object },
    picker: Boolean
  })

  const mv = computed(() => props.modelValue || {})

  const clearAll = () => {
    emit('update:fee', 0);
    emit('update:feeType', undefined);
  }
  const types = {
    'alg': { label: 'Dynamic', description: 'Percentage, contingencies, or other algorithm based fees', display: () => 'Dynamic'},
    'pepm': { label: 'PEPM', description: 'Per Employee Per Month', display: (val) => `${dollarString(val, '$', 2) } PEPM`},
    'pmpm': { label: 'PMPM', description: 'Per Member (employee + dependents) Per Month', display: (val) => `${dollarString(val, '$', 2) } PMPM`},
    'flat': { label: 'Flat', description: 'Flat Dollar Amount', display: (val) => `${dollarString(val, '$', 0)}`}
  }
</script>

<style lang="scss" scoped>

</style>
