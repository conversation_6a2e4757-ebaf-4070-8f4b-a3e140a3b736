<template>
  <div class="_fw">

    <q-input v-model="search.text" dense filled class="mw600">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>
    <div class="q-py-sm row items-center">
      <q-icon name="mdi-filter" color="accent" size="20px" class="q-mr-sm"></q-icon>
      <team-member-status multiple clickable picker v-model="status"></team-member-status>
    </div>
    <data-table
        v-bind="{
      store: offerStore,
      hideAddBtn: true,
      columns,
      params,
      openItem,
      searchOff: true
        }"
    >
      <template v-slot:menu="scope">
        <div class="w250 mw100 q-pa-sm bg-white">
          <q-list separator>
            <q-item clickable @click="openItem(scope.row)">
              <q-item-section>
                <q-item-label>Open</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-icon name="mdi-eye" color="accent"></q-icon>
              </q-item-section>
            </q-item>
            <remove-item label="Deny" confirm-label="Confirm Denial"
                         @remove="statusUpdate(scope.row, 'rejected')"></remove-item>
          </q-list>
        </div>
      </template>
    </data-table>

  </div>
</template>

<script setup>
  import DataTable from 'components/common/tables/DataTable.vue';
  import DefaultChip from 'components/common/avatars/DefaultChip.vue';
  import RoleChip from 'components/plans/team/cards/RoleChip.vue';
  import FeeChip from 'components/plans/team/cards/FeeChip.vue';
  import RemoveItem from 'components/common/buttons/RemoveItem.vue';
  import TeamMemberStatus from 'components/plans/team/cards/TeamMemberStatus.vue';

  import {computed, ref} from 'vue';
  import {useOffers} from 'stores/offers';
  import {useHosts} from 'stores/hosts';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import {useRouter} from 'vue-router';

  const router = useRouter();

  const offerStore = useOffers();
  const hostStore = useHosts();

  const props = defineProps({
    plan: Object
  })

  const { search, searchQ } = HQuery({ keys: ['dba'] })

  const { h$ } = HFind({
    store: hostStore,
    pause: computed(() => !search.value.text?.length),
    params: computed(() => {
      return {
        query: {
          ...searchQ.value
        }
      }
    })
  })

  const hostIds = computed(() => search.value.text?.length ? h$.data.map(a => a._id) : undefined);

  const status = ref([]);
  const params = computed(() => {
    const query = { plan: props.plan._id  }
    if (status.value?.length) query.status = { $in: status.value }
    if (hostIds.value) query.host = { $in: hostIds.value }
    return {
      runJoin: { offer_host: true },
      query
    }
  })


  const openItem = (val) => {
    const { href } = router.resolve({ name: 'plan-offer', params: { offerId: val._id }})
    window.open(href, '_blank')
  }

  const statusUpdate = (val, st) => {
    offerStore.patch(val._id, { status: st }, { special_change: ['status'] })
  }

  const columns = computed(() => {
    return [
      {
        label: 'Company',
        component: DefaultChip,
        attrs: (row) => {
          return {
            namePath: 'dba',
            modelValue: row._fastjoin.host,
          }
        }
      },
      {
        label: 'Role',
        component: RoleChip,
        attrs: (row) => {
          return {
            modelValue: row.role
          }
        }
      },
      {
        label: 'Fee',
        component: FeeChip,
        attrs: (row) => {
          return {
            modelValue: row
          }
        }
      }
    ]
  })

</script>

<style lang="scss" scoped>

</style>
