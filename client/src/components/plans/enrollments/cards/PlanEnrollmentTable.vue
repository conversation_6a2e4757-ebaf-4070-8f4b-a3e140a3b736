<template>
  <div class="_fw">
    <q-tab-panels class="_panel" :model-value="!selected" animated>
      <q-tab-panel class="_panel" :name="true">
        <div class="_fw q-px-sm">
          <plan-people-picker
              multiple
              v-model="pplFilter"
              :plan="fullPlan"
              :select-attrs="{
            borderless: true,
            class: '_finp',
          placeholder: 'Filter By Participant...'
        }"
          ></plan-people-picker>
        </div>

        <slot name="filter"></slot>
        <q-slide-transition>
          <div v-if="multi.length" class="q-py-sm row items-center">
            <q-btn flat no-caps @click="remindMulti">
              <q-icon name="mdi-reminder" color="primary" class="q-mr-sm"></q-icon>
              <span>Send Reminders</span>
            </q-btn>
            <remove-proxy no-caps flat :label="`Remove ${$possiblyPlural('Enrollment', multi)}`" :remove-label="`Confirm you want to remove enrollment access for ${$possiblyPlural('', multi, 'person', 'people')}? This could have ERISA implications.`" @remove="removeMulti"></remove-proxy>
          </div>
        </q-slide-transition>
        <q-table
            :columns="eCols"
            :rows="e$.data"
            hide-pagination
            :rows-per-page-options="[0]"
            hide-no-data
            hide-bottom

        >
          <template v-slot:header="scope">
            <q-th auto-width>
              <q-checkbox size="sm" :model-value="multi.length >= e$.data.length" @update:model-value="val => val ? multi = e$.data.map(a => a._id) : multi = []"></q-checkbox>
            </q-th>
            <q-th
                v-for="col in scope.cols"
                :key="col.name"
                :props="scope"
            >
              {{ col.label }}
            </q-th>
            <q-th auto-width></q-th>

          </template>
          <template v-slot:body="scope">
            <q-tr :props="scope" @dblclick="selected = scope.row">
             <q-td auto-width>
               <q-checkbox size="sm" :model-value="multi.includes(scope.row._id)" @update:model-value="toggleMulti(scope.row._id, $event)"></q-checkbox>
             </q-td>
              <q-td v-for="(col, i) in scope.cols" :key="`td-${i}`">
                <component :is="col.component" v-bind="col.attrs(scope.row, col)" v-on="col.listeners ? col.listeners(scope.row) : {}"></component>
              </q-td>
              <q-td auto-width>
                <q-btn dense flat size="sm" icon="mdi-dots-vertical">
                  <q-menu>
                    <div class="w300 mw100 bg-white q-pa-sm">
                      <q-list separator>
                        <q-item clickable @click="remindMulti([scope.row._id])">
                          <q-item-section avatar>
                            <q-icon color="primary" name="mdi-reminder"></q-icon>
                          </q-item-section>
                          <q-item-section>
                            <q-item-label>Send Reminder</q-item-label>
                          </q-item-section>
                        </q-item>
                        <q-item clickable @click="selected = scope.row">
                          <q-item-section avatar>
                            <q-icon color="accent" name="mdi-eye"></q-icon>
                          </q-item-section>
                          <q-item-section>
                            <q-item-label>View Details</q-item-label>
                          </q-item-section>
                        </q-item>
                        <q-item>
                          <q-item-section avatar>
                            <remove-proxy dense flat :label="undefined" remove-label="Remove enrollment? This may have compliance implications." @remove="remove(scope.row._id)"></remove-proxy>
                          </q-item-section>
                          <q-item-section>
                            <q-item-label>Remove</q-item-label>
                          </q-item-section>
                        </q-item>
                      </q-list>
                    </div>
                  </q-menu>
                </q-btn>
              </q-td>
            </q-tr>
          </template>
        </q-table>
        <div v-if="!e$.total" class="q-pa-lg font-1r text-italic">No enrollments for this period</div>
        <pagination-row v-bind="{ pagination: ePagination, pageRecordCount: ePr, h$:e$, limit: eLimit}"></pagination-row>


      </q-tab-panel>
      <q-tab-panel class="_panel" :name="false">
        <div class="q-py-sm _fw">
          <q-btn dense flat icon="mdi-close" color="red" @click="selected = undefined"></q-btn>
        </div>
        <enrollment-viewer :model-value="selected"></enrollment-viewer>
      </q-tab-panel>
    </q-tab-panels>
  </div>

</template>

<script setup>
  import PlanPeoplePicker from 'components/plans/lists/PlanPeoplePicker.vue';
  import EnrollmentViewer from 'components/enrollments/cards/EnrollmentViewer.vue';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';
  import TdText from 'components/common/tables/TdText.vue';
  import StatusChip from 'components/enrollments/cards/StatusChip.vue';

  import {computed, ref} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {$capitalizeFirstLetter, $errNotify, $possiblyPlural} from 'src/utils/global-methods';
  import {formatDate} from 'src/utils/date-utils';

  import {useEnrollments} from 'stores/enrollments';
  import {_flatten} from 'symbol-syntax-utils';
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';


  const eStore = useEnrollments();

  const props = defineProps({
    plan: { required: true },
    planKey: { type: String },
    query: Object
  })

  const selected = ref()
  const multi = ref([]);
  const toggleMulti = (id, val) => {
    if(val) multi.value.push(id);
    else {
      const idx = multi.value.indexOf(id);
      if(idx > -1) multi.value.splice(idx, 1);
    }
  }

  const fullPlan = computed(() => props.plan)

  const pplFilter = ref([]);

  const eParams = computed(() => {
    const q = { plan: fullPlan.value?._id, ...props.query };
    if (props.planKey) q.version = props.planKey;
    if (pplFilter.value?.length) q.person = { $in: _flatten(pplFilter.value.map(a => a._id)) }
    return {
      query: q,
      runJoin: {
        enrollment_person: true
      }
    }
  })

  const eLimit = ref(10);
  const { h$: e$, pagination: ePagination, pageRecordCount: ePr } = HFind({
    store: eStore,
    params: eParams,
    limit: eLimit
  })

  const remindMulti = async () => {
    for(const eId of multi.value){
      const e = eStore.getFromStore(eId).value;
      if(e.status !== 'complete'){
        await eStore.patch(eId, { $set: { updatedAt: new Date() } }, { runJoin: { enroll_reminder: true } })
            .catch(err => $errNotify(`Error sending reminder: ${err.message}`))
      }
    }
    multi.value = [];
  }

  const remove = async (id) => {
    const val = eStore.getFromStore(id).value;
    if(val.status && val.status !== 'complete') {
      await eStore.remove(id)
          .catch(err => $errNotify(`Error removing enrollment: ${err.message}`))
    } else $errNotify('Cannot remove an enrollment that has been completed')
  }

  const removeMulti = async () => {
    const eligible = multi.value.filter(a => {
      const e = eStore.getFromStore(a).value;
      return e.status && e.status !== 'complete'
    });
    await eStore.remove(null, { query: { _id: { $in: eligible } } })
        .catch(err => $errNotify(`Error removing enrollment: ${err.message}`))
    multi.value = [];
  }

  const eCols = computed(() => {
    return [
      {
        label: 'Person',
        name: 'person',
        field: '_fastjoin',
        component: TdText,
        attrs: (row) => {
          return { col: { value: row._fastjoin?.person?.name ||  row.name || 'No Name Listed' } }
        }
      },
      {
        label: 'Email',
        name: 'email',
        field: '_fastjoin',
        component: TdText,
        attrs: (row) => {
          return { col: { value: row._fastjoin?.person?.email ||  row.email || 'No Email' } }
        }
      },
      {
        label: 'Status',
        name: 'status',
        field: 'status',
        component: StatusChip,
        attrs: (row) => {
          return {
            modelValue: row.status,
          }
        }
      },
      {
        label: 'Event',
        name: 'year',
        field: 'version',
        component: TdText,
        attrs: (row) => {
          return { col: { value: row.version?.split('_').join('-') } }
        }
      },
      {
        label: 'Invited',
        name: 'invited',
        field: 'createdAt',
        component: TdText,
        attrs: (row) => {
          return { col: { value: formatDate(row.createdAt, 'MM/DD/YY') } }
        }
      },
      {
        label: 'Enrolled On',
        name: 'enrolled',
        field: 'enrolledAt',
        component: TdText,
        attrs: (row) => {
          return { col: { value: formatDate(row.enrolledAt, 'MM/DD/YY') } }
        }
      },
      {
        label: 'Type',
        name: 'type',
        field: 'type',
        component: TdText,
        attrs: (row) => {
          return { col: { value: $capitalizeFirstLetter(row.type) } }
        },
      },
      {
        label: 'Enrolled',
        name: 'count',
        field: 'enrolled',
        component: TdText,
        attrs: (row) => {
          return { col: { value: Object.keys(row.enrolled || {}).length } }
        }
      }
    ].map(a => {
      return {
        sortable: true,
        align: 'left',
        ...a
      };
    })
  })

</script>

<style lang="scss" scoped>

</style>
