<template>
  <div class="_fw">
    <spec-list
        adding
        searchable
        show-person
        :plan="plan"
        :plan-year="planYear"
    ></spec-list>

  </div>
</template>

<script setup>

  import SpecList from 'components/enrollments/specs/lists/SpecList.vue';

  const props = defineProps({
    plan: { required: true },
    planYear: { required: true }
  })

</script>

<style lang="scss" scoped>

</style>
