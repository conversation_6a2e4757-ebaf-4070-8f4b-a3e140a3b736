
export const enrollmentsByYear = (plan:any, openOnly?:boolean) => {
    const obj:any = {};
    for (const k in plan?.enrollments || {}) {
        if(!openOnly || plan.enrollments[k].open_enroll) {
            const spl = k.split('_');
            if (!spl[1]) spl.push('0');
            obj[spl[0]] = {...obj[spl[0]], [spl[1]]: plan.enrollments[k]};
        }
    }
    const returnObj:any = {};
    Object.keys(obj).sort((a, b) => Number(b) - Number(a)).forEach(k => returnObj[k] = obj[k]);
    return returnObj;
}

export const statusObj = {
    'not_started': {
        color: 'purple',
        label: 'Not Started'
    },
    'open': {
        color: 'accent',
        label: 'Open'
    },
    'review': {
        color: 'orange',
        label: 'Under Review'
    },
    'complete': {
        color: 'green',
        label: 'Complete'
    },
    'closed': {
        color: 'black',
        label: 'Closed'
    }
}
export const statuses = Object.keys(statusObj);
