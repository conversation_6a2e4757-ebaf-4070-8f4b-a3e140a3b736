<template>
  <div class="_fw">
    <div v-if="!docReq.status" class="q-pa-sm font-1r tw-six">Use your census data to automatically add employees/participants</div>
    <div v-else-if="docReq.status === 'partial'" class="q-pa-sm font-1r tw-six text-red-10">See errors adding people/compensation</div>

    <remove-proxy :dense="false" push class="bg-primary" :icon="undefined" no-caps remove-label="Discard Census?" @remove="complete">
      <span class="q-mr-sm tw-six text-white">I'm finished with this data</span>
      <q-icon name="mdi-check-circle" color="white"></q-icon>
    </remove-proxy>

    <div class="q-py-sm"></div>

    <census-table
        service-path="doc-requests"
        :store="drStore"
        full
        :id="docReq._id"
        :errs-in="Object.keys(errs).length ? errs : undefined"
        :update="addCams"
        :save-btn-label="Object.keys(errs).length ? 'Correct Files' : 'Add People'"
        :save-btn="!loading"
    ></census-table>


  </div>
</template>

<script setup>
  import CensusTable from 'pages/landing/sm-er/results/CensusTable.vue';
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';

  import {idGet} from 'src/utils/id-get';
  import {computed, ref} from 'vue';
  import {useDocRequests} from 'stores/doc-requests';
  import {useJunkDrawers} from 'stores/junk-drawers';
  import {getStateName} from 'components/common/geo/data/states';
  import {usePpls} from 'stores/ppls';
  import {useCams} from 'stores/cams';
  import {useGroups} from 'stores/groups';

  const drStore = useDocRequests();
  const junkStore = useJunkDrawers();
  const pplStore = usePpls();
  const camsStore = useCams();
  const groupStore = useGroups();

  const props = defineProps({
    req: { required: true }
  })

  const { item: docReq } = idGet({
    store: drStore,
    value: computed(() => props.req)
  })

  const addErrs = ref({})
  const camsErrs = ref({})
  const loading = ref(false)
  const errs = ref({})

  const saveTo = ref();
  const maybeSave = () => {
    if(saveTo.value) clearTimeout(saveTo.value);
    saveTo.value = setTimeout(() => {
      if(!loading.value) {
        drStore.patch(docReq.value._id, { employees: docReq.value.employees })
      }
    }, 3000)
  }

  const addCams = async (ees, data, auto) => {
    drStore.patchInStore(docReq.value._id, { employees: ees })
    maybeSave();
    if (auto) return

    loading.value = true;

    const group = await groupStore.create({ planClass: true, name: 'All Employees', org: docReq.value.org })

    const employees = [...ees || []]
    let patch;
    const zips = Array.from(new Set(ees.map(a => a.zip ? `zips|${a.zip.substring(0, 3)}` : false).filter(a => !!a)));
    const query = {itemId: { $in: zips }, $limit: zips.length }
    const jds = await junkStore.findInStore({ query });
    query.itemId.$in = []
    for(const z of zips){
      if(!jds.data.filter(a => a.itemId === z)[0]) query.itemId.$in.push(z);
    }
    const fetched = await junkStore.find({ query })
        .catch(err => {
          console.error(`Error formatting addresses: ${err.message}`)
          return { data: [] }
        })
    for(const d of fetched.data){
      jds.data.push(d);
    }
    const drawers = {};
    for(let i = 0; i < jds.data.length; i++){
      drawers[jds.data[i].itemName] = jds.data[i];
    }
    const getAddress = (zip) => {
      const drawer = drawers[zip.substring(0, 3)]
      if (!drawer?.data[zip]) return undefined;
      return {
        postal: zip,
        region: getStateName(drawer.data[zip].state),
        country: 'US',
        city: drawer.data[zip].city,
      }
    }


    const eeToCam = async (ee, i) => {
      delete errs.value[i]
      delete addErrs.value[ee.uid];
      delete camsErrs.value[ee.uid];
      if(ee.person) return;
      const dob = ee.dob || new Date(new Date().getTime() - (ee.age * 1000 * 60 * 60 * 24 * 365));
      const person = {
        email: ee.email,
        firstName: ee.firstName,
        lastName: ee.lastName,
        dob,
        address: getAddress(ee.zip)
      }
      const deps = [];
      for (let i = 0; i < ee.deps || 0; i++) {
        deps.push({
          lastName: ee.lastName,
          firstName: `Child-${i + 1}`,
          dob: new Date(new Date().getTime() - (1000 * 60 * 60 * 24 * 365 * 10))
        })
      }
      const ppl = await pplStore.create(person, {
        runJoin: {
          groupId: group._id,
          add_household: {
            spouse: {
              dob,
              lastName: ee.lastName,
              firstName: 'Spouse'
            }, deps
          }
        }
      })
          .catch(err => {
            addErrs.value[ee.uid] = err.message
          })
      if (ppl._id) {
        employees[i].person = ppl._id;
        patch = true;
        const obj = {
          org: docReq.value.org,
          person: ppl._id,
          interval: ee.hourly ? 'hour' : 'year',
          estHours: ee.hours,
          amount: ee.wage,
          name: ee.role || 'Untitled',
          class: ee.w9 ? 'ic' : 'ee'
        }
        await camsStore.create(obj)
            .catch(err => {
              console.error(`Error adding cams: ${err.message}`)
              camsErrs.value[ee.uid] = err.message
            })
      }
    }

    loading.value = true;
    await Promise.all(ees.map((a, i) => eeToCam(a, i)))
    loading.value = false;

    for(let i = 0; i < employees.length; i++) {
      if(addErrs.value[employees[i].uid]){
        errs.value[i] = addErrs.value[employees[i].uid]
        patch = true;
        employees[i].errorAdding = true;
        employees[i].addError = addErrs.value[employees[i].uid]
      }
      if(camsErrs.value[employees[i].uid]){
        errs.value[i] = (errs.value[i] ? errs.value[i] + ' | ' : '') + addErrs.value[employees[i].uid]
        patch = true;
        employees[i].errorAdding = true;
        employees[i].camsError = camsErrs.value[employees[i].uid]
      }
    }
    if(saveTo.value) clearTimeout(saveTo.value)
    if(patch){
      drStore.patchInStore(docReq.value._id, { employees, status: 'partial' })
      drStore.patch(docReq.value._id, { employees, status: 'partial' })
    } else {
      drStore.patchInStore(docReq.value._id, { status: 'complete' })
      drStore.patch(docReq.value._id, { status: 'complete' })
    }
  }

  const complete = () => {
    drStore.patch(docReq.value._id, { status: 'complete' })
  }

</script>

<style lang="scss" scoped>

</style>
