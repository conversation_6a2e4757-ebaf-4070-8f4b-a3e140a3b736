<template>
  <div class="_fw">
    <plan-year-picker
        prefix="Plan Year: "
        :model-value="planYear"
        @update:model-value="planYearSelect = $event"
    ></plan-year-picker>
    <div class="row q-col-gutter-sm pw1">

      <div class="col-12 _fh q-py-sm">
        <div class="__in">
          <div class="_f_l _f_chip">Funds Summary</div>
          <div class="__c">
            <plan-funds-table
                :care-account="ca"
                :plan-year="planYear"
                :contributions="contributions"
                :plan="fullPlan"
                :coverages="c$.data"
            ></plan-funds-table>
          </div>
        </div>
      </div>

      <template v-if="Object.keys(eligibleKeys.cafe).length">
        <div class="col-12 col-md-6 _fh q-py-sm">
          <div class="__in">
            <div class="_f_l _f_chip">Cafeteria Funds</div>
            <div class="__c">
              <div class="__cap alt-font tw-five">Funded by payroll elections - these funds will be made available for
                employees to use via virtual card. Choose a main budget - and sub budgets/cards will be issued for each
                enrollee to match their election.
              </div>
              <div class="__tbl q-py-md">
                <table>
                  <tbody>
                  <tr class="tw-six text-grey-7 alt-font font-3-4r">
                    <td>PLAN<br><span class="tw-four font-3-4r">(Funds Needed)</span></td>
                    <td>ACCOUNT</td>
                    <td>BUDGET</td>
                  </tr>
                  <tr v-for="(k, i) in Object.keys(eligibleKeys.cafe)" :key="`cafe-${i}`">
                    <td class="tw-six">
                      {{ cafeKeys[k].shortName }}
                      <span class="tw-four alt-font font-3-4r">
                        <br>({{ dollarString((contributions?.byPlan || {})[k] || 0, '$', 2) }})
                      </span>
                      <q-tooltip>{{ cafeKeys[k].name }}</q-tooltip>
                    </td>
                    <td>
                      <care-account-picker
                          short
                          emit-value
                          :org="fullPlan?.org"
                          v-model="accounts.cafe[k]"
                      ></care-account-picker>
                    </td>
                    <td>
                      <budget-chip
                          :care-account="accounts.cafe[k]"
                          :disable="!accounts.cafe[k]"
                          :model-value="eligibleKeys.cafe[k].budget"
                          @update:model-value="addBudget($event, `cafe.${k}.budget`)"
                          picker
                      >
                        <template v-slot:top="scope">
                          <div class="row">
                            <remove-proxy name="Budget" dense flat icon="mdi-chevron-left" color="accent" @remove="accounts.cafe[k] = undefined">
                              <template v-slot:default>
                                <span class="q-mr-xs">{{ scope.budget?.name }}</span>
                                <q-icon name="mdi-close" color="red"></q-icon>
                              </template>
                            </remove-proxy>
                          </div>
                        </template>
                      </budget-chip>

                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

        </div>
      </template>

      <div class="col-12 col-md-6 _fh q-py-sm">
        <div class="__in">
          <div class="_f_l _f_chip">Contributions By Group</div>

          <div class="__c">
            <employer-contributions :plan="fullPlan"></employer-contributions>
          </div>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup>
  import EmployerContributions from 'components/plans/forms/EmployerContributions.vue';
  import CareAccountPicker from 'components/care-accounts/lists/CareAccountPicker.vue';
  import PlanFundsTable from 'components/plans/cards/PlanFundsTable.vue';
  import BudgetChip from 'components/accounts/issuing/components/budgets/cards/BudgetChip.vue';
  import PlanYearPicker from 'components/plans/utils/PlanYearPicker.vue';
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';

  import {computed, ref, watch} from 'vue';
  import {hraKeys, cafeKeys, getCurrentPlanYear} from 'components/plans/utils';
  import {useBudgets} from 'stores/budgets';
  import {usePlans} from 'stores/plans';
  import {$errNotify, dollarString} from 'src/utils/global-methods';
  import {idGet} from 'src/utils/id-get';
  import {getLatestOpen} from '../utils/enrollments';
  import {HFind} from 'src/utils/hFind';
  import {useCoverages} from 'stores/coverages';
  import {LocalStorage} from 'symbol-auth-client';
  import {useOrgs} from 'stores/orgs';
  import {useCareAccounts} from 'stores/care-accounts';

  const budgetStore = useBudgets();
  const planStore = usePlans();
  const cStore = useCoverages();
  const orgStore = useOrgs();
  const caStore = useCareAccounts();

  const props = defineProps({
    plan: { required: false }
  })

  const { item: fullPlan } = idGet({
    store: planStore,
    value: computed(() => props.plan || LocalStorage.getItem('plan_id')),
  })

  const { item:org } = idGet({
    store: orgStore,
    value: computed(() => fullPlan.value.org)
  })

  const { h$:ca$ } = HFind({
    store: caStore,
    params: computed(() => {
      return {
        query: { owner: org.value._id }
      }
    })
  })
  const ca = computed(() => ca$.data[0])


  const planYearSelect = ref('');
  const planYear = computed(() => planYearSelect.value || getCurrentPlanYear(fullPlan.value))


  watch(planYear, async (nv, ov) => {
    if (nv && nv !== ov) {
      const open = getLatestOpen(fullPlan.value);
      const er = (fullPlan.value?.enrollments || {})[open] || {};
      const past = er.lastEnrolled ? er.lastEnrolled : new Date().getTime() - 1000 * 60 * 60;
      if (er.open_enroll && !er.lastUpdate || new Date(er.lastUpdate).getTime() < new Date(past).getTime()) {
        await planStore.patch(fullPlan.value._id, { $set: { [`enrollments.${open}.lastUpdate`]: new Date() } })
            .catch(err => {
              console.error(`Error updating funds totals: ${err.message}`)
              // $errNotify('There was an error updating your plan fund totals - you may need to refresh the page')
            })
      }
    }
  }, { immediate: true })
  const accounts = ref({ cafe: {}, hra: {} })
  const eligibleKeys = computed(() => {
    const { cafe = {}, hra = {} } = fullPlan.value || {}
    const hraObj = {};
    for (const k in hraKeys) {
      const { active, budget, recurs } = hra[k] || {}
      if (active) hraObj[k] = { active, budget, recurs }
    }
    const cafeObj = {};
    for (const k of ['dcp', 'fsa']) {
      if (cafe[k]?.active) cafeObj[k] = { active: true, budget: cafe[k]?.budget }
    }
    return {
      cafe: cafeObj,
      hra: hraObj
    }
  })

  const budgetIds = ref([])
  const budgets = ref({ data: [], total: 0 })

  const addBudget = (id, path) => {
    const patchObj = { $set: { [`${path}`]: id } }
    planStore.patchInStore(fullPlan.value._id, patchObj)
    planStore.patch(fullPlan.value._id, patchObj)
        .catch(err => {
          console.error(`Error adding budget: ${err.message}`)
          $errNotify(err.message)
        })
  }

  const loadBudgets = async (tries = 0) => {
    budgets.value = await budgetStore.find({
      query: { _id: { $in: budgetIds.value } },
      runJoin: { budget_parent: true }
    })
        .catch(err => {
          console.error('Error fetching budget', err.message);
          if (tries < 1) loadBudgets(tries + 1)
          return { total: 0, data: [] }
        })
    budgets.value.data.forEach(item => {
      const ca = item.careAccount || item._fastjoin?.parent?.careAccount;
      if (ca) {
        for (const k in accounts.value) {
          for (const subK in accounts.value[k]) {
            if (eligibleKeys.value[k][subK].budget === item._id) {
              accounts.value[k] = { ...accounts.value[k], [subK]: ca }
            }
          }
        }
      }
    })
  }

  const contributions = computed(() => {
    const { enrollments = {} } = fullPlan.value || {};
    let contributions = { key: 0, employer: {}, employee: {}, needed: {}, byCoverage: {}, byPlan: {} }
    const latest = getLatestOpen(fullPlan.value);
    if (latest) contributions = { ...contributions, key: latest, ...enrollments[latest].contributions }
    return contributions
  })
  const coverageIds = computed(() => Object.keys(fullPlan.value?.coverages || {}))

  const { h$: c$ } = HFind({
    store: cStore,
    limit: computed(() => coverageIds.value.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: coverageIds.value }
        }
      }
    })
  })

  const ichraTotal = computed(() => {
    const byId = contributions.value?.byCoverage || {}
    const ichras = c$.data.filter(a => a.ichra);
    let total = 0;
    for (let i = 0; i < ichras.length; ++i) {
      const amt = byId[ichras[i]._id]?.needed
      if (amt) total += amt;
    }
    return total;
  })

  watch(fullPlan, (nv, ov) => {
    if (nv && nv._id !== ov?._id) {
      setTimeout(() => {
        budgetIds.value = [];
        for (const k in eligibleKeys.value) {
          for (const subK in eligibleKeys.value[k]) {
            const b = eligibleKeys.value[k][subK].budget
            if (b) {
              budgetIds.value.push(b);
              accounts.value[k] = { ...accounts.value[k], [subK]: {} }
            }
          }
        }
        loadBudgets()
      }, 50)
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  .__in {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 100%;
    grid-template-rows: auto 1fr;
  }

  .__c {
    border-radius: 0 0 10px 10px;
    background: white;
    box-shadow: 0 2px 8px -2px #999;
    padding: 2vh 1vw;
    height: 100%;
  }

  .__tbl {
    width: 100%;
    overflow-x: scroll;

    table {
      border-collapse: collapse;
      width: 100%;

      td {
        border-bottom: solid .2px #999;
        padding: 5px 10px;
      }
    }
  }

  .__cap {
    padding: 10px;
    border-radius: 6px;
    background: #f6f6f6;
    font-size: .85rem;
  }
</style>
