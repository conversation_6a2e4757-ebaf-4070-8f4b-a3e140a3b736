<template>
  <div class="_fw">
    <div class="_form_grid">

      <!--        CONTRIBUTION TYPE-->
      <div class="_form_label">Type</div>
      <div class="q-pa-sm">
        <q-radio @update:model-value="emitUp" v-model="form.type" val="percent"
                 label="Percent (of pay)"></q-radio>
        <q-radio @update:model-value="emitUp" v-model="form.type" val="flat"
                 label="Flat"></q-radio>
      </div>

      <template v-if="form.type === 'percent'">
        <div class="_form_label">Percent Of</div>
        <div class="q-pa-sm">
          <q-radio v-model="form.percentType" val="cost" label="Plan Cost"></q-radio>
          <q-radio v-model="form.percentType" val="income" label="Income"></q-radio>
        </div>
      </template>

      <!--        MIN-->
      <div class="_form_label">Single</div>
      <div class="q-pa-sm">
        <money-input
            :prefix="form.type === 'flat' ? '$' : ''"
            :suffix="form.type === 'flat' ? '' : '%'"
            :decimal="2"
            v-model="form.amount"
            @update:model-value="setSingle"></money-input>
      </div>
      <div class="_form_label">Family</div>
      <div class="q-pa-sm">
        <money-input
            :prefix="form.type === 'flat' ? '$' : ''"
            :suffix="form.type === 'flat' ? '' : '%'"
            :decimal="2"
            v-model="form.family"
            @update:model-value="setFamily"></money-input>
      </div>

      <div class="_form_label">Match</div>
      <div class="q-pa-sm">
        <q-checkbox :model-value="!!form.match" @update:model-value="setForm('match', $event)"
                    label="Only contribute as match"></q-checkbox>
      </div>

      <div class="_form_label">Post Tax Adjustment</div>
      <div class="q-pa-sm">
        <div class="font-3-4r">Since post-tax elections incur a higher payroll tax cost (+7.65%) you may want to adjust your contribution to account for this in the case post-tax elections are made. In effect, this penalizes post-tax elections but keeps your costs predictable.</div>
        <money-input
            prefix=""
            suffix="%"
            :decimal="2"
            v-model="form.postTax"
            @update:model-value="setForm('postTax', $event)"></money-input>
      </div>

      <template v-if="form.type === 'percent'">
        <div class="_form_label">Pay Base</div>
        <div class="q-pa-sm">
          <q-checkbox :model-value="!!form.includeExtras" @update:model-value="setForm('includeExtras', $event)"
                      label="Include bonus & other wages in calculation"></q-checkbox>
        </div>
      </template>

    </div>
  </div>
</template>

<script setup>
  import MoneyInput from 'src/components/common/input/MoneyInput.vue';

  import {nextTick, ref, watch} from 'vue';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: Object
  })

  const formFn = (defs) => {
    return {
      type: 'flat',
      ...defs
    }
  }

  const form = ref(formFn());

  const setForm = (path, val) => {
    form.value[path] = val;
    emit('update:model-value', form.value);
  }

  const emitUp = () => {
    nextTick(() => emit('update:model-value', form.value));
  }

  const setFamily = (v) => {
    if(!form.value.single) {
      form.value.single = v;
    }
    if(form.value.type === 'percent'){
      form.value.family = Math.min(form.value.family || 0, 1)
      form.value.single = Math.min(form.value.single || 0, 1)
    }
    emitUp()
  }
  const setSingle = (v) => {
    if(form.value.family < v) form.value.family = v;
    if(form.value.type === 'percent'){
      form.value.family = Math.min(form.value.family || 0, 1)
      form.value.single = Math.min(form.value.single || 0, 1)
    }
    emitUp()
  }

  watch(() => props.modelValue, (nv, ov) => {
    if (nv && JSON.stringify(nv) !== JSON.stringify(ov)) {
      form.value = formFn(nv);
    }
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
