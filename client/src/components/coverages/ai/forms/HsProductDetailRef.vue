<template>
  <q-list separator>
    <q-input dense filled v-model="search.text">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>

    <template v-for="(hs, i) in h$.data" :key="`hs-${i}`">
      <q-item v-if="Object.keys(hs.products || {}).length === 1" :clickable="modelValue?.split(':')[0] !== hs._id" @click="selectHs(hs)">
        <q-item-section>
          <q-item-label>{{ hs.name }}</q-item-label>
        </q-item-section>
        <q-item-section side>
          <remove-proxy icon="mdi-close" v-if="modelValue?.split(':')[0] === hs._id" dense flat name="Ai Access" @remove="removeHs()"></remove-proxy>
          <q-icon v-else name="mdi-plus" color="primary"></q-icon>
        </q-item-section>
      </q-item>
      <q-expansion-item v-else :label="hs.name" group="hs">
        <q-list>
          <q-item-label header>Select Product</q-item-label>
          <q-item v-for="(k, idx) in Object.keys(hs.products || {})" :key="`k-${i}-${idx}`" :clickable="modelValue !== `${hs._id}:${k}`"
                  @click="selectHs(hs, k)">
            <q-item-section>
              <q-item-label>{{ hs.products[k].name || k }}</q-item-label>
            </q-item-section>
            <q-item-section side>
              <remove-proxy icon="mdi-close" v-if="modelValue === `${hs._id}:${k}`" dense flat name="Ai Access" @remove="removeHs()"></remove-proxy>
              <q-icon v-else name="mdi-plus" color="primary"></q-icon>
            </q-item-section>
          </q-item>
        </q-list>
      </q-expansion-item>
    </template>

  </q-list>

</template>

<script setup>
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';

  import {computed, ref, watch} from 'vue';
  import {HFind} from 'src/utils/hFind';
  import {useHealthShares} from 'stores/health-shares';
  import {HQuery} from 'src/utils/hQuery';

  const hsStore = useHealthShares();

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    coverage: { required: true },
    modelValue: { required: false }
  })

  const coverageName = computed(() => props.coverage?.name?.split(' ')[0]);

  const { search, searchQ } = HQuery({ keys: ['name', 'aka'], search: coverageName })

  const { h$ } = HFind({
    store: hsStore,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value
        }
      }
    })
  })

  const selectHs = (hs, k) => {
    let key = k || Object.keys(hs.products)[0];
    emit('update:model-value', `${hs._id}:${key}`);
  }

  const removeHs = () => {
    emit('remove');
  }

</script>

<style lang="scss" scoped>

</style>
