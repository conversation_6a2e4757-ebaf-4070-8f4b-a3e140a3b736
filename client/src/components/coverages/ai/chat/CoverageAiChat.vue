<template>
  <ai-chat
      openFocus
      :run-chat="runChat"
      chat-name="coverage_chat"
      :subject="coverage?._id"
      subject-label="plan"
      :prompts="prompts"
  ></ai-chat>
</template>

<script setup>
import AiChat from 'components/ai/chat/AiChat.vue';
import {ref} from 'vue';
import {useCoverages} from 'stores/coverages';
import {useMarketplace} from 'stores/marketplace';

const coverageStore = useCoverages();
const marketStore = useMarketplace();

const props = defineProps({
  household: { required: false },
  coverage: { required: true },
  prompts: {
    default: () => {
      return ['How does this plan determine when to pay a claim?', 'Does this plan require prior authorization for any services?', 'What are reasons a claim would be denied?']
    }
  }
})

const message = ref();
const error = ref(false);
const thinking = ref(false);
const runChat = async (text, history, session) => {
  error.value = false;
  thinking.value = true;

  const ai_chat = {
    text,
    chat_history: [],
    household: props.household,
    plan: props.coverage
  };
  if (session && history) {
    for (const ch of history) {
      ai_chat.chat_history.push({ role: 'user', content: ch.question })
      ai_chat.chat_history.push({ role: 'assistant', content: ch.answer })
    }
    if (!ai_chat.chat_history.length) delete ai_chat.chat_history;
  }
  const store = props.coverage.acaPlan ? marketStore : coverageStore;
  message.value = 'Searching Plan Information...'
  const res = await store.get(props.coverage._id, { runJoin: { ai_chat } })
      .catch(err => {
        console.error(`Error running coverage chat: ${err.message}`)
        error.value = true;
        message.value = `Error searching plan docs: ${err.message}`;
        thinking.value = false;
        return;
      })
  message.value = '';
  error.value = false;
  thinking.value = false;
  return res._fastjoin.ai_response;
}
</script>

<style lang="scss" scoped>

</style>
