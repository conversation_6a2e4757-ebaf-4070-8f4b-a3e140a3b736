import {dollarString} from 'src/utils/global-methods';
import ImageCompare from 'components/coverages/utils/sync/compare/ImageCompare.vue';
import TypeCompare from 'components/coverages/utils/sync/compare/TypeCompare.vue';
import GeoCompare from 'components/coverages/utils/sync/compare/GeoCompare.vue';
import VideoCompare from 'components/coverages/utils/sync/compare/VideoCompare.vue';
import ProviderCompare from 'components/coverages/utils/sync/compare/ProviderCompare.vue';
import NetworkCompare from 'components/coverages/utils/sync/compare/NetworkCompare.vue';
import DocumentsCompare from 'components/coverages/utils/sync/compare/DocumentsCompare.vue';
import DeductibleCompare from 'components/coverages/utils/sync/compare/DeductibleCompare.vue';
import DeductiblesCompare from 'components/coverages/utils/sync/compare/DeductiblesCompare.vue';
import PremiumsCompare from 'components/coverages/utils/sync/compare/PremiumsCompare.vue';
import RatesCompare from 'components/coverages/utils/sync/compare/RatesCompare.vue';
import CoinsCompare from 'components/coverages/utils/sync/compare/CoinsCompare.vue';
import CoinsSCompare from 'components/coverages/utils/sync/compare/CoinsSCompare.vue';

export const components = {
    'image': ImageCompare,
    'geo': GeoCompare,
    'coverageType': TypeCompare,
    'video': VideoCompare,
    'providerList': ProviderCompare,
    'networkObj': NetworkCompare,
    'documents': DocumentsCompare,
    'deductible': DeductibleCompare,
    'moop': DeductibleCompare,
    'deductibles': DeductiblesCompare,
    'moops': DeductiblesCompare,
    'premium': PremiumsCompare,
    'rates': RatesCompare,
    'coinsurance': CoinsCompare,
    'coins': CoinsSCompare,
    'copays': CoinsSCompare,
}

export const coverageDisplaySchema = {
    issuer: {label: 'Issuer'},
    org: {label: 'For Company/Plan'},
    provider: {label: 'Medical Provider (for direct care)'},
    public: {label: 'Make available publicly'},
    sim: {label: 'Use for simulations'},
    name: {label: 'Product/coverage name'},
    carrierName: {label: 'Carrier Name'},
    carrierLogo: {label: 'Carrier Logo', type: 'image'},
    webpage: {label: 'Webpage'},
    geo: {type: 'geo'},
    postTax: {label: 'After Tax'},
    openNetwork: {label: 'Open Network'},
    contract: {label: 'Contract'},
    type: {label: 'Coverage Type', type: 'coverageType'},
    covered: {label: 'Covered Party'},
    listBillDiscount: { label: 'List Bill Discount', format: (v) => `${dollarString(v, '', 2)}%` },
    av: { label: 'Actuarial Values' },
    description: {label: 'Description'},
    hsaQualified: {label: 'HSA Eligible'},
    dpc: {label: 'Is DPC'},
    ichra: {label: 'Is ICHRA'},
    shop: {label: 'Individual Shop Experience'},
    video: {label: 'Video', type: 'video'},
    adj: {
        label: 'Adjudication',
        sub: {
            autoMax: {label: 'Automate if less than', format: (v) => dollarString(v, '$', 0)},
            authProviders: {label: 'Prior auth providers', type: 'providerList'}
        }
    },
    networks: {
        label: 'Networks', type: 'networkObj'
    },
    fortyPremium: {label: 'Premium at age 40', format: (v: number) => dollarString(v, '$', 2)},
    maxAge: {label: 'Max Age'},
    documents: {label: 'Documents', type: 'documents'},
    preventive: {label: 'Preventive'},
    productDetailRef: {label: 'Product Detail Link From Carrier'},
    coinsurance: {
        label: 'Coinsurance Percent (percent the patient pays before MOOP)',
        type: 'coinsurance'
    },
    coins: {
        label: 'Coinsurance Percent Categories',
        type: 'coins'
    },
    deductible: {label: 'Deductible', type: 'deductible'},
    deductibles: {label: 'Special Deductibles', type: 'deductibles'},
    copays: {label: 'Special Copays', type: 'copays'},
    premium: {label: 'Premium', type: 'premium'},
    rates: {label: 'Area Based Rates', type: 'rates'},
    moop: {label: 'Moop', type: 'moop'},
    moops: {label: 'Special Moops', type: 'moops'},
    monthsSinceSmoked: {label: 'Months until non-smoker status'},
    disability: {
        label: 'Disabled Dependents',
        sub: {
            coverOverRequiredAge: {label: 'Cover older than legally required disabled dependents'},
            incomeLimit: {label: 'Income Limit', format: (v) => dollarString(v, '$', 0)}
        }
    }

}
