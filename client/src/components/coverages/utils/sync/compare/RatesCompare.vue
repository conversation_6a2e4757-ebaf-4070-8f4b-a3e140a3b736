<template>
  <div class="q-pa-sm font-3-4r">
    Rates by state are displayed. If area rates change down to the zip-code level, the rate changes will not be displayed here due to a nearly endless possible size of display. If you accept changes, you'll accept all zip-code level changes.
  </div>
  <div class="w500 mw100 q-pb-sm">
    <q-input dense filled v-model="search.text">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>
  </div>
  <div class="_fw">

    <div class="_fw q-pa-sm">
    <q-list separator>
      <q-item-label header>Removing Rates</q-item-label>
      <q-expansion-item expand-icon="mdi-menu-down" v-for="(r, i) in ov$.data" :key="`ov-${i}`" :label="r.state">
        <rates-table :model-value="r"></rates-table>
      </q-expansion-item>
    </q-list>
    <pagination-row v-bind="{ pAttrs: { size: 'sm' }, pagination:ovP, pageRecordCount:ovPRC, limit:ovLimit, h$:ov$}"></pagination-row>
    </div>

    <div class="row justify-center">
      <div class="q-pa-sm mw100">
        <q-icon size="25px" name="`mdi-arrow-down-bold`"></q-icon>
      </div>
    </div>

    <div class="_fw q-pa-sm">
      <q-list separator>
        <q-item-label header>Removing Rates</q-item-label>
        <q-expansion-item expand-icon="mdi-menu-down" v-for="(r, i) in nv$.data" :key="`nv-${i}`" :label="r.state">
          <rates-table :model-value="r"></rates-table>
        </q-expansion-item>
      </q-list>
      <pagination-row v-bind="{ pAttrs: { size: 'sm' }, pagination:nvP, pageRecordCount:nvPRC, limit:nvLimit, h$:nv$}"></pagination-row>
    </div>


  </div>
</template>

<script setup>


  import {computed} from 'vue';
  import {HQuery} from 'src/utils/hQuery';
  import {HFind} from 'src/utils/hFind';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';
  import RatesTable from 'components/coverages/cards/RatesTable.vue';

  const props = defineProps({
    nv: { required: true },
    ov: { required: true }
  })

  const diff = computed(() => {
    const nArr = props.nv || [];
    const oArr = props.ov || [];
    if(!oArr.length) return { nv: nArr, ov: [] };
    if(!nArr.length) return { ov: oArr, nv: [] }
    const res = { nv: [], ov: [] }
    for(let i = 0; i < nArr.length; i++) {
      const idx = oArr.indexOf(nArr[i])
      if(idx === -1) res.nv.push(nArr[i]);
    }
    for(let i = 0; i < oArr.length; i++){
      if(!nArr.includes(oArr[i])) res.ov.push(oArr[i]);
    }
    return res
  })

  const { search, searchQ } = HQuery({keys: ['state', 'stateKey'], or: true })

  const nvLimit = computed(() => Math.min(25, diff.value.nv.length))
  const { h$:nv$, pageRecordCount:nvPRC, pagination:nvP } = HFind({
    store,
    limit: nvLimit,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          _id: { $in: diff.value.nv }
        }
      }
    })
  })

  const nvById = computed(() => {
    const obj = {};
    for(let i = 0; i < nv$.data.length; i++){
      obj[nv$.data[i]._id] = nv$.data[i];
    }
    return obj
  })

  const ovLimit = computed(() => Math.min(25, diff.value.ov.length))
  const { h$:ov$, pagination:ovP, pageRecordCount:ovPRC } = HFind({
    store,
    limit:ovLimit,
    params: computed(() => {
      return {
        query: {
          ...searchQ.value,
          _id: { $in: diff.value.ov }
        }
      }
    })
  })

  const ovById = computed(() => {
    const obj = {};
    for(let i = 0; i < ov$.data.length; i++){
      obj[ov$.data[i]._id] = ov$.data[i];
    }
    return obj
  })
</script>

<style lang="scss" scoped>

</style>
