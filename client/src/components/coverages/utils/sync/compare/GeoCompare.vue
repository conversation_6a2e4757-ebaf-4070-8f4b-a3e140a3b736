<template>
  <div class="__grd _fw">
    <div class="__map">
      <template v-if="ov">
        <map-box :geo-in="ov" fit-bounds></map-box>
      </template>
      <div v-else class="_fa flex flex-center">
        <div class="font-7-8r">N/A</div>
      </div>
    </div>
    <div class="q-px-sm">
      <q-icon size="25px" name="mdi-arrow-right-bold"></q-icon>
    </div>
    <div class="__map">
      <template v-if="nv">
        <map-box :geo-in="nv" fit-bounds></map-box>
      </template>
      <div v-else class="_fa flex flex-center">
        <div class="font-7-8r">N/A</div>
      </div>
    </div>

  </div>
</template>

<script setup>

  import MapBox from 'components/common/mapbox/map/MapBox.vue';

  const props = defineProps({
    nv: { required: true },
    ov: { required: true },
  })
</script>

<style lang="scss" scoped>

  .__grd {
    display: grid;
    width: 100%;
    grid-template-columns: auto minmax(30px, 80px) auto;
  }
  @media screen and (max-width: 500px) {
    .__grd {
      grid-template-columns: 1fr;
      grid-template-rows: auto auto auto;
    }
  }
  .__map {
    width: min(200px, 40vw);
    height: min(200px, 40vw);
    position: relative;
    overflow: hidden;
    border-radius: 5px;
  }
</style>
