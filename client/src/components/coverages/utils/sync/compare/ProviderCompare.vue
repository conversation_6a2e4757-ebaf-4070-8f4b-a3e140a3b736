<template>
  <div class="w500 mw100 q-pb-sm">
    <q-input dense filled v-model="search.text">
      <template v-slot:prepend>
        <q-icon name="mdi-magnify"></q-icon>
      </template>
    </q-input>
  </div>
  <div class="row items-center">

    <div class="__list">
      <div class="q-pa-sm tw-six text-ir-deep font-3-4r">Remove Auth Providers</div>
      <div v-if="!ov$.total" class="q-pa-md font-7-8r">N/A</div>
      <q-list separator>
        <provider-item v-for="(p, i) in ov$.data" :key="`ov-${i}`" simple :model-value="p"></provider-item>
      </q-list>
      <pagination-row v-bind="{ pAttrs: { size: 'sm' }, pagination:ovP, pageRecordCount:ovPRC, limit:ovLimit, h$:ov$}"></pagination-row>

    </div>


    <div class="q-pa-sm mw100">
      <q-icon size="25px" :name="`mdi-arrow-${$q.screen.lt.md ? 'down' : 'right'}-bold`"></q-icon>
    </div>


    <div class="__list">
      <div class="q-pa-sm tw-six text-ir-deep font-3-4r">Add Auth Providers</div>
      <div v-if="!nv$.total" class="q-pa-md font-7-8r">N/A</div>
      <q-list separator>
        <provider-item v-for="(p, i) in nv$.data" :key="`nv-${i}`" simple :model-value="p"></provider-item>
      </q-list>
      <pagination-row v-bind="{ pAttrs: { size: 'sm' }, pagination:nvP, pageRecordCount:nvPRC, limit:nvLimit, h$:nv$}"></pagination-row>
    </div>

  </div>
</template>

<script setup>
  import ProviderItem from 'components/providers/cards/ProviderItem.vue';
  import PaginationRow from 'components/utils/pagination/PaginationRow.vue';

  import {HFind} from 'src/utils/hFind';
  import {useProviders} from 'stores/providers';
  import {computed} from 'vue';
  import {HQuery} from 'src/utils/hQuery';


  const providerStore = useProviders();

  const props = defineProps({
    ov: { required: true },
    nv: { required: true }
  })

  const ovEx = computed(() => {
    if (!props.nv) return props.ov || [];
    if (!props.ov) return [];
    return props.ov.filter(a => !props.nv.includes(a))
  })
  const nvEx = computed(() => {
    if (!props.ov) return props.nv || [];
    if (!props.nv) return [];
    return props.nv.filter(a => !props.ov.includes(a))
  })

  const { search, searchQ } = HQuery({})

  const ovLimit = computed(() => Math.max(ovEx.value.length, 25))
  const { h$: ov$, pageRecordCount:ovPRC, pagination:ovP } = HFind({
    store: providerStore,
    limit: ovLimit,
    params: computed(() => {
      return {
        query: { _id: { $in: ovEx }, ...searchQ.value }
      }
    })
  })

  const nvLimit = computed(() => Math.max(nvEx.value.length, 25));
  const { h$: nv$, pageRecordCount:nvPRC, pagination:nvP} = HFind({
    store: providerStore,
    limit,
    params: computed(() => {
      return {
        query: { _id: { $in: nvEx }, ...searchQ.value }
      }
    })
  })
</script>

<style lang="scss" scoped>

  .__list {
    padding: 10px;
    max-height: 300px;
    overflow-y: scroll;
    width: 350px;
    max-width: calc(50vw - 50px);
  }

  @media screen and (max-width: 1023px)  {
    .__list {
      width: 100%;
      max-width: 100%;
    }
  }
</style>
