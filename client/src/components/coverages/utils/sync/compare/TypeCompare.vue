<template>
  <div class="flex items-center">
    <type-chip v-if="ov" :model-value="ov"></type-chip>
    <div v-else class="q-pa-xs font-7-8r">N/A</div>

    <div class="q-px-sm">
      <q-icon size="25px" name="mdi-arrow-right-bold"></q-icon>
    </div>

    <type-chip v-if="nv" :model-value="nv"></type-chip>
    <div v-else class="q-pa-xs font-7-8r">N/A</div>

  </div>
</template>

<script setup>
  import TypeChip from 'components/coverages/cards/TypeChip.vue';

  const props = defineProps({
    nv: { required: true },
    ov: { required: true },
  })

</script>

<style lang="scss" scoped>

</style>
