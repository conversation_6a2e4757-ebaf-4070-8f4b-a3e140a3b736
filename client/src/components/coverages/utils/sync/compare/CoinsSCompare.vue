<template>
  <div class="_fw q-pa-md">

    <div class="__list">
      <div class="q-pa-sm tw-six text-ir-deep font-3-4r">Removing</div>
      <div v-if="!ov" class="q-pa-md text-center font-7-8r">N/A</div>
      <div v-else>
        <coins-compare v-for="(k, i) in diff.ov" :key="`ov-${i}`" :nv="nv[k]" :ov="ov[k]"></coins-compare>
      </div>
    </div>

    <div class="row justify-center">
      <div class="q-pa-sm mw100">
        <q-icon size="25px" name="mdi-arrow-down-bold"></q-icon>
      </div>
    </div>

    <div class="__list">
      <div class="q-pa-sm tw-six text-ir-deep font-3-4r">Adding/Modifying</div>

      <div v-if="!nv" class="q-pa-md text-center font-7-8r">N/A</div>
      <div v-else>
        <coins-compare v-for="(k, i) in diff.nv" :key="`nv-${i}`" :nv="nv[k]" :ov="ov[k]"></coins-compare>
      </div>
    </div>

  </div>
</template>

<script setup>
  import CoinsCompare from 'components/coverages/utils/sync/compare/CoinsCompare.vue';

  import {computed} from 'vue';

  const props = defineProps({
    nv: { required: true },
    ov: { required: true }
  })

  const diff = computed(() => {
    const nArr = Object.keys(props.nv || {});
    const oArr = Object.keys(props.ov || {});
    if (!oArr.length) return { nv: nArr, ov: [] };
    if (!nArr.length) return { ov: oArr, nv: [] }
    const res = { nv: [], ov: [] }
    for (let i = 0; i < nArr.length; i++) {
      const idx = oArr.indexOf(nArr[i])
      if (idx === -1) res.nv.push(nArr[i]);
      else {
        for (const k in props.ov[oArr[idx]]) {
          if (props.ov[oArr[idx]][k] !== props.nv[nArr[i][k]]) {
            res.nv.push(nArr[i]);
            res.ov.push(oArr[idx])
            break;
          }
        }
      }
    }
    for (let i = 0; i < oArr.length; i++) {
      if (!nArr.includes(oArr[i])) res.ov.push(oArr[i]);
    }
    return res
  })

</script>

<style lang="scss" scoped>
  .__list {
    padding: 15px;
    border: solid 1px var(--ir-mid);
    border-radius: 8px;
    max-height: 400px;
    overflow-y: scroll;
    width: 100%;
    max-width: 100%;
    font-size: 1rem;
    font-family: var(--alt-font);
  }

</style>
