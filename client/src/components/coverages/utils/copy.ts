export const pickCoverageFieldsForCopy: string[] = [
    'issuer', 'provider', 'name', 'carrierName', 'carrierLogo', 'webpage', 'geo', 'postTax', 'openNetwork', 'contract', 'type', 'covered', 'description', 'hsaQualified', 'dpc', 'ichra', 'shop', 'video', 'adj', 'networks', 'fortyPremium', 'maxAge', 'documents', 'preventive', 'coins', 'coinsurance', 'deductible', 'deductibles', 'copay', 'copays', 'premium', 'rates', 'deductibleType', 'moop', 'moops', 'monthsSinceSmoked', 'disability', 'catsWhitelist', 'catsBlacklist', 'procedureWhitelist', 'procedureBlacklist', 'plan_type', 'productDetailRef'
];
