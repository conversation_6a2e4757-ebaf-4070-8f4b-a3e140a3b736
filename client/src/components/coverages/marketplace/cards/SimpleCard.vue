<template>
  <div class="_fw relative-position">
    <div class="__logo q-mr-sm" v-if="mv?.carrier?.logo_url">
      <q-img :src="mv.carrier.logo_url" fit="contain" class="_fa"></q-img>
    </div>
    <div class="font-1r tw-six __name">
      <div>{{ mv?.carrierName }}</div>
    </div>
    <div class="font-1r tw-five __name">{{ mv.title }}
      <q-tooltip>{{ mv.name }}</q-tooltip>
    </div>
    <slot name="premium">
      <div class="row q-py-sm">
        <div class="font-1-1-4r tw-six text-a7"><span class="font-7-8r text-black">Monthly Premium: </span>
          {{ dollarString(mv.premium, '$', 0) }}
        </div>
      </div>
    </slot>

    <table>

      <thead>
      <tr>
        <td></td>
        <td>Individual</td>
        <td>Family</td>
      </tr>
      </thead>
      <tbody>
      <tr>
        <td>Deductible</td>
        <td>{{ dollarString(getDeductible(mv, 'single'), '$', 0) }}</td>
        <td>{{ dollarString(getDeductible(mv, 'family') || 'N/A', '$', 0) }}</td>
      </tr>
      <tr>
        <td>MOOP</td>

        <td>{{ dollarString(getMoop(mv, 'single'), '$', 0) }}</td>
        <td>{{ dollarString(getMoop(mv, 'family') || 'N/A', '$', 0) }}</td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>

  import {computed} from 'vue';
  import {dollarString} from 'src/utils/global-methods';
  import {getDeductible, getMoop} from 'src/components/enrollments/ichra/utils';

  const props = defineProps({
    modelValue: Object
  })

  const mv = computed(() => props.modelValue || {});

</script>

<style lang="scss" scoped>

  .__logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50%;
    opacity: .2;
  }
  .__name {
    display: grid;
    grid-template-columns: auto 1fr;
    width: 100%;
    align-items: center;

    div {
      &:last-child {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: .8rem;
        font-weight: 500;
      }
    }

  }

  table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;

    thead {
      tr {
        td {
          font-weight: 600;
          font-size: .8rem;
          color: var(--q-ir-grey-7);
        }
      }
    }

    tr {
      td {
        padding: 5px 8px;
        border-bottom: solid .2px #c5c5c5;
        font-size: .8rem;
      }
    }

    tr:last-child {
      td {
        border-bottom: none;
        font-size: .8rem;
      }
    }
  }
</style>
