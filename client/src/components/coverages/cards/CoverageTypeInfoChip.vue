<template>
  <q-chip v-bind="{ color: 'ir-bg2', clickable: true, ...$attrs }" @click="dialog = !dialog">
    <slot name="default">
      <q-icon name="mdi-information" class="q-mr-sm" color="accent"></q-icon>
      <span>Coverage Types</span>
    </slot>
    <common-dialog setting="smmd" v-model="dialog">
      <div class="_fw q-pa-sm">
        <coverage-type-info></coverage-type-info>
      </div>
    </common-dialog>
  </q-chip>
</template>

<script setup>

import CoverageTypeInfo from 'components/coverages/cards/CoverageTypeInfo.vue';
import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
import {ref} from 'vue';

const dialog = ref(false);

</script>

<style lang="scss" scoped>

</style>
