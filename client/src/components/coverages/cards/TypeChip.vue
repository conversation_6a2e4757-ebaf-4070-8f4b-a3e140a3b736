<template>
  <q-chip v-bind="{ dark: true, class: 'tw-six', color: typeColors[modelValue] || 'ir-grey-7', label: types[modelValue] || def, clickable: true, ...$attrs}" @click="dialog = true">
    <common-dialog setting="smmd" v-model="dialog">
      <div class="_fw q-pa-sm">
        <coverage-type-info :def-open="modelValue"></coverage-type-info>
      </div>
    </common-dialog>
  </q-chip>
</template>

<script setup>
  import { types, typeColors } from '../utils/types';
  import CommonDialog from 'components/common/dialogs/CommonDialog.vue';
  import {ref} from 'vue';
  import CoverageTypeInfo from 'components/coverages/cards/CoverageTypeInfo.vue';
  const props = defineProps({
    modelValue: String,
    def: { default: 'Unspecified' }
  })
  const dialog = ref(false)

</script>

<style lang="scss" scoped>

</style>
