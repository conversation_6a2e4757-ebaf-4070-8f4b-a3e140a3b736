<template>
  <q-expansion-item group="ded_form">
    <template v-slot:header>
      <limit-item :model-value="form"></limit-item>
    </template>
    <limit-form
        deductible
        v-model="form"
        @update:model-value="emitUp"
    ></limit-form>
    <div class="q-pa-sm">
      <q-radio
          v-model="form.type"
          val="annual"
          label="Per Plan Year"
          @update:model-value="emitUp(form, 'type')"
      ></q-radio>
      <q-radio
          v-model="form.type"
          val="event"
          label="Per Event"
          @update:model-value="emitUp(form, 'type')"
      ></q-radio>
    </div>
  </q-expansion-item>
</template>

<script setup>

  import LimitItem from 'components/plans/cards/LimitItem.vue';
  import LimitForm from 'components/plans/forms/premiums/LimitForm.vue';
  import {ref, watch} from 'vue';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: Object
  })

  const formFn = (defs) => {
    return {
      ...defs
    }
  }
  const form = ref(formFn())

  const emitUp = (v, path) => {
    setTimeout(() => {
      emit('update:model-value', form.value, path);
    }, 50)
  }

  watch(() => props.modelValue, (nv) => {
    if (nv) form.value = formFn({ ...form.value, ...nv })
  }, { immediate: true })
</script>

<style lang="scss" scoped>

</style>
