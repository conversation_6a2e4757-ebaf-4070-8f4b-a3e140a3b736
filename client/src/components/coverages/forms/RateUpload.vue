<template>
  <div class="_fw">
    <q-tabs content-class="__c" indicator-color="p3" no-caps v-model="panel" align="left">
      <q-tab label="Enter Rates" name="enter"></q-tab>
      <q-tab label="Upload Rates" name="upload"></q-tab>
    </q-tabs>
    <q-tab-panels animated v-model="panel">
      <q-tab-panel name="upload">
        <div class="q-pa-md font-1r">Rates for single coverage by age</div>
        <csv-upload
            headers="age,rate"
            :response="response"
            :required="requiredFields"
            @ready="uploadList"
            @clear="response = {}"
            :example-data="exampleData"
        ></csv-upload>
      </q-tab-panel>
      <q-tab-panel name="enter">
        <rate-table
            v-model="form"
            @update:model-value="emitUp"
        ></rate-table>
      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import RateTable from 'components/coverages/forms/RateTable.vue';
  import CsvUpload from 'components/common/uploads/csv/CsvUpload.vue';

  import {axiosFeathers, restCore} from 'components/common/uploads/services/utils';
  import {computed, ref, watch} from 'vue';

  const emit = defineEmits(['update:model-value']);
  const props = defineProps({
    modelValue: { required: true }
  })
  const response = ref({});
  const panel = ref('enter');
  const requiredFields = ref([
    {
      field: 'age',
      v: ['notEmpty'],
      required: true
    },
    {
      field: 'rate',
      v: ['notEmpty'],
      required: true
    }
  ]);

  const formFn = () => {
    const obj = {};
    for(let i = 0; i < 121; i++){
      obj[i] = 0
    }
    return obj;
  };
  const form = ref(formFn());

  const emitUp = () => {
    emit('update:model-value', form.value);
  }
  const uploadList = async (val, meta) => {
    const res = await axiosFeathers().patch(`/coverages/${props.modelValue._id}`, val, {
      params: {
        runJoin: { addCoverage: meta },
        core: restCore()
      }
    });
    response.value = res.data?._fastjoin?.addMembers;
    form.value = { ...form.value, ...response.value?.added };
    emitUp();
  };

  const exampleData = computed(() => {
    return [
      {
        header: 'Age',
        required: true,
        ex: '35'
      },
      {
        header: 'Single Rate',
        required: true,
        ex: '365',
      }
    ]
  })

  watch(() => props.modelValue, (nv) => {
    if(nv) form.value = { ...nv };
  }, { immediate: true })
</script>

<style lang="scss" scoped>
  .__c {
    background: linear-gradient(12deg, var(--q-p0), white, var(--q-p0));
  }
</style>
