<template>
  <div class="_fw">
    <q-table
        :rows="p$.data"
        flat
        :rows-per-page-options="[0]"
        :columns="cols"
        hide-no-data
        hide-bottom
        hide-pagination
    >
      <template v-slot:header="scope">
        <q-th
            v-for="col in scope.cols"
            :key="col.name"
            :props="scope"
        >
          {{ col.label }}
        </q-th>
      </template>
      <template v-slot:body="scope">
        <q-tr :props="scope" @dblclick="openItem(scope.row)">
          <q-td
              v-for="(col, i) in scope.cols"
              :key="`td-${i}`">
            <component
                v-if="col?.component"
                :is="col.component"
                v-bind="col.attrs(scope.row, col)"
                v-on="col.listeners ? col.listeners(scope.row) : {}"
            ></component>
            <div v-else>{{ col.value }}</div>
          </q-td>
        </q-tr>
      </template>
    </q-table>
  </div>
</template>

<script setup>
  import TdText from 'components/common/tables/TdText.vue';
  import MemberChip from 'components/households/cards/MemberChip.vue';
  import ProviderChip from 'components/providers/cards/ProviderChip.vue';
  import PaymentAmount from 'components/claims/payments/cards/PaymentAmount.vue';

  import {computed, ref} from 'vue';
  import {useClaimPayments} from 'stores/claimPayments';
  import {HFind} from 'src/utils/hFind';
  import {usePlans} from 'stores/plans';
  import {$limitStr} from 'src/utils/global-methods';
  import {usePpls} from 'stores/ppls';
  import {formatDate} from 'src/utils/date-utils';

  const paymentStore = useClaimPayments()
  const planStore = usePlans();
  const pplStore = usePpls();

  const props = defineProps({
    params: Object
  })

  const p = computed(() => {
    return {
      ...props.params,
      query: {
        ...props.params?.query
      }
    }
  })

  const { h$: p$ } = HFind({
    store: paymentStore,
    limit: ref(10),
    params: p
  })
  const ids = computed(() => {
    const obj = { plan: [], patients: [], people: [] }
    for (let i = 0; i < p$.data.length; i++) {
      const { plan, patient, person } = p$.data[i];
      if (plan) obj.plans.push(plan)
      if (patient) obj.patients.push(patient)
      if (person) obj.people.push(person)
    }
    return obj
  })
  const { h$: plans } = HFind({
    store: planStore,
    limit: computed(() => ids.value.plan.length),
    params: computed(() => {
      return {
        query: {
          _id: { $in: ids.value.plan }
        }
      }
    })
  })
  const { h$: ppl } = HFind({
    store: pplStore,
    limit: computed(() => [...ids.value.people, ...ids.value.patients].length),
    params: computed(() => {
      return {
        _id: { $in: [...ids.value.people, ...ids.value.patients] }
      }
    })
  })


  const cols = computed(() => {
    return [
      {
        label: 'Date',
        component: TdText,
        attrs: (row) => {
          return {
            col: { value: formatDate(row.createdAt, 'MM-DD-YYYY') }
          }
        }
      },
      {
        label: 'Amount',
        component: PaymentAmount,
        attrs: (row) => {
          return {
            modelValue: row
          }
        }
      },
      {
        label: 'Plan',
        component: TdText,
        attrs: (row) => {
          const plan = planStore.getFromStore(row.plan)
          return {
            col: { value: $limitStr(plan.value?.name, 40, '...') }
          }
        }
      },
      {
        label: 'Household',
        component: MemberChip,
        attrs: (row) => {
          const person = row.person ? pplStore.getFromStore(row.person) : row.person
          return {
            color: 'transparent',
            modelValue: person
          }
        }
      },
      {
        label: 'Patient',
        component: MemberChip,
        attrs: (row) => {
          const patient = row.person ? pplStore.getFromStore(row.patient) : row.patient
          return {
            color: 'transparent',
            modelValue: patient
          }
        }
      },
      {
        label: 'Provider',
        component: ProviderChip,
        attrs: (row) => {
          return {
            color: 'transparent',
            modelValue: row.provider
          }
        }
      }
    ]
        .map(a => {
      return {
        ...a,
        name: a.name || a.label,
        sortable: true,
        align: 'left'
      };
    });
  })

  const openItem = (row) => {
    console.log('open item', row);
  }
</script>

<style lang="scss" scoped>

</style>
