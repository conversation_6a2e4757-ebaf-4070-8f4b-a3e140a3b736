<template>
  <div v-bind="{class: '__pm alt-font', ...$attrs}">
    <span>{{ dollarString(lines.total, '$', 2) }}</span>
    <q-tooltip>
      <q-table>
        <tr class="font-3-4r text-grey-3 tw-six">
          <td>Type</td>
          <td>Amount</td>
          <td>On</td>
          <td>Status</td>
        </tr>
        <tr>
          <td>Payment</td>
          <td>{{ dollarString(lines.amount, '$', 2) }}</td>
          <td>{{ formatDate(cp.createdAt, 'MM-DD-YYYY') }}</td>
          <td>
            <cp-status size="sm" :model-value="cp.status"></cp-status>
          </td>
        </tr>
        <template v-if="cp.refunds?.length">
          <tr v-for="(rf, i) in cp.refunds" :key="`rf-${i}`">
            <td>Refund</td>
            <td>{{ dollarString(rf.amount, '$', 2) }}</td>
            <td>{{ formatDate(rf.refundedAt, 'MM-DD-YYYY') }}</td>
            <td>
              <cp-status size="sm" :model-value="rf.status"></cp-status>
            </td>
          </tr>
        </template>
      </q-table>
    </q-tooltip>
  </div>
</template>

<script setup>

  import {dollarString} from 'src/utils/global-methods';
  import {computed} from 'vue';
  import {formatDate} from 'src/utils/date-utils';
  import CpStatus from 'components/claims/payments/cards/CpStatus.vue';

  const props = defineProps({
    modelValue: { required: true }
  })

  const cp = computed(() => props.modelValue || {});
  const lines = computed(() => {
    const { amount, refunds = [] } = props.modelValue || {};
    let refunded = 0;
    for (let i = 0; i < refunds.length; i++) {
      if (refunds[i].status === 'complete') {
        refunded += refunds[i].amount || 0;
      }
    }
    return {
      amount, refunded, total: amount - refunded
    }
  })
</script>

<style lang="scss" scoped>
  .__pm {
    font-weight: 600;
    font-size: 1rem;
    color: #999;
  }

  table {
    border-collapse: collapse;
    font-size: .85rem;
    color: white;

    td {
      padding: 5px 8px;
      border-bottom: solid .2px #999;
    }

    tr:last-child {
      td {
        border-bottom: none;
      }
    }
  }
</style>
