<template>
  <div class="row items-center">
    <div class="col-12">
      <div class="row items-center">
        <div class="col-6 q-px-sm">
          <q-input v-model="form.firstName" filled label="First Name" autogrow></q-input>

        </div>
        <div class="col-6 q-px-sm">
          <q-input v-model="form.lastName" filled label="Last Name" autogrow></q-input>

        </div>

      </div>
      <div class="font-3-4r q-py-sm">Enter at least one contact method</div>
      <email-field class="q-mb-sm" hide-bottom-space filled v-model="form.email"></email-field>
      <phone-input :input-attrs="{ filled: true, hideBottomSpace: true }" emit-value v-model="form.phone"></phone-input>
      <div class="font-3-4r q-py-sm flex items-center">
        <div>Enter</div>
        &nbsp; <q-chip size="sm" outline icon="mdi-lock" label="Secure Fields" color="ir-blue-10"></q-chip> &nbsp;
        <div>{{form?._id ? '(optional - can be added later)' : ''}}</div>
      </div>
      <dob-input hide-bottom-space class="q-mb-sm" v-model="form.dob" filled></dob-input>
      <ssn-input hide-bottom-space filled v-model="form.ssn"></ssn-input>
    </div>
    <div class="q-py-md row justify-end">
      <q-btn push glossy color="primary" icon="mdi-content-save" label="Save" no-caps @click="save"></q-btn>
    </div>
  </div>
</template>

<script setup>
  import EmailField from 'src/components/common/input/EmailField.vue';
  import DobInput from 'src/components/common/input/DobInput.vue';
  import SsnInput from 'src/components/common/input/SsnInput.vue';

  import {computed, ref, watch} from 'vue';
  import PhoneInput from 'src/components/common/phone/PhoneInput.vue';
  import {HForm} from 'src/utils/hForm';

  import { usePpls } from 'stores/ppls';
  const store = usePpls();

  const props = defineProps({
    modelValue: Object,
    groupId: { required: true }
  })

  const active = ref('email');

  const formFn = (val) => {
    return {
      email: '',
      name: '',
      dob: undefined,
      ssn: undefined,
      ...val
    }
  }

  const { form, save } = HForm({
    store,
    value: computed(() => props.modelValue),
    formFn,
    params: computed(() => {
      if(props.groupId) return { runJoin: { groupId: props.groupId }};
      return {}
    }),
    // beforeFn: (f) => {
    //   if(props.groupId) f.groupId = props.groupId;
    //   return f;
    // }
  })

  const mv = computed(() => props.modelValue);
  watch(mv, (nv, ov) => {
    if (nv && (!ov || JSON.stringify(nv) !== JSON.stringify(ov))) {
      form.value = formFn(nv)
    }
  }, { immediate: true })

</script>

<style lang="scss" scoped>

</style>
