<template>
  <div class="_fw">
    <div class="row justify-center">
      <div class="_cent">
        <div class="row">
          <div class="col-12 col-md-6 q-pa-sm">

            <div class="__c">
              <div class="__title">
                <div>Your Job(s) - {{ person?.cams?.length || 0 }}</div>
              </div>
              <cam-card :model-value="active" :options="h$.data"></cam-card>
            </div>
          </div>
          <div class="col-12 col-md-6 q-pa-sm">

            <div class="__c">
              <div class="__title">
                <div>Household Income</div>
              </div>
              <household-income :person="person"></household-income>
            </div>
          </div>
          <div class="col-12 col-md-6 q-pa-sm">
            <div class="__c">
              <div class="__title">
                <div>Household Members</div>
              </div>
              <household-members :person="person"></household-members>
            </div>
          </div>
          <div class="col-12 col-md-6 q-pa-sm">
            <div class="__c">
              <div class="__title">
                <div>Tax</div>
              </div>
              <div class="row cursor-pointer" v-if="plan?._id">
                <table>
                  <tbody>
                  <tr>
                    <q-td>
                      <q-checkbox v-model="withPlan"></q-checkbox>
                    </q-td>
                    <td>Show with</td>
                    <td><b>{{ plan?.name }}</b>
                      <q-menu>
                        <q-list separator class="bg-white w300 mw100">
                          <q-item-label header>Choose Enrollment Version</q-item-label>
                          <q-item clickable @click="selectedEnrollment=er" v-for="(er, i) in e$.data" :key="`er-${i}`">
                            <q-item-section>
                              <q-item-label>{{ er.planYear }} - {{ er.version.split('_')[1] }}</q-item-label>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </td>
                    <td>
                      <q-icon size="20px" color="p6" name="mdi-menu-down"></q-icon>
                    </td>

                  </tr>
                  </tbody>
                </table>

              </div>
              <household-tax
                  :plan="withPlan ? plan : undefined"
                  :enrollment="withPlan ? e$.data[0] : undefined"
                  :person="person"></household-tax>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup>
  import CamCard from 'components/comps/cams/cards/CamCard.vue';
  import HouseholdIncome from 'components/households/cards/HouseholdIncome.vue';
  import HouseholdMembers from 'components/households/cards/HouseholdMembers.vue';
  import HouseholdTax from 'components/households/cards/HouseholdTax.vue';

  import {useCams} from 'stores/cams';
  import {computed, ref} from 'vue';
  import {loginPerson} from 'stores/utils/login';
  import {HFind} from 'src/utils/hFind';
  import {getCurrentPlanYear} from 'components/plans/utils';
  import {useEnrollments} from 'stores/enrollments';
  import {usePlans} from 'stores/plans';
  import {idGet} from 'src/utils/id-get';

  const { person, login } = loginPerson()
  const store = useCams();
  const eStore = useEnrollments();
  const planStore = usePlans();

  const props = defineProps({
    org: { required: true },
  })

  const withPlan = ref(true);

  const params = computed(() => {
    return {
      query: {
        org: props.org?._id || props.org,
        person: login.value.owner
      }
    }
  });

  const idx = ref(0);
  const active = computed(() => (h$.data || [])[idx.value]);
  const { h$ } = HFind({
    store,
    params
  })

  const { h$: e$ } = HFind({
    store: eStore,
    limit: ref(5),
    pause: computed(() => !h$.data?.length),
    params: computed(() => {
      const yr = getCurrentPlanYear();
      return {
        query: {
          $sort: { version: -1 },
          planYear: { $in: [String(yr), String(yr - 1), String(yr + 1)] },
          status: 'complete',
          org: props.org?._id || props.org,
          cams: { $in: h$.data.map(a => a._id) }
        }
      }
    })
  })

  const selectedEnrollment = ref(undefined);
  const { item: plan } = idGet({
    store: planStore,
    value: computed(() => selectedEnrollment.value?.plan || e$.data[0]?.plan)
  })

</script>

<style lang="scss" scoped>
  .__cp {
    margin-top: 30px;
  }

  .__c {
    margin-top: 30px;
    padding: 4vh 3vw;
    border-radius: 6px;
    background: white;
    box-shadow: 0 2px 7px rgba(0, 0, 0, .1);
    position: relative;
    height: 100%;
  }

  .__title {
    height: 30px;
    width: 100%;

    div {
      position: absolute;
      height: 50px;
      top: -20px;
      left: 2.5%;
      width: 95%;
      border-radius: 10px;
      display: grid;
      align-items: center;
      padding: 0 25px;
      color: white;
      font-weight: 600;
      font-size: 1.24rem;
      background: linear-gradient(140deg, var(--q-p7), var(--q-primary), var(--q-p7));
    }
  }

  table {
    transform: translate(0, -10px);

    td {
      padding: 5px 10px;
      font-size: .8rem;
    }
  }
</style>
