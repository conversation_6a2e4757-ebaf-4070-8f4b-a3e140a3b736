<template>
  <div class="_fw">
    <div class="_fw q-py-sm row items-center">
      <q-chip @click="tab = 'settings'" dense square clickable color="transparent"
              :class="tab === 'settings' ? ' tw-six' : ''">
        <q-icon class="q-mr-sm" color="primary" name="mdi-cog"></q-icon>
        <span>Settings</span>
      </q-chip>
      <div>|</div>
      <q-chip @click="tab = 'list'" dense square clickable color="transparent" :class="tab === 'list' ? ' tw-six' : ''">
        <q-icon class="q-mr-sm" color="primary" name="mdi-account-group"></q-icon>
        <span>Prospects</span>
      </q-chip>
    </div>
    <q-tab-panels class="_panel" v-model="tab" animated>
      <q-tab-panel class="_panel" name="settings">

        <div class="_f_l _f_chip">Access</div>
        <div class="q-pa-sm">
          <q-radio :model-value="comp.access" val="public" label="Public"
                   @update:model-value="toggleAccess($event)"></q-radio>
          <q-radio :model-value="comp.access" val="private" label="Private"
                   @update:model-value="toggleAccess($event)"></q-radio>
        </div>

        <div class="_f_l _f_chip">Prospect Stages</div>
        <div class="q-pa-xs font-7-8r">Add stages for recruiting prospects to this comp package</div>

        <div class="q-py-sm _fw row items-center">
          <q-chip color="ir-bg2" clickable @click="addingStage = true">
            <template v-if="addingStage">
              <input placeholder="Name Status" @input="stageAdd.label = $event.target.value"
                     :style="{ width: `${$max(8, stageAdd.label.length)}ch`}">
              <q-avatar class="q-ml-sm" size="20px" :color="stageAdd.color">
                <q-popup-proxy v-model="stageColorPicker">
                  <div class="w500 mw100">
                    <color-name-picker v-model="stageAdd.color"></color-name-picker>
                  </div>
                </q-popup-proxy>
              </q-avatar>
            </template>
            <template v-else>
              <span class="q-mr-sm">Add Status</span>
              <q-icon name="mdi-plus" color="primary"></q-icon>
            </template>
          </q-chip>
          <q-btn dense flat size="sm" color="primary" icon="mdi-plus" v-if="addingStage" @click="addStage"></q-btn>
          <stage-chip v-for="(k, i) in Object.keys(comp.stages || {})" :key="`k-${i}`" :comp="comp" :model-value="k">
            <template v-slot:side>
              <remove-proxy size="xs" class="q-ml-sm" dense flat icon="mdi-close" color="red"
                            remove-label="Remove Stage? <br>Any prospects sorted by this stage will no longer be searchable by stage"
                            @remove="removeStage(k)"></remove-proxy>
            </template>
          </stage-chip>
        </div>

        <div class="_f_l _f_chip">Invite Prospects</div>
        <div class="q-pa-sm">



        </div>
      </q-tab-panel>
      <q-tab-panel class="_panel" name="list">

        <org-prospects :comp-id="comp._id" :or-g="comp.org"></org-prospects>

      </q-tab-panel>
    </q-tab-panels>

  </div>
</template>

<script setup>
  import ColorNamePicker from 'components/common/colors/ColorNamePicker.vue';
  import RemoveProxy from 'components/common/buttons/RemoveProxy.vue';
  import StageChip from 'components/comps/utils/StageChip.vue';

  import {computed, ref} from 'vue';
  import {$errNotify, $max} from 'src/utils/global-methods';
  import {useComps} from 'stores/comps';
  import {idGet} from 'src/utils/id-get';
  import OrgProspects from 'components/comps/cams/prospects/OrgProspects.vue';

  const compStore = useComps()

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: comp } = idGet({
    store: compStore,
    value: computed(() => props.modelValue)
  })

  const accessTo = ref()
  const toggleAccess = (v) => {
    if (v !== comp.value.access) {
      compStore.patchInStore(comp.value._id, { access: v })
      clearTimeout(accessTo.value)
      accessTo.value = setTimeout(() => {
        compStore.patch(comp.value._id, { $set: { access: v } })
      }, 3000)
    }
  }

  const tab = ref('settings')

  const addingStage = ref(false);
  const stageColorPicker = ref(false);
  const newStage = () => {
    return {
      label: '',
      color: 'ir-blue'
    }
  }
  const stageAdd = ref(newStage());
  const addStage = async () => {
    if (!stageAdd.value.label || !stageAdd.value.color) return $errNotify('Must have name and color')
    const key = stageAdd.value.label.split(' ').join('_').toLowerCase()
    const obj = { ...comp.value.stages, [key]: stageAdd.value };
    compStore.patchInStore(comp.value._id, { stages: obj })
    const updated = await compStore.patch(comp.value._id, { $set: { [`stages.${key}`]: stageAdd.value } })
    if (updated) {
      stageAdd.value = newStage()
      addingStage.value = false;
    }
  }
  const removeStage = (k) => {
    const obj = { ...comp.value.stages }
    delete obj[k];
    compStore.patchInStore(comp.value._id, { stages: obj })
    compStore.patch(comp.value._id, { $unset: { [`stages.${k}`]: '' } })
  }
</script>

<style lang="scss" scoped>
  input {
    border: none;
    width: 8ch;
    box-sizing: content-box;
    padding: 0px;
    background-color: transparent;

    &:focus {
      border: none;
      outline: none;
    }
  }
</style>
