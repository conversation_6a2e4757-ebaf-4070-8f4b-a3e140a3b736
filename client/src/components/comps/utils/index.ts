import {computed} from 'vue';
import {AnyObj} from 'src/utils/types';
import {$pluralExtension, dollarString} from 'src/utils/global-methods';
export * from './dual-store';
export * from './income';
import { Comp, IvKey, iv } from './income';


export const intervals = computed(() => {
    return iv
})

export const getUnit = (key:IvKey):string => {
    if(iv[key]) return iv[key].unit;
    else return '';
}
export const getLabel = (key:IvKey):string => {
    if(iv[key]) return iv[key].label;
    else return '';
}
export const getAbbr = (key:IvKey):string => {
    if(iv[key]) return iv[key].abbr;
    else return '';
}
export const getTimeFactor = (comp:Comp) => {
    if(['hour', 'day', 'week'].includes(comp.interval)) return (comp.estHours || 40) / 40;
    else return 1;
}
export const adjust_amount = (comp:Comp, desiredInterval?:IvKey) => {
    const { amount = 0, interval = 'hour' } = comp || { amount: 0 };
    const {index, factor} = iv[interval];
    const {index:dIndex, factor:dFactor} = iv[desiredInterval||interval];
    if(index === dIndex) return amount;
    if(index < dIndex) return (amount / factor) * dFactor
    else return (amount) / ((factor/dFactor) * getTimeFactor(comp));
}

const types = {
    'percent': { label: 'Percent' },
    'flat': { label: 'Flat' },
    'units': { label: 'Units' }
};
export const extraTypes = computed(() => {
    return types
})

export const getTypeLabel = (key:keyof typeof types):string => {
    if(types[key]) return types[key].label;
    else return '';
}

type Extra = { type?: string, amount: number, unit?: string, interval?:string } & AnyObj;
export const getExtraSymbols = (extra:Extra) => {
    if(!extra) return { prefix: '', suffix: ''};
    const {type, amount, unit} = extra;
    if(!type) return { prefix: '$', suffix: ` - ${getAbbr(extra.interval as any)}` }
    if(type === 'flat') return { prefix: '$', suffix: ''}
    if(type === 'percent') return { prefix: '', suffix: '%' }
    else return { prefix: '', suffix: ` ${$pluralExtension(unit || 'Unit', amount)}`}
}

type ExtraAmountOptions = {
    display?:boolean
};
export const extraAmount = (extra:Extra, { display = true }:ExtraAmountOptions = {}) => {
    if(!extra) return 0;
    const { prefix, suffix } = getExtraSymbols(extra);
    const amt = extra?.amount || 0
    return display ? `${dollarString(amt, prefix, amt < 500 ? 2 : 0)}${suffix}` : amt;
}

type Options = {
    interval?:IvKey,
    prefix?:string,
    decimal?:number,
    full?:boolean
}

export const fullDisplay = (comp:Comp, { interval, prefix = '', decimal = 0, full }:Options = {}):string => {
    if(!decimal || decimal === 0) decimal = comp?.amount > 999 ? 0 : 2;
    return `${dollarString(adjust_amount(comp, interval), prefix, decimal)}${full ? ` / ${getAbbr(interval || comp?.interval)}` : '' }`
}

