<template>
  <q-chip
      v-bind="{
    label,
    iconRight: 'mdi-menu-down',
    color: 'ir-bg2',
    class: 'bg-ir-bg2',
        ...$attrs
      }"
  >
    <q-popup-proxy>
      <div class="w300 mw100 bg-white q-pa-md">
        <q-input dense filled v-model="search" @update:model-value="emit('input-value', $event)">
          <template v-slot:prepend>
            <q-icon name="mdi-magnify"></q-icon>
          </template>
        </q-input>
        <q-list separator>
          <q-item v-for="(opt, i) in useOpts" :key="`opt-${i}`" clickable @click="select(opt)">
            <q-item-section>
              <q-item-label class="tw-six">{{opt.name}}</q-item-label>
              <q-item-label caption>{{fullDisplay(opt, { full: true })}}</q-item-label>
            </q-item-section>
            <q-item-section side v-if="isSelected(opt)">
              <q-icon color="green" name="mdi-check-circle-outline"></q-icon>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </q-popup-proxy>

  </q-chip>
</template>

<script setup>

  import {computed, ref} from 'vue';
  import {fullDisplay} from 'src/components/comps/utils';

  const emit = defineEmits(['update:model-value', 'input-value']);
  const props = defineProps({
    modelValue: { required: true },
    multiple: Boolean,
    options: Array
  })

  const label = computed(() => {
    if(!props.modelValue) return 'Choose Comp Package'
    else {
      if(Array.isArray(props.modelValue)){
        const l = props.modelValue.length;
        if(l === 1) return props.modelValue[0].name
        else return l ? `${l} Selected` : 'Choose Comp Package'
      } else return props.modelValue.name
    }
  })

  const search = ref('');
  const useOpts = computed(() => {
    return (props.options || []).filter(a => a.name?.toLowerCase().includes(search.value.toLowerCase()))
  })

  const select = (val) => {
    if(props.multiple){
      if(!props.modelValue) emit('update:model-value', [val])
      else {
        const list = [...props.modelValue];
        const idx = list.map(a => a._id).indexOf(val._id);
        if(idx > -1){
          list.splice(idx, 1);
          emit('update:model-value', list)
        } else emit('update:model-value', [...list, val])
      }
    }
  }

  const isSelected = (opt) => {
    if(!props.modelValue) return false;
    else{
      if(Array.isArray(props.modelValue)) return props.modelValue.map(a => a._id).includes(opt._id);
      else return props.modelValue._id === opt._id;
    }
  }
</script>

<style lang="scss" scoped>

</style>
