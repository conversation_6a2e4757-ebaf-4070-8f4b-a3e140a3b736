<template>
  <div class="_fw">
    <q-list separator>
      <q-item-label header v-if="!modelValue">No Extras</q-item-label>
      <template v-else>
        <q-expansion-item expand-icon="mdi-menu-down" group="a" v-for="(k, i) in Object.keys(modelValue)"
                          :key="`k-${i}`">
          <template v-slot:header>
            <q-item class="_fw">
              <q-item-section>
                <q-item-label>{{ k.split('_').join(' ') }}</q-item-label>
              </q-item-section>
            </q-item>
          </template>
          <extra-card :model-value="modelValue[k]"></extra-card>
        </q-expansion-item>
      </template>

    </q-list>
  </div>
</template>

<script setup>

  import ExtraCard from 'src/components/comps/cards/ExtraCard.vue';

  const props = defineProps({
    modelValue: { required: true }
  })

</script>

<style lang="scss" scoped>


</style>
