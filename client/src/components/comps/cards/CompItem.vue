<template>
  <q-item v-bind="{ class: '_fw', ...$attrs }">
    <q-item-section>
      <q-item-label class="font-3-4r tw-six">{{ modelValue.name }}</q-item-label>
      <q-item-label caption>
        {{extraAmount(modelValue)}}
      </q-item-label>
    </q-item-section>
  </q-item>
</template>

<script setup>
  import {extraAmount} from '../utils';

  const props = defineProps({
    modelValue: { type: Object, required: true }
  })
</script>

<style lang="scss" scoped>

</style>
