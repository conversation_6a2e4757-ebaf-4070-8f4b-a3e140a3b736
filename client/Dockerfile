# build stage
FROM node:lts-alpine as client-stage
ARG APP_BASEPATH
ENV APP_BASEPATH=${APP_BASEPATH:-/app}

ARG VUE_APP_FEATHERS_URL
ENV VUE_APP_FEATHERS_URL=${VUE_APP_FEATHERS_URL}

RUN echo ${VUE_APP_FEATHERS_URL}

WORKDIR /app
COPY . .
RUN rm -rf node_modules package-lock.json server; npm install -g npm@latest; npm -v; npm install; npm audit fix; npm install -g @quasar/cli @vue/cli

RUN export QENV=production;

RUN npm run build;


FROM caddy:latest
COPY --from=client-stage /app/dist/spa /srv/
ADD Caddyfile /etc/caddy/Caddyfile
EXPOSE 80 443 2019

